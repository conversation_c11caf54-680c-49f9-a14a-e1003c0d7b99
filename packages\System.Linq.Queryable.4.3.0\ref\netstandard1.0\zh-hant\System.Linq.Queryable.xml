﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Queryable</name>
  </assembly>
  <members>
    <member name="T:System.Linq.EnumerableExecutor">
      <summary>表示運算式樹狀架構，並且提供在重新撰寫後執行運算式樹狀架構的功能。</summary>
    </member>
    <member name="M:System.Linq.EnumerableExecutor.#ctor">
      <summary>初始化 <see cref="T:System.Linq.EnumerableExecutor" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Linq.EnumerableExecutor`1">
      <summary>表示運算式樹狀架構，並且提供在重新撰寫後執行運算式樹狀架構的功能。</summary>
      <typeparam name="T">執行運算式樹狀架構所產生之值的資料型別。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableExecutor`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>初始化 <see cref="T:System.Linq.EnumerableExecutor`1" /> 類別的新執行個體。</summary>
      <param name="expression">與新執行個體關聯的運算式樹狀架構。</param>
    </member>
    <member name="T:System.Linq.EnumerableQuery">
      <summary>表示做為 <see cref="T:System.Linq.EnumerableQuery" /> 資料來源的 <see cref="T:System.Collections.IEnumerable" />。</summary>
    </member>
    <member name="M:System.Linq.EnumerableQuery.#ctor">
      <summary>初始化 <see cref="T:System.Linq.EnumerableQuery" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:System.Linq.EnumerableQuery`1">
      <summary>表示做為 <see cref="T:System.Linq.IQueryable`1" /> 資料來源的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合。</summary>
      <typeparam name="T">集合中的資料型別。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Linq.EnumerableQuery`1" /> 類別的新執行個體，並使其與 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合產生關聯。</summary>
      <param name="enumerable">與新執行個體關聯的集合。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>初始化 <see cref="T:System.Linq.EnumerableQuery`1" /> 類別的新執行個體，並使其與運算式樹狀架構產生關聯。</summary>
      <param name="expression">與新執行個體關聯的運算式樹狀架構。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>傳回可以逐一查看關聯之 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合的列舉值，或者如果為 null，則逐一查看重新寫入運算式樹狀架構所產生的集合，做為 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 資料來源上的查詢並且執行。</summary>
      <returns>可以用來逐一查看關聯之資料來源的列舉值。</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回可以逐一查看關聯之 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合的列舉值，或者如果為 null，則逐一查看重新寫入運算式樹狀架構所產生的集合，做為 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 資料來源上的查詢並且執行。</summary>
      <returns>可以用來逐一查看關聯之資料來源的列舉值。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#ElementType">
      <summary>取得此執行個體所代表之集合中的資料型別。</summary>
      <returns>此執行個體所代表之集合中的資料型別。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Expression">
      <summary>取得與此執行個體關聯的或代表此執行個體的運算式樹狀架構。</summary>
      <returns>與此執行個體關聯的或代表此執行個體的運算式樹狀架構。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Provider">
      <summary>取得與這個執行個體關聯的查詢提供者。</summary>
      <returns>與這個執行個體關聯的查詢提供者。</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>建構新的 <see cref="T:System.Linq.EnumerableQuery`1" /> 物件，並將其與指定的運算式樹狀架構 (表示資料的 <see cref="T:System.Linq.IQueryable`1" /> 集合) 建立關聯。</summary>
      <returns>表示與 <paramref name="expression" /> 關聯的 EnumerableQuery 物件。</returns>
      <param name="expression">要執行的運算式樹狀架構。</param>
      <typeparam name="S">該 <paramref name="expression" /> 所代表之集合中的資料型別。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery(System.Linq.Expressions.Expression)">
      <summary>建構新的 <see cref="T:System.Linq.EnumerableQuery`1" /> 物件，並將其與指定的運算式樹狀架構 (表示資料的 <see cref="T:System.Linq.IQueryable" /> 集合) 建立關聯。</summary>
      <returns>與 <paramref name="expression" /> 相關聯的 <see cref="T:System.Linq.EnumerableQuery`1" /> 物件。</returns>
      <param name="expression">表示資料之 <see cref="T:System.Linq.IQueryable" /> 集合的運算式樹狀架構。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute``1(System.Linq.Expressions.Expression)">
      <summary>在無法以 <see cref="T:System.Linq.Queryable" /> 方法查詢的可列舉資料來源上，將重新寫入運算式以呼叫 <see cref="T:System.Linq.Enumerable" /> 方法後，執行運算式，而不使用 <see cref="T:System.Linq.Queryable" /> 方法。</summary>
      <returns>執行 <paramref name="expression" /> 所產生的值。</returns>
      <param name="expression">要執行的運算式樹狀架構。</param>
      <typeparam name="S">該 <paramref name="expression" /> 所代表之集合中的資料型別。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute(System.Linq.Expressions.Expression)">
      <summary>在無法以 <see cref="T:System.Linq.Queryable" /> 方法查詢的可列舉資料來源上，將重新寫入運算式以呼叫 <see cref="T:System.Linq.Enumerable" /> 方法後，執行運算式，而不使用 <see cref="T:System.Linq.Queryable" /> 方法。</summary>
      <returns>執行 <paramref name="expression" /> 所產生的值。</returns>
      <param name="expression">要執行的運算式樹狀架構。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.ToString">
      <summary>傳回可列舉集合的文字表示，如果為 null，則傳回與該執行個體關聯的運算式樹狀架構的文字表示。</summary>
      <returns>可列舉集合的文字表示，如果為 null，則為與該執行個體關聯的運算式樹狀架構的文字表示。</returns>
    </member>
    <member name="T:System.Linq.Queryable">
      <summary>提供一組 static (在 Visual Basic 中為 Shared) 方法，用於查詢實作 <see cref="T:System.Linq.IQueryable`1" /> 的資料結構。</summary>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0,``0}})">
      <summary>將累加函式套用到序列上。</summary>
      <returns>最終累積值。</returns>
      <param name="source">所要彙總的序列。</param>
      <param name="func">要套用到每個項目的累加函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``2(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}})">
      <summary>將累加函式套用到序列上。使用指定的初始值做為初始累加值。</summary>
      <returns>最終累積值。</returns>
      <param name="source">所要彙總的序列。</param>
      <param name="seed">初始累積值。</param>
      <param name="func">要在每個項目上叫用的累加函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TAccumulate">累積值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``3(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,``2}})">
      <summary>將累加函式套用到序列上。使用指定的值做為初始累加值，並使用指定的函式來選取結果值。</summary>
      <returns>轉換後的最終累加值。</returns>
      <param name="source">所要彙總的序列。</param>
      <param name="seed">初始累積值。</param>
      <param name="func">要在每個項目上叫用的累加函式。</param>
      <param name="selector">用來將最終累加值轉換成結果值的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TAccumulate">累積值的型別。</typeparam>
      <typeparam name="TResult">結果值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="func" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.All``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>判斷序列的所有項目是否全都符合條件。</summary>
      <returns>如果來源序列的每個項目都通過以指定之述詞 (Predicate) 進行的測試，或序列是空的，則為 true，否則為 false。</returns>
      <param name="source">要測試其項目是否符合條件的序列。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0})">
      <summary>判斷序列是否包含任何項目。</summary>
      <returns>如果來源序列包含任何項目，則為 true，否則為 false。</returns>
      <param name="source">要檢查是否為空白的序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>判斷序列的任何項目是否符合條件。</summary>
      <returns>如果來源序列中的任何項目通過以指定之述詞進行的測試，則為 true，否則為 false。</returns>
      <param name="source">要測試其項目是否符合條件的序列。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>將泛型 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 轉換成泛型 <see cref="T:System.Linq.IQueryable`1" />。</summary>
      <returns>代表輸入序列的 <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">所要轉換的序列。</param>
      <typeparam name="TElement">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable(System.Collections.IEnumerable)">
      <summary>將 <see cref="T:System.Collections.IEnumerable" /> 轉換成 <see cref="T:System.Linq.IQueryable" />。</summary>
      <returns>代表輸入序列的 <see cref="T:System.Linq.IQueryable" />。</returns>
      <param name="source">所要轉換的序列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> 不會針對某些 <paramref name="T" /> 實作 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Decimal})">
      <summary>計算 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Double})">
      <summary>計算 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int32})">
      <summary>計算 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int64})">
      <summary>計算 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>計算可為 Null 之 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果來源序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值之可為 Null 的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>計算可為 Null 之 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果來源序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值之可為 Null 的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>計算可為 Null 之 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果來源序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值之可為 Null 的 <see cref="T:System.Int32" />  值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>計算可為 Null 之 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果來源序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值之可為 Null 的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>計算可為 Null 之 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果來源序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值之可為 Null 的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Single})">
      <summary>計算 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">用來計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列是空的或是只包含 null 值，則為 null。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均。</returns>
      <param name="source">要計算平均值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 沒有包含任何項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Cast``1(System.Linq.IQueryable)">
      <summary>將 <see cref="T:System.Linq.IQueryable" /> 的項目轉換成指定的型別。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含已轉換成指定之型別的每個來源序列項目。</returns>
      <param name="source">包含要轉換之項目的 <see cref="T:System.Linq.IQueryable" />。</param>
      <typeparam name="TResult">要將 <paramref name="source" /> 之項目轉換成的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidCastException">無法將序列中的項目轉換為型別 <paramref name="TResult" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Concat``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>串連兩個序列。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含兩個輸入序列的串連項目。</returns>
      <param name="source1">要串連的第一個序列。</param>
      <param name="source2">要串連到第一個序列的序列。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0)">
      <summary>使用預設的相等比較子 (Comparer) 來判斷序列是否包含指定的項目。</summary>
      <returns>如果輸入序列包含具有指定值的項目，則為 true，否則為 false。</returns>
      <param name="source">要在其中尋找 <paramref name="item" /> 的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="item">要在序列中尋找的物件。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 來判斷序列是否包含指定的項目。</summary>
      <returns>如果輸入序列包含具有指定值的項目，則為 true，否則為 false。</returns>
      <param name="source">要在其中尋找 <paramref name="item" /> 的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="item">要在序列中尋找的物件。</param>
      <param name="comparer">用來比較值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0})">
      <summary>傳回序列中的項目數。</summary>
      <returns>輸入序列中的項目數目。</returns>
      <param name="source">包含要計算之項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 中的項目數目大於 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回指定之序列中符合條件的項目數目。</summary>
      <returns>序列中符合述詞函式之條件的項目數目。</returns>
      <param name="source">包含要計算之項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 中的項目數目大於 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0})">
      <summary>傳回指定之序列的項目；如果序列是空的，則傳回單一集合中型別參數的預設值。</summary>
      <returns>如果 <paramref name="source" /> 是空的，則為包含 default(<paramref name="TSource" />) 的 <see cref="T:System.Linq.IQueryable`1" />，否則為 <paramref name="source" />。</returns>
      <param name="source">在空白時，要傳回預設值的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0},``0)">
      <summary>傳回指定之序列的項目；如果序列是空的，則傳回單一集合中型別參數的預設值。</summary>
      <returns>如果 <paramref name="source" /> 是空的，則為包含 <paramref name="defaultValue" /> 的 <see cref="T:System.Linq.IQueryable`1" />，否則為 <paramref name="source" />。</returns>
      <param name="source">在空白時，要傳回指定之值的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="defaultValue">在序列空白時所要傳回的值。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0})">
      <summary>使用預設的相等比較子來比較值，以便從序列傳回獨特的項目。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含來自 <paramref name="source" /> 的獨特項目。</returns>
      <param name="source">要從中移除重複項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 來比較值，以便從序列傳回獨特的項目。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含來自 <paramref name="source" /> 的獨特項目。</returns>
      <param name="source">要從中移除重複項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="comparer">用來比較值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAt``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>傳回位於序列中指定索引處的項目。</summary>
      <returns>位於 <paramref name="source" /> 中指定之位置的項目。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="index">要擷取的項目之以零起始索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小於零。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAtOrDefault``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>傳回位於序列中指定索引處的項目；如果索引超出範圍，則傳回預設值。</summary>
      <returns>如果 <paramref name="index" /> 超出 <paramref name="source" /> 的範圍，則為 default(<paramref name="TSource" />)，否則為位於 <paramref name="source" /> 中指定索引處的項目。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="index">要擷取的項目之以零起始索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>使用預設相等比較子來比較值，以便產生兩個序列的差異。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含兩個序列的差異。</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" />，其項目若未同時存在 <paramref name="source2" /> 中，便會傳回這些項目。</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其項目若同時出現在第一個序列中，則不會出現在傳回的序列中。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 來比較值，以便產生兩個序列的差異。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含兩個序列的差異。</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" />，其項目若未同時存在 <paramref name="source2" /> 中，便會傳回這些項目。</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其項目若同時出現在第一個序列中，則不會出現在傳回的序列中。</param>
      <param name="comparer">用來比較值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0})">
      <summary>傳回序列的第一個項目。</summary>
      <returns>
        <paramref name="source" /> 中的第一個項目。</returns>
      <param name="source">要傳回第一個項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">來源序列為空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回序列中符合指定之條件的第一個項目。</summary>
      <returns>
        <paramref name="source" /> 中通過 <paramref name="predicate" /> 之測試的第一個項目。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
      <exception cref="T:System.InvalidOperationException">沒有任何項目符合 <paramref name="predicate" /> 中的條件。-或-來源序列為空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>傳回序列的第一個項目；如果序列中沒有包含任何項目，則傳回預設值。</summary>
      <returns>如果 <paramref name="source" /> 是空的，則為 default(<paramref name="TSource" />)，否則為 <paramref name="source" /> 中的第一個項目。</returns>
      <param name="source">要傳回第一個項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回序列中符合指定之條件的第一個項目；如果找不到這類項目，則傳回預設值。</summary>
      <returns>如果 <paramref name="source" /> 是空的，或是沒有任何項目通過 <paramref name="predicate" /> 所指定的測試，則為 default(<paramref name="TSource" />)，否則為 <paramref name="source" /> 中通過 <paramref name="predicate" /> 指定之測試的第一個項目。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>依據指定的索引鍵選擇器函式來群組序列的項目。</summary>
      <returns>在 C# 中為 IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt;，而在 Visual Basic 中則為 IQueryable(Of IGrouping(Of TKey, TSource))，其中 <see cref="T:System.Linq.IGrouping`2" /> 物件包含物件和索引鍵的序列。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>依據指定的索引鍵選取器函式來群組序列的項目，並使用指定的比較子來比較索引鍵。</summary>
      <returns>在 C# 中為 IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt;，而在 Visual Basic 中則為 IQueryable(Of IGrouping(Of TKey, TSource))，其中 <see cref="T:System.Linq.IGrouping`2" /> 包含物件和索引鍵的序列。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}})">
      <summary>依據指定的索引鍵選取器函式來群組序列的項目，並使用指定的函式來投影每個群組的項目。</summary>
      <returns>在 C# 中為 IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt;，而在 Visual Basic 中則為 IQueryable(Of IGrouping(Of TKey, TElement))，其中每個 <see cref="T:System.Linq.IGrouping`2" /> 都包含型別 <paramref name="TElement" /> 之物件的序列和索引鍵。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <param name="elementSelector">用來將每個來源項目對應至 <see cref="T:System.Linq.IGrouping`2" /> 之項目的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <typeparam name="TElement">每個 <see cref="T:System.Linq.IGrouping`2" /> 中的項目型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>使用指定的函式來群組序列的項目並投影每個群組的項目。索引鍵值是使用指定的比較子來進行比較。</summary>
      <returns>在 C# 中為 IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt;，而在 Visual Basic 中則為 IQueryable(Of IGrouping(Of TKey, TElement))，其中每個 <see cref="T:System.Linq.IGrouping`2" /> 都包含型別 <paramref name="TElement" /> 之物件的序列和索引鍵。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <param name="elementSelector">用來將每個來源項目對應至 <see cref="T:System.Linq.IGrouping`2" /> 之項目的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <typeparam name="TElement">每個 <see cref="T:System.Linq.IGrouping`2" /> 中的項目型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="elementSelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}})">
      <summary>依據指定的索引鍵選取器函式來群組序列的項目，並從每個群組及其索引鍵建立結果值。每個群組的項目都是利用指定的函式進行投影。</summary>
      <returns>T:System.Linq.IQueryable`1，其具有 <paramref name="TResult" /> 的型別引數，而且其中每個項目都代表群組及其索引鍵的投影。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <param name="elementSelector">用來將每個來源項目對應至 <see cref="T:System.Linq.IGrouping`2" /> 之項目的函式。</param>
      <param name="resultSelector">用來從各個群組建立結果值的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <typeparam name="TElement">每個 <see cref="T:System.Linq.IGrouping`2" /> 中的項目型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 所傳回之結果值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="elementSelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>依據指定的索引鍵選取器函式來群組序列的項目，並從每個群組及其索引鍵建立結果值。索引鍵是使用指定的比較子來進行比較，而每個群組的項目則都是利用指定的函式進行投影。</summary>
      <returns>T:System.Linq.IQueryable`1，其具有 <paramref name="TResult" /> 的型別引數，而且其中每個項目都代表群組及其索引鍵的投影。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <param name="elementSelector">用來將每個來源項目對應至 <see cref="T:System.Linq.IGrouping`2" /> 之項目的函式。</param>
      <param name="resultSelector">用來從各個群組建立結果值的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <typeparam name="TElement">每個 <see cref="T:System.Linq.IGrouping`2" /> 中的項目型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 所傳回之結果值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="elementSelector" />、<paramref name="resultSelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}})">
      <summary>依據指定的索引鍵選取器函式來群組序列的項目，並從每個群組及其索引鍵建立結果值。</summary>
      <returns>T:System.Linq.IQueryable`1，其具有 <paramref name="TResult" /> 的型別引數，而且其中每個項目都代表群組及其索引鍵的投影。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <param name="resultSelector">用來從各個群組建立結果值的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 所傳回之結果值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>依據指定的索引鍵選取器函式來群組序列的項目，並從每個群組及其索引鍵建立結果值。索引鍵是使用指定的比較子來進行比較。</summary>
      <returns>T:System.Linq.IQueryable`1，其具有 <paramref name="TResult" /> 的型別引數，而且其中每個項目都代表群組及其索引鍵的投影。</returns>
      <param name="source">要群組其項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用來擷取各項目之索引鍵的函式。</param>
      <param name="resultSelector">用來從各個群組建立結果值的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 中表示之函式所傳回索引鍵的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 所傳回之結果值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="resultSelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}})">
      <summary>根據索引鍵相等與否，將兩個序列的項目相互關聯，並群組產生的結果。預設的相等比較子是用於比較索引鍵。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含透過對兩個序列執行群組聯結所取得之型別 <paramref name="TResult" /> 的項目。</returns>
      <param name="outer">要聯結的第一個序列。</param>
      <param name="inner">要加入第一個序列的序列。</param>
      <param name="outerKeySelector">用來從第一個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="innerKeySelector">用來從第二個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="resultSelector">函式，用來從第一個序列的項目以及第二個序列的相符項目集合建立結果項目。</param>
      <typeparam name="TOuter">第一個序列的項目之型別。</typeparam>
      <typeparam name="TInner">第二個序列的項目之型別。</typeparam>
      <typeparam name="TKey">索引鍵選取器函式所傳回的索引鍵之型別。</typeparam>
      <typeparam name="TResult">結果項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>根據索引鍵相等與否，將兩個序列的項目相互關聯，並群組產生的結果。指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 是用於比較索引鍵。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含透過對兩個序列執行群組聯結所取得之型別 <paramref name="TResult" /> 的項目。</returns>
      <param name="outer">要聯結的第一個序列。</param>
      <param name="inner">要加入第一個序列的序列。</param>
      <param name="outerKeySelector">用來從第一個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="innerKeySelector">用來從第二個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="resultSelector">函式，用來從第一個序列的項目以及第二個序列的相符項目集合建立結果項目。</param>
      <param name="comparer">用來雜湊及比較索引鍵的比較子。</param>
      <typeparam name="TOuter">第一個序列的項目之型別。</typeparam>
      <typeparam name="TInner">第二個序列的項目之型別。</typeparam>
      <typeparam name="TKey">索引鍵選取器函式所傳回的索引鍵之型別。</typeparam>
      <typeparam name="TResult">結果項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>使用預設相等比較子來比較值，以便產生兩個序列的交集。</summary>
      <returns>包含兩個序列之交集的序列。</returns>
      <param name="source1">傳回其獨特項目同時出現在 <paramref name="source2" /> 中的序列。</param>
      <param name="source2">傳回其獨特項目同時出現在第一個序列中的序列。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 來比較值，以便產生兩個序列的交集。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含兩個序列的交集。</returns>
      <param name="source1">傳回其獨特項目同時出現在 <paramref name="source2" /> 中的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="source2">傳回其獨特項目同時出現在第一個序列中的 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="comparer">用來比較值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}})">
      <summary>根據相符索引鍵，將兩個序列的項目相互關聯。預設的相等比較子是用於比較索引鍵。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其具有透過對兩個序列執行內部聯結所取得之型別 <paramref name="TResult" /> 的項目。</returns>
      <param name="outer">要聯結的第一個序列。</param>
      <param name="inner">要加入第一個序列的序列。</param>
      <param name="outerKeySelector">用來從第一個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="innerKeySelector">用來從第二個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="resultSelector">用來從兩個相符項目建立結果項目的函式。</param>
      <typeparam name="TOuter">第一個序列的項目之型別。</typeparam>
      <typeparam name="TInner">第二個序列的項目之型別。</typeparam>
      <typeparam name="TKey">索引鍵選取器函式所傳回的索引鍵之型別。</typeparam>
      <typeparam name="TResult">結果項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>根據相符索引鍵，將兩個序列的項目相互關聯。指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 是用於比較索引鍵。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其具有透過對兩個序列執行內部聯結所取得之型別 <paramref name="TResult" /> 的項目。</returns>
      <param name="outer">要聯結的第一個序列。</param>
      <param name="inner">要加入第一個序列的序列。</param>
      <param name="outerKeySelector">用來從第一個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="innerKeySelector">用來從第二個序列各個項目擷取聯結索引鍵的函式。</param>
      <param name="resultSelector">用來從兩個相符項目建立結果項目的函式。</param>
      <param name="comparer">用來雜湊及比較索引鍵的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TOuter">第一個序列的項目之型別。</typeparam>
      <typeparam name="TInner">第二個序列的項目之型別。</typeparam>
      <typeparam name="TKey">索引鍵選取器函式所傳回的索引鍵之型別。</typeparam>
      <typeparam name="TResult">結果項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0})">
      <summary>傳回序列中的最後一個項目。</summary>
      <returns>位於 <paramref name="source" /> 中最後一個位置的值。</returns>
      <param name="source">要傳回最後一個項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">來源序列為空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回序列中符合指定之條件的最後一個項目。</summary>
      <returns>
        <paramref name="source" /> 中通過 <paramref name="predicate" /> 指定之測試的最後一個項目。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
      <exception cref="T:System.InvalidOperationException">沒有任何項目符合 <paramref name="predicate" /> 中的條件。-或-來源序列為空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>傳回序列中的最後一個項目；如果序列中沒有包含任何項目，則傳回預設值。</summary>
      <returns>如果 <paramref name="source" /> 是空的，則為 default(<paramref name="TSource" />)，否則為 <paramref name="source" /> 中的最後一個項目。</returns>
      <param name="source">要傳回最後一個項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回序列中符合條件的最後一個項目；如果找不到這類項目，則傳回預設值。</summary>
      <returns>如果 <paramref name="source" /> 是空的，或是沒有任何項目通過述詞函式中的測試，則為 default(<paramref name="TSource" />)，否則為 <paramref name="source" /> 中通過述詞函式之測試的最後一個項目。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0})">
      <summary>傳回代表序列中項目總數的 <see cref="T:System.Int64" />。</summary>
      <returns>
        <paramref name="source" /> 中的項目數目。</returns>
      <param name="source">包含要計算之項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">項目數目超出 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回 <see cref="T:System.Int64" />，其代表序列中符合條件的項目數目。</summary>
      <returns>
        <paramref name="source" /> 中符合述詞函式之條件的項目數目。</returns>
      <param name="source">包含要計算之項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
      <exception cref="T:System.OverflowException">符合的項目數目超出 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``1(System.Linq.IQueryable{``0})">
      <summary>傳回泛型 <see cref="T:System.Linq.IQueryable`1" /> 中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要判斷最大值的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>對泛型 <see cref="T:System.Linq.IQueryable`1" /> 的每個項目叫用投影函式，並傳回最大的結果值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要判斷最大值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 表示之函式所傳回值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``1(System.Linq.IQueryable{``0})">
      <summary>傳回泛型 <see cref="T:System.Linq.IQueryable`1" /> 的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">要判斷最小值的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>對泛型 <see cref="T:System.Linq.IQueryable`1" /> 的每個項目叫用投影函式，並傳回最小的結果值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">要判斷最小值的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 表示之函式所傳回值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OfType``1(System.Linq.IQueryable)">
      <summary>根據指定的型別來篩選 <see cref="T:System.Linq.IQueryable" /> 的項目。</summary>
      <returns>集合，其中包含 <paramref name="source" /> 中型別為 <paramref name="TResult" /> 的項目。</returns>
      <param name="source">要篩選其項目的 <see cref="T:System.Linq.IQueryable" />。</param>
      <typeparam name="TResult">用來做為序列項目之篩選依據的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>依據索引鍵，按遞增順序排序序列中的項目。</summary>
      <returns>依據索引鍵排序其項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">要排序的值序列。</param>
      <param name="keySelector">用來從項目擷取索引鍵的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比較子，依遞增順序排序序列中的項目。</summary>
      <returns>依據索引鍵排序其項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">要排序的值序列。</param>
      <param name="keySelector">用來從項目擷取索引鍵的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>依據索引鍵，按遞減順序排序序列中的項目。</summary>
      <returns>依據索引鍵按遞減順序排序其項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">要排序的值序列。</param>
      <param name="keySelector">用來從項目擷取索引鍵的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比較子，依遞減順序排序序列中的項目。</summary>
      <returns>依據索引鍵按遞減順序排序其項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">要排序的值序列。</param>
      <param name="keySelector">用來從項目擷取索引鍵的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Reverse``1(System.Linq.IQueryable{``0})">
      <summary>反轉序列中項目的排序方向。</summary>
      <returns>其項目對應於輸入序列中反向排序之項目的 <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">要反轉方向的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>將序列的每一個項目規劃成一個新的表單。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其項目為在 <paramref name="source" /> 各個項目上叫用投影函式的結果。</returns>
      <param name="source">要投影的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 表示之函式所傳回值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,``1}})">
      <summary>透過加入項目的索引，將序列的每個項目投影成新的表單。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其項目為在 <paramref name="source" /> 各個項目上叫用投影函式的結果。</returns>
      <param name="source">要投影的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 表示之函式所傳回值的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>將序列的每個項目投影成 <see cref="T:System.Collections.Generic.IEnumerable`1" />，並在其中的每個項目上叫用結果選取器函式。每個中繼序列產生的值都會合併成單一的一維序列，然後再傳回。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其項目是執行下列動作後所產生的結果：對 <paramref name="source" /> 的各個項目叫用一對多投影函式 <paramref name="collectionSelector" />，然後再將每個序列項目及其對應之 <paramref name="source" /> 項目對應到結果項目。</returns>
      <param name="source">要投影的值序列。</param>
      <param name="collectionSelector">要套用到輸入序列中各個項目的投影函式。</param>
      <param name="resultSelector">要套用到各中繼序列之各個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TCollection">
        <paramref name="collectionSelector" /> 表示之函式所收集之中繼項目的型別。</typeparam>
      <typeparam name="TResult">產生的序列之項目型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="collectionSelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>將序列的每個項目都投影成 <see cref="T:System.Collections.Generic.IEnumerable`1" />，並將產生的序列合併成一個序列。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其項目是對輸入序列中各個項目叫用一對多投影函式後所產生的結果。</returns>
      <param name="source">要投影的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 表示之函式所傳回序列之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>將序列的每個項目都投影成 <see cref="T:System.Collections.Generic.IEnumerable`1" />，以合併產生該項目之來源項目的索引。接著對各中繼序列的每個項目叫用結果選取器函式，然後將產生的值合併成單一的一維序列並傳回。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其項目是執行下列動作後所產生的結果：對 <paramref name="source" /> 的各個項目叫用一對多投影函式 <paramref name="collectionSelector" />，然後再將每個序列項目及其對應之 <paramref name="source" /> 項目對應到結果項目。</returns>
      <param name="source">要投影的值序列。</param>
      <param name="collectionSelector">要套用到輸入序列每個項目的投影函式；此函式的第二個參數代表來源項目的索引。</param>
      <param name="resultSelector">要套用到各中繼序列之各個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TCollection">
        <paramref name="collectionSelector" /> 表示之函式所收集之中繼項目的型別。</typeparam>
      <typeparam name="TResult">產生的序列之項目型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="collectionSelector" /> 或 <paramref name="resultSelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>將序列的每個項目都投影成 <see cref="T:System.Collections.Generic.IEnumerable`1" />，並將產生的序列合併成一個序列。各來源項目的索引是在該項目的投影表單中使用。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其項目是對輸入序列中各個項目叫用一對多投影函式後所產生的結果。</returns>
      <param name="source">要投影的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式；此函式的第二個參數代表來源項目的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> 表示之函式所傳回序列之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>使用預設相等比較子來比較項目，以判斷兩個序列是否相等。</summary>
      <returns>如果兩個來源序列的長度相同，而且其對應項目比較結果相同，則為 true，否則為 false。</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" />，其項目要與 <paramref name="source2" /> 的項目比較。</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其項目要與第一個序列的項目比較。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 來比較項目，以判斷兩個序列是否相等。</summary>
      <returns>如果兩個來源序列的長度相同，而且其對應項目比較結果相同，則為 true，否則為 false。</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" />，其項目要與 <paramref name="source2" /> 的項目比較。</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" />，其項目要與第一個序列的項目比較。</param>
      <param name="comparer">用來比較項目的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0})">
      <summary>傳回序列的唯一一個項目，如果序列中不是正好一個項目，則擲回例外狀況。</summary>
      <returns>輸入序列的單一項目。</returns>
      <param name="source">要傳回單一項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 具有多個項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回序列中符合指定之條件的唯一一個項目，如果有一個以上這類項目，則擲回例外狀況。</summary>
      <returns>輸入序列中符合 <paramref name="predicate" /> 之條件的單一項目。</returns>
      <param name="source">要傳回單一項目的來源 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
      <exception cref="T:System.InvalidOperationException">沒有任何項目符合 <paramref name="predicate" /> 中的條件。-或-超過一個項目符合 <paramref name="predicate" /> 中的條件。-或-來源序列為空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>傳回序列的唯一一個項目，如果序列是空白，則為預設值，如果序列中有一個以上的項目，這個方法就會擲回例外狀況。</summary>
      <returns>輸入序列的單一項目；如果序列沒有包含任何項目，則為 default(<paramref name="TSource" />)。</returns>
      <param name="source">要傳回單一項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 具有多個項目。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>傳回序列中符合指定之條件的唯一一個項目，如果沒有這類項目，則為預設值，如果有一個以上的項目符合條件，這個方法就會擲回例外狀況。</summary>
      <returns>輸入序列中符合 <paramref name="predicate" /> 中條件的單一項目；如果找不到這類項目，則為 default(<paramref name="TSource" />)。</returns>
      <param name="source">要傳回單一項目的來源 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
      <exception cref="T:System.InvalidOperationException">超過一個項目符合 <paramref name="predicate" /> 中的條件。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Skip``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>略過序列中指定的項目數目，然後傳回其餘項目。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含出現在輸入序列中指定之索引後面的項目。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="count">傳回其餘項目之前要略過的項目數目。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>只要指定的條件為 true，便略過序列中的項目，然後傳回其餘項目。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含的項目位於 <paramref name="source" />，而且是從沒有通過 <paramref name="predicate" /> 所指定之測試的線性系列中第一個項目開始。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>只要指定的條件為 true，便略過序列中的項目，然後傳回其餘項目。項目的索引是用於述詞功能的邏輯中。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含的項目位於 <paramref name="source" />，而且是從沒有通過 <paramref name="predicate" /> 所指定之測試的線性系列中第一個項目開始。</returns>
      <param name="source">傳回項目的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試各項目是否符合條件的函式；此函式的第二個參數代表來源項目的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Decimal})">
      <summary>計算 <see cref="T:System.Decimal" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Double})">
      <summary>計算 <see cref="T:System.Double" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int32})">
      <summary>計算 <see cref="T:System.Int32" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int64})">
      <summary>計算 <see cref="T:System.Int64" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>計算可為 Null 之 <see cref="T:System.Decimal" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和之可為 Null 的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>計算可為 Null 之 <see cref="T:System.Double" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和之可為 Null 的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>計算可為 Null 之 <see cref="T:System.Int32" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和之可為 Null 的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>計算可為 Null 之 <see cref="T:System.Int64" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和之可為 Null 的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>計算可為 Null 之 <see cref="T:System.Single" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和之可為 Null 的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Single})">
      <summary>計算 <see cref="T:System.Single" /> 值序列的總和。</summary>
      <returns>序列中值的總合。</returns>
      <param name="source">要計算總和的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Decimal" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Double" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Int32" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Int64" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Decimal" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Double" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Int32" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Int64" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
      <exception cref="T:System.OverflowException">總和大於 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得可為 Null 之 <see cref="T:System.Single" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>計算在輸入序列中各項目上叫用投影函式後所取得之 <see cref="T:System.Single" /> 值序列的總和。</summary>
      <returns>預計值的總合。</returns>
      <param name="source">型別 <paramref name="TSource" /> 的值序列。</param>
      <param name="selector">要套用到每個項目的投影函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Take``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>從序列開頭傳回指定的連續項目數目。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含來自 <paramref name="source" /> 開頭的指定項目數目。</returns>
      <param name="source">傳回項目的序列。</param>
      <param name="count">要傳回的項目數目。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>只要指定的條件為 true，就會傳回序列中的項目。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含輸入序列中的項目，而這些項目出現在已無法通過 <paramref name="predicate" /> 所指定之測試的項目前面。</returns>
      <param name="source">傳回項目的序列。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>只要指定的條件為 true，就會傳回序列中的項目。項目的索引是用於述詞功能的邏輯中。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含輸入序列中的項目，而這些項目出現在已無法通過 <paramref name="predicate" /> 所指定之測試的項目前面。</returns>
      <param name="source">傳回項目的序列。</param>
      <param name="predicate">用來測試各項目是否符合條件的函式；此函式的第二個參數代表來源序列中項目的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>依據索引鍵，按遞增順序執行序列中項目的後續排序作業。</summary>
      <returns>依據索引鍵排序其項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">包含要排序之項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">用來從各個項目擷取索引鍵的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比較子，依遞增順序執行序列中項目的後續排序作業。</summary>
      <returns>依據索引鍵排序其項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">包含要排序之項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">用來從各個項目擷取索引鍵的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>依據索引鍵，按遞減順序執行序列中項目的後續排序作業。</summary>
      <returns>依據索引鍵按遞減順序排序其項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">包含要排序之項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">用來從各個項目擷取索引鍵的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 表示之函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比較子，依遞減順序執行序列中項目的後續排序作業。</summary>
      <returns>依據索引鍵按遞減順序排序其項目的集合。</returns>
      <param name="source">包含要排序之項目的 <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">用來從各個項目擷取索引鍵的函式。</param>
      <param name="comparer">用來比較索引鍵的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 函式所傳回索引鍵的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" /> 或 <paramref name="comparer" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>使用預設相等比較值來比較值，以便產生兩個序列的集合等位。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含來自兩個輸入序列的項目，但不包括重複的項目。</returns>
      <param name="source1">序列，其獨特項目構成等位作業的第一個集合。</param>
      <param name="source2">序列，其獨特項目構成等位作業的第二個集合。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 產生兩個序列的集合等位。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含來自兩個輸入序列的項目，但不包括重複的項目。</returns>
      <param name="source1">序列，其獨特項目構成等位作業的第一個集合。</param>
      <param name="source2">序列，其獨特項目構成等位作業的第二個集合。</param>
      <param name="comparer">用來比較值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">輸入序列的項目之型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 為 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>根據述詞來篩選值序列。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含輸入序列中符合 <paramref name="predicate" /> 指定之條件的項目。</returns>
      <param name="source">要篩選的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試每個項目是否符合條件的函式。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>根據述詞來篩選值序列。述詞函式的邏輯中使用各項目的索引。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含輸入序列中符合 <paramref name="predicate" /> 指定之條件的項目。</returns>
      <param name="source">要篩選的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用來測試各項目是否符合條件的函式；此函式的第二個參數代表來源序列中項目的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 之項目的型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 是 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Zip``3(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>使用指定的述詞函式來合併兩個序列。</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" />，其中包含兩個輸入序列的合併項目。</returns>
      <param name="source1">要合併的第一個序列。</param>
      <param name="source2">要合併的第二個序列。</param>
      <param name="resultSelector">指定如何從兩個序列合併項目的函式。</param>
      <typeparam name="TFirst">第一個輸入序列的項目型別。</typeparam>
      <typeparam name="TSecond">第二個輸入序列的項目型別。</typeparam>
      <typeparam name="TResult">結果序列的項目型別。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2 " />為 null。</exception>
    </member>
  </members>
</doc>