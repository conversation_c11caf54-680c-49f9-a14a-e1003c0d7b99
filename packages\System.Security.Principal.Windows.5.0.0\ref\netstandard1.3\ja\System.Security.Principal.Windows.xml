﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Principal.Windows</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle">
      <summary>[セキュリティ クリティカル] Windows のスレッドまたはプロセスのアクセス トークンにセーフ ハンドルを提供します。詳細については、「アクセス トークン」を参照してください。</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.#ctor(System.IntPtr)">
      <summary>[セキュリティ クリティカル] <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="handle">使用する既存のハンドルを表す <see cref="T:System.IntPtr" /> オブジェクト。<see cref="F:System.IntPtr.Zero" /> を使用して無効なハンドルを返します。</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.InvalidHandle">
      <summary>[セキュリティ クリティカル] <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> オブジェクトを <see cref="F:System.IntPtr.Zero" /> でインスタンス化して、無効なハンドルを返します。</summary>
      <returns>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> オブジェクトを返します。</returns>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.IsInvalid">
      <summary>[セキュリティ クリティカル] ハンドルが無効かどうかを示す値を取得します。</summary>
      <returns>ハンドルが無効な場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityNotMappedException">
      <summary>ID を既知の ID に割り当てることができないプリンシパルの例外を表します。</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor">
      <summary>
        <see cref="T:System.Security.Principal.IdentityNotMappedException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String)">
      <summary>指定されたエラー メッセージを使用して、<see cref="T:System.Security.Principal.IdentityNotMappedException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと内部例外を使用して、<see cref="T:System.Security.Principal.IdentityNotMappedException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">例外の原因を説明するエラー メッセージ。</param>
      <param name="inner">現在の例外の原因である例外。<paramref name="inner" /> が null でない場合は、内部例外を処理する catch ブロックで現在の例外が発生します。</param>
    </member>
    <member name="P:System.Security.Principal.IdentityNotMappedException.UnmappedIdentities">
      <summary>
        <see cref="T:System.Security.Principal.IdentityNotMappedException" /> 例外について割り当てられていない ID のコレクションを表します。</summary>
      <returns>割り当てられていない ID のコレクション。</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReference">
      <summary>ID を表し、<see cref="T:System.Security.Principal.NTAccount" /> クラスおよび <see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスの基本クラスです。このクラスはパブリック コンストラクターを提供していないため、継承できません。</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Equals(System.Object)">
      <summary>指定したオブジェクトが <see cref="T:System.Security.Principal.IdentityReference" /> クラスのこのインスタンスと等しいかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="o" /> が、基になる型と値がこの <see cref="T:System.Security.Principal.IdentityReference" /> インスタンスと同じであるオブジェクトの場合は true。それ以外の場合は false。</returns>
      <param name="o">この <see cref="T:System.Security.Principal.IdentityReference" /> インスタンスと比較するオブジェクト、または null 参照。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.GetHashCode">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> のハッシュ関数として機能します。<see cref="M:System.Security.Principal.IdentityReference.GetHashCode" /> は、ハッシュ アルゴリズムや、ハッシュ テーブルなどのデータ構造での使用に適しています。</summary>
      <returns>この <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトのハッシュ コード。</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.IsValidTargetType(System.Type)">
      <summary>指定した型が <see cref="T:System.Security.Principal.IdentityReference" /> クラスの有効な変換型であるかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="targetType" /> が <see cref="T:System.Security.Principal.IdentityReference" /> クラスの有効な変換型である場合は true。それ以外の場合は false。</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.IdentityReference" /> からの変換として機能するための有効性を照会する型。有効な変換後の型は、次のとおりです。<see cref="T:System.Security.Principal.NTAccount" /><see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Equality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>2 つの <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトを比較し、それらのオブジェクトが等しいかどうかを確認します。これらのオブジェクトが <see cref="P:System.Security.Principal.IdentityReference.Value" /> プロパティで返される標準の名前表現と同じ標準の名前表現を持つ場合、またはこれらのオブジェクトがどちらも null である場合は、これらのオブジェクトが等しいと見なされます。</summary>
      <returns>
        <paramref name="left" /> と <paramref name="right" /> が等しい場合は true。それ以外の場合は false。</returns>
      <param name="left">等しいかどうかの比較に使用する左辺の <see cref="T:System.Security.Principal.IdentityReference" /> オペランド。このパラメーターは、null に設定できます。</param>
      <param name="right">等しいかどうかの比較に使用する右辺の <see cref="T:System.Security.Principal.IdentityReference" /> オペランド。このパラメーターは、null に設定できます。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Inequality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>2 つの <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトを比較し、それらのオブジェクトが等しくないかどうかを判断します。これらのオブジェクトが <see cref="P:System.Security.Principal.IdentityReference.Value" /> プロパティで返される標準の名前表現と異なる標準の名前表現を持つ場合、または一方のオブジェクトが null である場合は、これらのオブジェクトが等しくないと見なされます。</summary>
      <returns>
        <paramref name="left" /> と <paramref name="right" /> が等しくない場合は true。それ以外の場合は false。</returns>
      <param name="left">等しくないかどうかの比較に使用する左辺の <see cref="T:System.Security.Principal.IdentityReference" /> オペランド。このパラメーターは、null に設定できます。</param>
      <param name="right">等しくないかどうかの比較に使用する右辺の <see cref="T:System.Security.Principal.IdentityReference" /> オペランド。このパラメーターは、null に設定できます。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.ToString">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトで表される ID の文字列形式を返します。</summary>
      <returns>ID の文字列形式。</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Translate(System.Type)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトで表されるアカウント名を他の <see cref="T:System.Security.Principal.IdentityReference" /> 派生型に変換します。</summary>
      <returns>変換後の ID。</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.IdentityReference" /> からの変換後の型。</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReference.Value">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトで表される ID の文字列値を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトで表される ID の文字列値。</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReferenceCollection">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトのコレクションを表し、<see cref="T:System.Security.Principal.IdentityReference" /> から派生したオブジェクトのセットを <see cref="T:System.Security.Principal.IdentityReference" /> から派生した型に変換する手段を提供します。</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor">
      <summary>項目なしのコレクションを使用して、<see cref="T:System.Security.Principal.IdentityReferenceCollection" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor(System.Int32)">
      <summary>指定した初期サイズを使用して、<see cref="T:System.Security.Principal.IdentityReferenceCollection" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="capacity">コレクション内の項目の初期数。<paramref name="capacity" /> の値はヒントにすぎません。作成された項目の最大数とは限りません。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Add(System.Security.Principal.IdentityReference)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションに <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトを追加します。</summary>
      <param name="identity">コレクションに追加する <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Clear">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションからすべての <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトを削除します。</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Contains(System.Security.Principal.IdentityReference)">
      <summary>指定した <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトが <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションに格納されているかどうかを示します。</summary>
      <returns>指定したオブジェクトがコレクションに格納されている場合は true。</returns>
      <param name="identity">確認する対象の <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクト。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.CopyTo(System.Security.Principal.IdentityReference[],System.Int32)">
      <summary>指定したインデックスを開始位置として、<see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 配列に <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションをコピーします。</summary>
      <param name="array">
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションのコピー先の <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> 配列オブジェクト。</param>
      <param name="offset">
        <paramref name="array" /> の 0 から始まるインデックス。この位置を先頭に、<see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションがコピーされます。</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Count">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクション内の項目の数を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクション内の <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトの数。</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションを反復処理するために使用できる列挙子を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションの列挙子。</returns>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Item(System.Int32)">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションの指定したインデックス位置にあるノードを取得または設定します。</summary>
      <returns>コレクション内の指定したインデックス位置の <see cref="T:System.Security.Principal.IdentityReference" />。<paramref name="index" /> がコレクション内のノードの数以上である場合、戻り値は null です。</returns>
      <param name="index">
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションの 0 から始まるインデックス。</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Remove(System.Security.Principal.IdentityReference)">
      <summary>指定した <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクトをコレクションから削除します。</summary>
      <returns>指定したオブジェクトがコレクションから削除された場合は true。</returns>
      <param name="identity">削除する <see cref="T:System.Security.Principal.IdentityReference" /> オブジェクト。</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.System#Collections#Generic#ICollection{T}#IsReadOnly"></member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションを反復処理するために使用できる列挙子を取得します。</summary>
      <returns>
        <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクションの列挙子。</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type)">
      <summary>コレクション内のオブジェクトを指定した型に変換します。このメソッドを呼び出した場合、2 つ目のパラメーターを false に設定して <see cref="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)" /> を呼び出した場合と同じ結果が得られます。この場合、変換に失敗した項目に対して例外がスローされません。</summary>
      <returns>元のコレクションの変換後の内容を表す <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクション。</returns>
      <param name="targetType">コレクション内の項目の変換後の型。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)">
      <summary>コレクション内のオブジェクトを指定した型に変換します。また、指定したフォールト トレランスを使用して、変換後の型が割り当てられていない型に関連付けられたエラーを処理または無視します。</summary>
      <returns>元のコレクションの変換後の内容を表す <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> コレクション。</returns>
      <param name="targetType">コレクション内の項目の変換後の型。</param>
      <param name="forceSuccess">変換エラーの処理方法を決定するブール値。<paramref name="forceSuccess" /> が true の場合、変換後の型が割り当てられていないことが原因で変換エラーが発生すると、変換が失敗して例外がスローされます。<paramref name="forceSuccess" /> が false の場合、変換後の型が割り当てられていないことが原因で変換に失敗した型は、返されるコレクション内に変換されずにコピーされます。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.NTAccount">
      <summary>ユーザーまたはグループ アカウントを表します。</summary>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String)">
      <summary>名前を指定して、<see cref="T:System.Security.Principal.NTAccount" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="name">
        <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトの作成に使用する名前。このパラメーターには、null も空の文字列も指定できません。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> が空の文字列です。または<paramref name="name" /> が長すぎます。</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String,System.String)">
      <summary>ドメイン名とアカウント名を指定して、<see cref="T:System.Security.Principal.NTAccount" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="domainName">ドメインの名前。このパラメーターには、null または空の文字列を指定できます。ドメイン名が null 値の場合、空の文字列と同様に処理されます。</param>
      <param name="accountName">アカウントの名前。このパラメーターには、null も空の文字列も指定できません。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="accountName" /> は null なので、</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="accountName" /> が空の文字列です。または<paramref name="accountName" /> が長すぎます。または<paramref name="domainName" /> が長すぎます。</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Equals(System.Object)">
      <summary>この <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトが指定したオブジェクトに等しいかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="o" /> が、基になる型と値がこの <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトと同じであるオブジェクトの場合は true。それ以外の場合は false。</returns>
      <param name="o">この <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトと比較する対象のオブジェクト、または null。</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.GetHashCode">
      <summary>現在の <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトのハッシュ関数として機能します。<see cref="M:System.Security.Principal.NTAccount.GetHashCode" /> メソッドは、ハッシュ アルゴリズムや、ハッシュ テーブルなどのデータ構造での使用に適しています。</summary>
      <returns>現在の <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトのハッシュ値。</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)">
      <summary>指定した型が <see cref="T:System.Security.Principal.NTAccount" /> クラスの有効な変換型であるかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="targetType" /> が <see cref="T:System.Security.Principal.NTAccount" /> クラスの有効な変換型である場合は true。それ以外の場合は false。</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.NTAccount" /> からの変換として機能するための有効性を照会する型。有効な変換後の型は、次のとおりです。- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Equality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>2 つの <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトを比較し、それらのオブジェクトが等しいかどうかを確認します。これらのオブジェクトが <see cref="P:System.Security.Principal.NTAccount.Value" /> プロパティで返される標準の名前表現と同じ標準の名前表現を持つ場合、またはこれらのオブジェクトがどちらも null である場合は、これらのオブジェクトが等しいと見なされます。</summary>
      <returns>
        <paramref name="left" /> と <paramref name="right" /> が等しい場合は true。それ以外の場合は false。</returns>
      <param name="left">等しいかどうかの比較に使用する左のオペランド。このパラメーターは、null に設定できます。</param>
      <param name="right">等しいかどうかの比較に使用する右のオペランド。このパラメーターは、null に設定できます。</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Inequality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>2 つの <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトを比較し、それらのオブジェクトが等しくないかどうかを判断します。これらのオブジェクトが <see cref="P:System.Security.Principal.NTAccount.Value" /> プロパティで返される標準の名前表現と異なる標準の名前表現を持つ場合、または一方のオブジェクトが null である場合は、これらのオブジェクトが等しくないと見なされます。</summary>
      <returns>
        <paramref name="left" /> と <paramref name="right" /> が等しくない場合は true。それ以外の場合は false。</returns>
      <param name="left">等しくないかどうかの比較に使用する左のオペランド。このパラメーターは、null に設定できます。</param>
      <param name="right">等しくないかどうかの比較に使用する右のオペランド。このパラメーターは、null に設定できます。</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.ToString">
      <summary>
        <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトで表されるアカウントのアカウント名を Domain\Account 形式で返します。</summary>
      <returns>Domain\Account 形式のアカウント名。</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Translate(System.Type)">
      <summary>
        <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトで表されるアカウント名を他の <see cref="T:System.Security.Principal.IdentityReference" /> 派生型に変換します。</summary>
      <returns>変換後の ID。</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.NTAccount" /> からの変換後の型。変換後の型は、<see cref="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)" /> メソッドで有効と見なされる型である必要があります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " /> は <see cref="T:System.Security.Principal.IdentityReference" /> 型ではありません。</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">ID 参照の一部またはすべてを変換できませんでした。</exception>
      <exception cref="T:System.SystemException">ソース アカウント名が長すぎます。またはWin32 エラー コードが返されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.NTAccount.Value">
      <summary>この <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトの大文字の文字列形式を返します。</summary>
      <returns>この <see cref="T:System.Security.Principal.NTAccount" /> オブジェクトの大文字の文字列形式。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.SecurityIdentifier">
      <summary>セキュリティ識別子 (SID) を表し、SID のマーシャリングおよび比較演算を実現します。</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Byte[],System.Int32)">
      <summary>セキュリティ識別子 (SID) の指定したバイナリ表現を使用して、<see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="binaryForm">SID を表すバイト配列。</param>
      <param name="offset">
        <paramref name="binaryForm" /> の開始インデックスとして使用するバイト オフセット。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.IntPtr)">
      <summary>セキュリティ識別子 (SID) のバイナリ形式を表す整数を使用して、<see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="binaryForm">SID のバイナリ形式を表す整数。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Security.Principal.WellKnownSidType,System.Security.Principal.SecurityIdentifier)">
      <summary>一般的なセキュリティ識別子 (SID) の種類とドメイン SID を指定して、<see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="sidType">列挙値の 1 つ。この値は <see cref="F:System.Security.Principal.WellKnownSidType.LogonIdsSid" /> にしないでください。</param>
      <param name="domainSid">ドメイン SID。この値は、次の <see cref="T:System.Security.Principal.WellKnownSidType" /> 値に必要です。このパラメーターは、その他の <see cref="T:System.Security.Principal.WellKnownSidType" /> 値については無視されます。- <see cref="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountGuestSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountComputersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountControllersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.String)">
      <summary>SDDL (Security Descriptor Definition Language) 形式の指定したセキュリティ識別子 (SID) を使用して、<see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="sddlForm">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトを作成するために使用された SID の SDDL 文字列。</param>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.AccountDomainSid">
      <summary>SID が Windows アカウントの SID を表す場合、<see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表される SID からアカウント ドメイン セキュリティ識別子 (SID) 部分が返されます。SID が Windows アカウントの SID を表さない場合、このプロパティは <see cref="T:System.ArgumentNullException" /> を返します。</summary>
      <returns>SID が Windows アカウントの SID を表す場合、<see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表される SID のアカウント ドメイン SID 部分。それ以外の場合は <see cref="T:System.ArgumentNullException" /> が返されます。</returns>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.BinaryLength">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表されるセキュリティ識別子 (SID) の長さがバイト単位で返されます。</summary>
      <returns>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表される SID の長さ ( バイト単位)。</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.CompareTo(System.Security.Principal.SecurityIdentifier)">
      <summary>現在の <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトと、指定した <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトとを比較します。</summary>
      <returns>このインスタンスと <paramref name="sid" /> の相対値を示す符号付き数値。戻り値説明0 より小さい値このインスタンスは、<paramref name="sid" /> よりも小さくなっています。0このインスタンスは <paramref name="sid" /> と等価です。0 を超える値このインスタンスは <paramref name="sid" /> よりも大きくなっています。</returns>
      <param name="sid">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Object)">
      <summary>この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトが指定したオブジェクトに等しいかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="o" /> が、基になる型と値がこの <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトと同じであるオブジェクトの場合は true。それ以外の場合は false。</returns>
      <param name="o">この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトと比較する対象のオブジェクト、または null。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Security.Principal.SecurityIdentifier)">
      <summary>指定した <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトが、現在の <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトと等しいかどうかを示します。</summary>
      <returns>
        <paramref name="sid" /> の値が現在の <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトの値に等しい場合は true。</returns>
      <param name="sid">現在のオブジェクトと比較するオブジェクト。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスで表される指定したセキュリティ識別子 (SID) のバイナリ表現をバイト配列にコピーします。</summary>
      <param name="binaryForm">コピーされた SID を受け取るバイト配列。</param>
      <param name="offset">
        <paramref name="binaryForm" /> の開始インデックスとして使用するバイト オフセット。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetHashCode">
      <summary>現在の <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトのハッシュ関数として機能します。<see cref="M:System.Security.Principal.SecurityIdentifier.GetHashCode" /> メソッドは、ハッシュ アルゴリズムや、ハッシュ テーブルなどのデータ構造での使用に適しています。</summary>
      <returns>現在の <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトのハッシュ値。</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsAccountSid">
      <summary>この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表されるセキュリティ識別子 (SID) が有効な Windows アカウント SID であるかどうかを示す値を返します。</summary>
      <returns>この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表される SID が有効な Windows アカウント SID である場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsEqualDomainSid(System.Security.Principal.SecurityIdentifier)">
      <summary>この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表されるセキュリティ識別子 (SID) が指定した SID と同じドメインにあるかどうかを示す値を返します。</summary>
      <returns>この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表される SID が <paramref name="sid" /> の SID と同じドメインにある場合は true。それ以外の場合は false。</returns>
      <param name="sid">この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトと比較する SID。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)">
      <summary>指定した型が <see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスの有効な変換型であるかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="targetType" /> が <see cref="T:System.Security.Principal.SecurityIdentifier" /> クラスの有効な変換型である場合は true。それ以外の場合は false。</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> からの変換として機能するための有効性を照会する型。有効な変換後の型は、次のとおりです。- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsWellKnown(System.Security.Principal.WellKnownSidType)">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトが、指定した一般的なセキュリティ識別子 (SID) の種類と一致するかどうかを示す値を返します。</summary>
      <returns>
        <paramref name="type" /> が <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトの SID の種類である場合は true。それ以外の場合は false。</returns>
      <param name="type">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトと比較する値。</param>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MaxBinaryLength">
      <summary>セキュリティ識別子のバイナリ表現の最大サイズをバイト単位で返します。</summary>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MinBinaryLength">
      <summary>セキュリティ識別子のバイナリ表現の最小サイズをバイト単位で返します。</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Equality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>2 つの <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトを比較し、それらのオブジェクトが等しいかどうかを確認します。これらのオブジェクトが <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> プロパティで返される標準の名前表現と同じ標準の名前表現を持つ場合、またはこれらのオブジェクトがどちらも null である場合は、これらのオブジェクトが等しいと見なされます。</summary>
      <returns>
        <paramref name="left" /> と <paramref name="right" /> が等しい場合は true。それ以外の場合は false。</returns>
      <param name="left">等しいかどうかの比較に使用する左のオペランド。このパラメーターは、null に設定できます。</param>
      <param name="right">等しいかどうかの比較に使用する右のオペランド。このパラメーターは、null に設定できます。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Inequality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>2 つの <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトを比較し、それらのオブジェクトが等しくないかどうかを判断します。これらのオブジェクトが <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> プロパティで返される標準の名前表現と異なる標準の名前表現を持つ場合、または一方のオブジェクトが null である場合は、これらのオブジェクトが等しくないと見なされます。</summary>
      <returns>
        <paramref name="left" /> と <paramref name="right" /> が等しくない場合は true。それ以外の場合は false。</returns>
      <param name="left">等しくないかどうかの比較に使用する左のオペランド。このパラメーターは、null に設定できます。</param>
      <param name="right">等しくないかどうかの比較に使用する右のオペランド。このパラメーターは、null に設定できます。</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.ToString">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表されるアカウントのセキュリティ識別子 (SID) を SDDL (Security Descriptor Definition Language) 形式で返します。SDDL 形式の例は S-1-5-9 です。</summary>
      <returns>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表されるアカウントの SDDL 形式の SID。</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Translate(System.Type)">
      <summary>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表されるアカウント名を他の <see cref="T:System.Security.Principal.IdentityReference" /> 派生型に変換します。</summary>
      <returns>変換後の ID。</returns>
      <param name="targetType">
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> からの変換後の型。変換後の型は、<see cref="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)" /> メソッドで有効と見なされる型である必要があります。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />が null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " /> は <see cref="T:System.Security.Principal.IdentityReference" /> 型ではありません。</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">ID 参照の一部またはすべてを変換できませんでした。</exception>
      <exception cref="T:System.SystemException">Win32 エラー コードが返されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.Value">
      <summary>この <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表されるセキュリティ識別子 (SID) に対する大文字の SDDL (Security Descriptor Definition Language) 文字列を返します。</summary>
      <returns>
        <see cref="T:System.Security.Principal.SecurityIdentifier" /> オブジェクトで表される SID に対する大文字の SDDL 文字列。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.TokenAccessLevels">
      <summary>アクセス トークンに関連付けられたユーザー アカウントの特権を定義します。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustDefault">
      <summary>ユーザーは、トークンの既定の所有者、プライマリ グループ、または随意アクセス制御リスト (DACL: Discretionary Access Control List) を変更できます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustGroups">
      <summary>ユーザーは、トークン内のグループの属性を変更できます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges">
      <summary>ユーザーは、トークン内の特権を有効または無効にできます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustSessionId">
      <summary>ユーザーは、トークンのセッション ID を調整できます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AllAccess">
      <summary>ユーザーは、トークンに対するすべての有効なアクセス権を持ちます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AssignPrimary">
      <summary>ユーザーは、プライマリ トークンをプロセスに割り当てることができます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Duplicate">
      <summary>ユーザーは、トークンを複製できます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Impersonate">
      <summary>ユーザーは、クライアントを偽装できます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.MaximumAllowed">
      <summary>
        <see cref="T:System.Security.Principal.TokenAccessLevels" /> 列挙体に割り当てることができる最大値。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Query">
      <summary>ユーザーは、トークンを照会できます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.QuerySource">
      <summary>ユーザーは、トークンのソースを照会できます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Read">
      <summary>ユーザーは、トークンに関する標準の読み取り権限と <see cref="F:System.Security.Principal.TokenAccessLevels.Query" /> 特権を持ちます。</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Write">
      <summary>ユーザーは、トークンに関する標準の書き込み権限、<see cref="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges,F:System.Security.Principal.TokenAccessLevels.AdjustGroups" /> 特権、および <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustDefault" /> 特権を持ちます。</summary>
    </member>
    <member name="T:System.Security.Principal.WellKnownSidType">
      <summary>一般的に使用されるセキュリティ識別子 (SID) を定義します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid">
      <summary>Account Administrators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid">
      <summary>Certificate Administrators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountComputersSid">
      <summary>Account Computer グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountControllersSid">
      <summary>Account Controller グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid">
      <summary>Account Domain Administrator グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid">
      <summary>Account Domain Guests グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid">
      <summary>Account Domain Users グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid">
      <summary>Enterprise Administrators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountGuestSid">
      <summary>Account Guest グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid">
      <summary>Account Kerberos Target グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid">
      <summary>Policy Administrators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid">
      <summary>RAS and IAS Server アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid">
      <summary>Schema Administrators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AnonymousSid">
      <summary>匿名アカウントの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AuthenticatedUserSid">
      <summary>認証済みユーザーの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BatchSid">
      <summary>バッチ プロセスの SID を示します。この SID は、トークンのプロセスがバッチ ジョブとしてログオンすると、このプロセスに追加されます。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAccountOperatorsSid">
      <summary>Account Operators アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAdministratorsSid">
      <summary>Administrator アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAuthorizationAccessSid">
      <summary>Windows Authorization Access グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinBackupOperatorsSid">
      <summary>Backup Operators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinDomainSid">
      <summary>Domain アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinGuestsSid">
      <summary>Guest アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinIncomingForestTrustBuildersSid">
      <summary>入力方向のフォレストの信頼の作成をユーザーに許可する SID を示します。この SID は、フォレストのルート ドメイン内にある Incoming Forest Trust Builders 組み込みグループのメンバーであるユーザーのトークンに追加されます。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinNetworkConfigurationOperatorsSid">
      <summary>Network Operators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceLoggingUsersSid">
      <summary>コンピューターを監視するためのリモート アクセス権を持つユーザー グループと一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceMonitoringUsersSid">
      <summary>このコンピューターのパフォーマンス カウンターのログをスケジュールするためのリモート アクセス権を持つユーザー グループと一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPowerUsersSid">
      <summary>Power Users グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPreWindows2000CompatibleAccessSid">
      <summary>Windows 2000 以前のコンピューターとの互換性があるアカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPrintOperatorsSid">
      <summary>Print Operators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinRemoteDesktopUsersSid">
      <summary>Remote Desktop Users グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinReplicatorSid">
      <summary>Replicator アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinSystemOperatorsSid">
      <summary>System Operators グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinUsersSid">
      <summary>組み込みの Users アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupServerSid">
      <summary>Creator グループ サーバーの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupSid">
      <summary>オブジェクトの Creator グループに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerServerSid">
      <summary>Creator Owner サーバーの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerSid">
      <summary>オブジェクトの所有者または作成者に一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DialupSid">
      <summary>ダイアルアップ アカウントの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DigestAuthenticationSid">
      <summary>Microsoft ダイジェスト認証パッケージがクライアントを認証したときに存在する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.EnterpriseControllersSid">
      <summary>エンタープライズ コントローラーの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.InteractiveSid">
      <summary>Interactive アカウントの SID を示します。この SID は、トークンのプロセスが対話形式でログオンすると、このプロセスに追加されます。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalServiceSid">
      <summary>ローカル サービスに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSid">
      <summary>ローカル SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSystemSid">
      <summary>ローカル システムに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LogonIdsSid">
      <summary>ログオン ID に一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.MaxDefined">
      <summary>
        <see cref="T:System.Security.Principal.WellKnownSidType" /> 列挙体で最大に定義された SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkServiceSid">
      <summary>ネットワーク サービスに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkSid">
      <summary>ネットワーク アカウントの SID を示します。この SID は、トークンのプロセスがネットワークにログオンすると、このプロセスに追加されます。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NTAuthoritySid">
      <summary>Windows NT 権限の SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NtlmAuthenticationSid">
      <summary>Microsoft NTLM 認証パッケージがクライアントを認証したときに存在する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NullSid">
      <summary>null の SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid">
      <summary>選択的認証オプションが有効にされているフォレスト全体でユーザーが認証されたときに存在する SID を示します。この SID が存在する場合、<see cref="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid" /> は存在できません。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ProxySid">
      <summary>プロキシの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RemoteLogonIdSid">
      <summary>リモート ログオンに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RestrictedCodeSid">
      <summary>制限付きコードの SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SChannelAuthenticationSid">
      <summary>セキュア チャネル (SSL/TLS) 認証パッケージがクライアントを認証したときに存在する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SelfSid">
      <summary>Self の SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ServiceSid">
      <summary>サービスの SID を示します。この SID は、トークンのプロセスがサービスとしてログオンすると、このプロセスに追加されます。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.TerminalServerSid">
      <summary>ターミナル サーバー アカウントに一致する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid">
      <summary>選択的認証オプションが有効にされていないフォレスト内または信頼からユーザーが認証されたときに存在する SID を示します。この SID が存在する場合、<see cref="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid" /> は存在できません。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WinBuiltinTerminalServerLicenseServersSid">
      <summary>ターミナル サーバー ライセンスを発行できるサーバー内に存在する SID を示します。</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WorldSid">
      <summary>すべてのユーザーに一致する SID を示します。</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsBuiltInRole">
      <summary>
        <see cref="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.String)" /> で使用する一般的なロールを定めます。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.AccountOperator">
      <summary>アカウント オペレーターは、コンピューター上またはドメイン上でユーザー アカウントを管理します。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Administrator">
      <summary>管理者は、コンピューターまたはドメインに対して完全で、無制限のアクセス権を所有しています。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.BackupOperator">
      <summary>バックアップ オペレーターは、ファイルのバックアップまたは復元の目的だけでセキュリティ制限をオーバーライドできます。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Guest">
      <summary>ゲストには、ユーザーよりも制約があります。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PowerUser">
      <summary>パワー ユーザーは、いくつかの制限付きで、管理者とほぼ同等のアクセス許可を所有しています。したがって、パワー ユーザーは、保証されたアプリケーションだけでなくレガシ アプリケーションも実行できます。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PrintOperator">
      <summary>印刷オペレーターは、プリンターを制御できます。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Replicator">
      <summary>レプリケーターは、ドメイン内のファイルのレプリケーションをサポートします。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.SystemOperator">
      <summary>システム オペレーターは、特定のコンピューターを管理します。</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.User">
      <summary>ユーザーは、偶然または意図的に、システム全体にわたる変更を行うことはできません。したがって、保証されたアプリケーションは実行できますが、多くのレガシ アプリケーションは実行できません。</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsIdentity">
      <summary>Windows ユーザーを表します。</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr)">
      <summary>指定した Windows アカウント トークンによって表されるユーザーを表す、<see cref="T:System.Security.Principal.WindowsIdentity" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="userToken">コードが実行されている対象ユーザーのアカウント トークン。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr,System.String)">
      <summary>指定した Windows アカウント トークンと指定した認証の種類によって表されるユーザーを表す、<see cref="T:System.Security.Principal.WindowsIdentity" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="userToken">コードが実行されている対象ユーザーのアカウント トークン。</param>
      <param name="type">(参照専用。) ユーザーを識別するために使用する認証の種類。詳細については、「解説」を参照してください。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.AccessToken">
      <summary>[セキュリティ クリティカル] この <see cref="T:System.Security.Principal.WindowsIdentity" /> インスタンスのこの <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> を取得します。</summary>
      <returns>
        <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> を返します。</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose">
      <summary>
        <see cref="T:System.Security.Principal.WindowsIdentity" /> によって使用されているすべてのリソースを解放します。</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Security.Principal.WindowsIdentity" /> で使用されているアンマネージ リソースを解放します。オプションとして、マネージ リソースを解放することもできます。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetAnonymous">
      <summary>匿名ユーザーを表すために、コード内で sentinel 値として使用できる <see cref="T:System.Security.Principal.WindowsIdentity" /> オブジェクトを返します。プロパティ値は、Windows オペレーティング システムが使用する組み込み匿名 ID を表しません。</summary>
      <returns>匿名のユーザーを表すオブジェクト。</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent">
      <summary>現在の Windows ユーザーを表す <see cref="T:System.Security.Principal.WindowsIdentity" /> オブジェクトを返します。</summary>
      <returns>現在のユーザーを表すオブジェクト。</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Boolean)">
      <summary>
        <paramref name="ifImpersonating" /> パラメーターの値に応じてスレッドまたはプロセスの Windows ID を表す <see cref="T:System.Security.Principal.WindowsIdentity" /> オブジェクトを返します。</summary>
      <returns>Windows ユーザーを表すオブジェクト。</returns>
      <param name="ifImpersonating">スレッドが現在偽装中の場合にだけ <see cref="T:System.Security.Principal.WindowsIdentity" /> を返すには、true。スレッドが偽装中の場合にスレッドの <see cref="T:System.Security.Principal.WindowsIdentity" /> を返すか、またはスレッドが現在偽装中でない場合にプロセスの <see cref="T:System.Security.Principal.WindowsIdentity" /> を返すには、false。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Security.Principal.TokenAccessLevels)">
      <summary>目的のトークン アクセス レベルを指定して現在の Windows ユーザーを表す <see cref="T:System.Security.Principal.WindowsIdentity" /> オブジェクトを返します。</summary>
      <returns>現在のユーザーを表すオブジェクト。</returns>
      <param name="desiredAccess">列挙値のビットごとの組み合わせ。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Groups">
      <summary>現在の Windows ユーザーが属しているグループを取得します。</summary>
      <returns>現在の Windows ユーザーが属しているグループを表すオブジェクト。</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.ImpersonationLevel">
      <summary>ユーザーの偽装レベルを取得します。</summary>
      <returns>偽装レベルを指定する列挙値の 1 つ。</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsAnonymous">
      <summary>システムによってユーザー アカウントが匿名アカウントとして識別されているかどうかを示す値を取得します。</summary>
      <returns>ユーザー アカウントが匿名アカウントである場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsGuest">
      <summary>システムによってユーザー アカウントが <see cref="F:System.Security.Principal.WindowsAccountType.Guest" /> アカウントとして識別されているかどうかを示す値を取得します。</summary>
      <returns>ユーザー アカウントが <see cref="F:System.Security.Principal.WindowsAccountType.Guest" /> アカウントである場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsSystem">
      <summary>システムによってユーザー アカウントが <see cref="F:System.Security.Principal.WindowsAccountType.System" /> アカウントとして識別されているかどうかを示す値を取得します。</summary>
      <returns>ユーザー アカウントが <see cref="F:System.Security.Principal.WindowsAccountType.System" /> アカウントである場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Owner">
      <summary>トークン所有者のセキュリティ識別子 (SID) を取得します。</summary>
      <returns>トークン所有者のオブジェクト。</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)">
      <summary>指定したアクションを、偽装した Windows ID として実行します。偽装したメソッド呼び出しを使用して <see cref="T:System.Security.Principal.WindowsImpersonationContext" /> で関数を実行するのではなく、<see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> を使用して関数をパラメーターとして直接指定することができます。</summary>
      <param name="safeAccessTokenHandle">偽装した Windows ID の SafeAccessTokenHandle。</param>
      <param name="action">実行する System.Action。</param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated``1(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Func{``0})">
      <summary>指定した関数を、偽装した Windows ID として実行します。偽装したメソッド呼び出しを使用して <see cref="T:System.Security.Principal.WindowsImpersonationContext" /> で関数を実行するのではなく、<see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> を使用して関数をパラメーターとして直接指定することができます。</summary>
      <returns>関数の結果を返します。</returns>
      <param name="safeAccessTokenHandle">偽装した Windows ID の SafeAccessTokenHandle。</param>
      <param name="func">実行する System.Func。</param>
      <typeparam name="T">関数によって使用され、関数によって返されるオブジェクトの型。</typeparam>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.User">
      <summary>ユーザーのセキュリティ識別子 (SID) を取得します。</summary>
      <returns>ユーザーのオブジェクト。</returns>
    </member>
    <member name="T:System.Security.Principal.WindowsPrincipal">
      <summary>コードによって Windows ユーザーの Windows グループ メンバーシップを確認できるようにします。</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.#ctor(System.Security.Principal.WindowsIdentity)">
      <summary>指定した <see cref="T:System.Security.Principal.WindowsIdentity" /> オブジェクトを使用して、<see cref="T:System.Security.Principal.WindowsPrincipal" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="ntIdentity">
        <see cref="T:System.Security.Principal.WindowsPrincipal" /> の新しいインスタンスの生成元となるオブジェクト。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ntIdentity" /> は null なので、</exception>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Int32)">
      <summary>現在のプリンシパルが、指定した相対識別子 (RID) の Windows ユーザー グループに属しているかどうかを確認します。</summary>
      <returns>現在のプリンシパルが、指定した Windows ユーザー グループのメンバーである、つまり特定のロール内にある場合は true。それ以外の場合は false。</returns>
      <param name="rid">プリンシパルのメンバーシップ状態を確認する Windows ユーザー グループの RID。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.SecurityIdentifier)">
      <summary>現在のプリンシパルが、指定したセキュリティ識別子 (SID) の Windows ユーザー グループに属しているかどうかを確認します。</summary>
      <returns>現在のプリンシパルが、指定した Windows ユーザー グループのメンバーである場合は true。それ以外の場合は false。</returns>
      <param name="sid">Windows ユーザー グループを一意に識別する <see cref="T:System.Security.Principal.SecurityIdentifier" />。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sid" /> は null なので、</exception>
      <exception cref="T:System.Security.SecurityException">Windows は Win32 エラーを返します。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.WindowsBuiltInRole)">
      <summary>現在のプリンシパルが、指定した <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> の Windows ユーザー グループに属しているかどうかを確認します。</summary>
      <returns>現在のプリンシパルが、指定した Windows ユーザー グループのメンバーである場合は true。それ以外の場合は false。</returns>
      <param name="role">
        <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> 値のいずれか。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="role" /> が有効な <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> 値ではありません。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
  </members>
</doc>