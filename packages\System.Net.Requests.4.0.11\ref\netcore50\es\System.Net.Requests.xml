﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Requests</name>
  </assembly>
  <members>
    <member name="T:System.Net.HttpWebRequest">
      <summary>Proporciona una implementación específica de HTTP de la clase <see cref="T:System.Net.WebRequest" />.</summary>
    </member>
    <member name="M:System.Net.HttpWebRequest.Abort">
      <summary>Cancela una solicitud de un recurso de Internet.</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Accept">
      <summary>Obtiene o establece el valor del encabezado HTTP Accept.</summary>
      <returns>Valor del encabezado HTTP Accept.El valor predeterminado es null.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.AllowReadStreamBuffering">
      <summary>Obtiene o establece un valor que indica si los datos recibidos del recurso de Internet deben almacenarse en el búfer.</summary>
      <returns>truepara almacenar en búfer recibido del recurso de Internet; de lo contrario, false.Es true para habilitar el almacenamiento en búfer de los datos recibidos del recurso de Internet; es false para deshabilitar el almacenamiento en búfer.De manera predeterminada, es true.</returns>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>Inicia una solicitud asincrónica de un objeto <see cref="T:System.IO.Stream" /> que se va a utilizar para escribir datos.</summary>
      <returns>
        <see cref="T:System.IAsyncResult" /> que hace referencia a la solicitud asincrónica.</returns>
      <param name="callback">Delegado <see cref="T:System.AsyncCallback" />. </param>
      <param name="state">Objeto de estado de esta solicitud. </param>
      <exception cref="T:System.Net.ProtocolViolationException">La propiedad <see cref="P:System.Net.HttpWebRequest.Method" /> es GET o HEAD.o bien <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> es true, <see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> es false, <see cref="P:System.Net.HttpWebRequest.ContentLength" /> es -1, <see cref="P:System.Net.HttpWebRequest.SendChunked" /> es false y <see cref="P:System.Net.HttpWebRequest.Method" /> es POST o PUT. </exception>
      <exception cref="T:System.InvalidOperationException">La secuencia la utiliza una llamada anterior a <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />.o bien <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> se establece en un valor y <see cref="P:System.Net.HttpWebRequest.SendChunked" /> es false.o bien El grupo de subprocesos se queda sin subprocesos. </exception>
      <exception cref="T:System.NotSupportedException">El validador de caché de solicitud indicó que la respuesta para esta solicitud se puede obtener de la caché; sin embargo, las solicitudes que escriben datos no deben utilizar la caché.Esta excepción puede aparecer si se utiliza un validador de caché personalizado que se implementa incorrectamente.</exception>
      <exception cref="T:System.Net.WebException">Se llamó anteriormente a <see cref="M:System.Net.HttpWebRequest.Abort" />. </exception>
      <exception cref="T:System.ObjectDisposedException">En una aplicación de .NET Compact Framework, una secuencia de solicitudes con longitud de contenido cero no se obtuvo y se cerró correctamente.Para obtener más información sobre cómo controlar las solicitudes de longitud de contenido cero, vea Network Programming in the .NET Compact Framework.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>Inicia una solicitud asincrónica de un recurso de Internet.</summary>
      <returns>
        <see cref="T:System.IAsyncResult" /> que hace referencia a la solicitud asincrónica de una respuesta.</returns>
      <param name="callback">Delegado <see cref="T:System.AsyncCallback" />. </param>
      <param name="state">Objeto de estado de esta solicitud. </param>
      <exception cref="T:System.InvalidOperationException">La secuencia está en uso por una llamada anterior al método <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />.o bien <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> se establece en un valor y <see cref="P:System.Net.HttpWebRequest.SendChunked" /> es false.o bien El grupo de subprocesos se queda sin subprocesos. </exception>
      <exception cref="T:System.Net.ProtocolViolationException">
        <see cref="P:System.Net.HttpWebRequest.Method" /> es GET o HEAD, y <see cref="P:System.Net.HttpWebRequest.ContentLength" /> es mayor que cero o <see cref="P:System.Net.HttpWebRequest.SendChunked" /> es true.o bien <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> es true, <see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> es false, y <see cref="P:System.Net.HttpWebRequest.ContentLength" /> es -1, <see cref="P:System.Net.HttpWebRequest.SendChunked" /> es false y <see cref="P:System.Net.HttpWebRequest.Method" /> es POST o PUT.o bien <see cref="T:System.Net.HttpWebRequest" /> tiene un cuerpo de entidad pero se llama al método <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> sin llamar al método <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />. o bien <see cref="P:System.Net.HttpWebRequest.ContentLength" /> es mayor que el cero, pero la aplicación no escribe todos los datos prometidos.</exception>
      <exception cref="T:System.Net.WebException">Se llamó anteriormente a <see cref="M:System.Net.HttpWebRequest.Abort" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContentType">
      <summary>Obtiene o establece el valor del encabezado HTTP Content-type.</summary>
      <returns>Valor del encabezado HTTP Content-type.El valor predeterminado es null.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContinueTimeout">
      <summary>Obtiene o establece el tiempo de espera, en milisegundos, para esperar hasta que se reciba 100-Continue del servidor. </summary>
      <returns>El tiempo de espera, en milisegundos, que se espera hasta que se recibe 100-Continue. </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.CookieContainer">
      <summary>Obtiene o establece las cookies asociadas a la solicitud.</summary>
      <returns>
        <see cref="T:System.Net.CookieContainer" /> que contiene las cookies asociadas a esta solicitud.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Credentials">
      <summary>Obtiene o establece la información de autenticación para la solicitud.</summary>
      <returns>
        <see cref="T:System.Net.ICredentials" /> que contiene las credenciales de autenticación asociadas a la solicitud.De manera predeterminada, es null.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>Finaliza una solicitud asincrónica para utilizar un objeto <see cref="T:System.IO.Stream" /> para escribir datos.</summary>
      <returns>
        <see cref="T:System.IO.Stream" /> que se utiliza para escribir los datos de la solicitud.</returns>
      <param name="asyncResult">Solicitud pendiente de un flujo. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" />is null. </exception>
      <exception cref="T:System.IO.IOException">La solicitud no se completó y no hay ninguna secuencia disponible. </exception>
      <exception cref="T:System.ArgumentException">La instancia actual no devolvió <paramref name="asyncResult" /> de una llamada a <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />. </exception>
      <exception cref="T:System.InvalidOperationException">Se llamó anteriormente a este método por medio de <paramref name="asyncResult" />. </exception>
      <exception cref="T:System.Net.WebException">Se llamó anteriormente a <see cref="M:System.Net.HttpWebRequest.Abort" />.o bien Se ha producido un error al procesar la solicitud. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>Finaliza una solicitud asincrónica de un recurso de Internet.</summary>
      <returns>
        <see cref="T:System.Net.WebResponse" /> que contiene la respuesta del recurso de Internet.</returns>
      <param name="asyncResult">Solicitud de una respuesta pendiente. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" />is null. </exception>
      <exception cref="T:System.InvalidOperationException">Se llamó anteriormente a este método por medio de <paramref name="asyncResult." />.o bien La propiedad <see cref="P:System.Net.HttpWebRequest.ContentLength" /> es mayor que 0, aunque no se han escrito los datos en la secuencia de la solicitud. </exception>
      <exception cref="T:System.Net.WebException">Se llamó anteriormente a <see cref="M:System.Net.HttpWebRequest.Abort" />.o bien Se ha producido un error al procesar la solicitud. </exception>
      <exception cref="T:System.ArgumentException">La instancia actual no devolvió <paramref name="asyncResult" /> de una llamada a <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.HaveResponse">
      <summary>Obtiene un valor que indica si se ha recibido una respuesta de un recurso de Internet.</summary>
      <returns>Es true si se ha recibido una respuesta; de lo contrario, es false.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Headers">
      <summary>Especifica una colección de los pares nombre/valor que componen los encabezados HTTP.</summary>
      <returns>
        <see cref="T:System.Net.WebHeaderCollection" /> que contiene los pares nombre-valor que componen los encabezados de la solicitud HTTP.</returns>
      <exception cref="T:System.InvalidOperationException">La solicitud se inició llamando al método <see cref="M:System.Net.HttpWebRequest.GetRequestStream" />, <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />, <see cref="M:System.Net.HttpWebRequest.GetResponse" /> o <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Method">
      <summary>Obtiene o establece el método para la solicitud.</summary>
      <returns>Método de solicitud que se debe utilizar para establecer contacto con el recurso de Internet.El valor predeterminado es GET.</returns>
      <exception cref="T:System.ArgumentException">No se proporciona ningún método.o bien La cadena de método contiene caracteres no válidos. </exception>
    </member>
    <member name="P:System.Net.HttpWebRequest.RequestUri">
      <summary>Obtiene el identificador URI original de la solicitud.</summary>
      <returns>Un <see cref="T:System.Uri" /> que contiene el URI del recurso de Internet pasado a la <see cref="M:System.Net.WebRequest.Create(System.String)" /> método.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.SupportsCookieContainer">
      <summary>Obtiene un valor que indica si la solicitud admite un <see cref="T:System.Net.CookieContainer" />.</summary>
      <returns>trueSi la solicitud proporciona compatibilidad para una <see cref="T:System.Net.CookieContainer" />; de lo contrario, false.trueSi un <see cref="T:System.Net.CookieContainer" /> se admite; de lo contrario, false. </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.UseDefaultCredentials">
      <summary>Obtiene o establece un valor <see cref="T:System.Boolean" /> que controla si se envían las credenciales predeterminadas con las solicitudes.</summary>
      <returns>Es true si se utilizan las credenciales predeterminadas; en cualquier otro caso, es false.El valor predeterminado es false.</returns>
      <exception cref="T:System.InvalidOperationException">Se intentó establecer esta propiedad después de que se enviara la solicitud.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="USERNAME" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.HttpWebResponse">
      <summary>Proporciona una implementación específica de HTTP de la clase <see cref="T:System.Net.WebResponse" />.</summary>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentLength">
      <summary>Obtiene la longitud del contenido devuelto por la solicitud.</summary>
      <returns>Número de bytes devueltos por la solicitud.La longitud del contenido no incluye la información de encabezado.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentType">
      <summary>Obtiene el tipo de contenido de la respuesta.</summary>
      <returns>Cadena que contiene el tipo de contenido de la respuesta.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Cookies">
      <summary>Obtiene o establece las cookies asociadas a esta respuesta.</summary>
      <returns>Un objeto <see cref="T:System.Net.CookieCollection" /> que contiene las cookies asociadas a esta respuesta.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="M:System.Net.HttpWebResponse.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa <see cref="T:System.Net.HttpWebResponse" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados. </param>
    </member>
    <member name="M:System.Net.HttpWebResponse.GetResponseStream">
      <summary>Obtiene la secuencia usada para leer el cuerpo de la respuesta del servidor.</summary>
      <returns>
        <see cref="T:System.IO.Stream" /> que contiene el cuerpo de la respuesta.</returns>
      <exception cref="T:System.Net.ProtocolViolationException">No hay secuencia de respuesta. </exception>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebResponse.Headers">
      <summary>Obtiene los encabezados asociados con esta respuesta del servidor.</summary>
      <returns>
        <see cref="T:System.Net.WebHeaderCollection" /> que contiene la información de encabezado devuelta con la respuesta.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Method">
      <summary>Obtiene el método usado para devolver la respuesta.</summary>
      <returns>Cadena que contiene el método HTTP usado para devolver la respuesta.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ResponseUri">
      <summary>Obtiene el URI del recurso de Internet que respondió a la solicitud.</summary>
      <returns>Objeto <see cref="T:System.Uri" /> que contiene el URI del recurso de Internet que respondió a la solicitud.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusCode">
      <summary>Obtiene el estado de la respuesta.</summary>
      <returns>Uno de los valores de <see cref="T:System.Net.HttpStatusCode" />.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusDescription">
      <summary>Obtiene la descripción del estado devuelto con la respuesta.</summary>
      <returns>Cadena que describe el estado de la respuesta.</returns>
      <exception cref="T:System.ObjectDisposedException">Se ha eliminado la instancia actual. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.SupportsHeaders">
      <summary>Obtiene un valor que indica si se admiten encabezados.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.Es true si se admiten encabezados; de lo contrario, es false.</returns>
    </member>
    <member name="T:System.Net.IWebRequestCreate">
      <summary>Proporciona la interfaz base para crear instancias de <see cref="T:System.Net.WebRequest" />.</summary>
    </member>
    <member name="M:System.Net.IWebRequestCreate.Create(System.Uri)">
      <summary>Crea una instancia de <see cref="T:System.Net.WebRequest" />.</summary>
      <returns>Instancia de <see cref="T:System.Net.WebRequest" />.</returns>
      <param name="uri">Identificador de recursos uniforme (URI) del recurso Web. </param>
      <exception cref="T:System.NotSupportedException">Esta instancia de <see cref="T:System.Net.IWebRequestCreate" /> no admite el esquema de solicitud especificado en <paramref name="uri" />. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> es null. </exception>
      <exception cref="T:System.UriFormatException">En las API de .NET para aplicaciones de la Tienda Windows o en la Biblioteca de clases portable, encuentre la excepción de la clase base, <see cref="T:System.FormatException" />, en su lugar.El identificador URI especificado en <paramref name="uri" /> no es válido. </exception>
    </member>
    <member name="T:System.Net.ProtocolViolationException">
      <summary>Excepción que se produce cuando se produce un error mientras se utiliza un protocolo de red.</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.ProtocolViolationException" />.</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.ProtocolViolationException" /> con el mensaje especificado.</summary>
      <param name="message">Cadena con el mensaje de error. </param>
    </member>
    <member name="T:System.Net.WebException">
      <summary>Excepción que se produce cuando se produce un error al obtener acceso a la red mediante un protocolo conectable.</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.WebException" />.</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.WebException" /> con el mensaje de error especificado.</summary>
      <param name="message">Texto del mensaje de error. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.WebException" /> con el mensaje de error y la excepción anidada especificados.</summary>
      <param name="message">Texto del mensaje de error. </param>
      <param name="innerException">Excepción anidada. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception,System.Net.WebExceptionStatus,System.Net.WebResponse)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.WebException" /> con el mensaje de error, la excepción anidada, el estado y la respuesta especificados.</summary>
      <param name="message">Texto del mensaje de error. </param>
      <param name="innerException">Excepción anidada. </param>
      <param name="status">Uno de los valores de <see cref="T:System.Net.WebExceptionStatus" />. </param>
      <param name="response">Instancia de <see cref="T:System.Net.WebResponse" /> que contiene la respuesta del host remoto. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Net.WebExceptionStatus)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.WebException" /> con el mensaje de error y el estado especificados.</summary>
      <param name="message">Texto del mensaje de error. </param>
      <param name="status">Uno de los valores de <see cref="T:System.Net.WebExceptionStatus" />. </param>
    </member>
    <member name="P:System.Net.WebException.Response">
      <summary>Obtiene la respuesta que devolvió el host remoto.</summary>
      <returns>Si hay una respuesta disponible en el recurso de Internet, se trata de una instancia de <see cref="T:System.Net.WebResponse" /> que contiene la respuesta de error de un recurso de Internet; en caso contrario, es null.</returns>
    </member>
    <member name="P:System.Net.WebException.Status">
      <summary>Obtiene el estado de la respuesta.</summary>
      <returns>Uno de los valores de <see cref="T:System.Net.WebExceptionStatus" />.</returns>
    </member>
    <member name="T:System.Net.WebExceptionStatus">
      <summary>Define códigos de estado para la clase <see cref="T:System.Net.WebException" />.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.ConnectFailure">
      <summary>No se ha podido establecer contacto con el punto de servicio remoto en el nivel de transporte.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.MessageLengthLimitExceeded">
      <summary>Se recibió un mensaje que superaba el límite especificado al enviar una solicitud o recibir una respuesta del servidor.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Pending">
      <summary>Está pendiente una solicitud asincrónica interna.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.RequestCanceled">
      <summary>La solicitud se canceló, se llamó al método <see cref="M:System.Net.WebRequest.Abort" /> o se produjo un error no clasificable.Éste es el valor predeterminado de <see cref="P:System.Net.WebException.Status" />.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.SendFailure">
      <summary>No se ha podido enviar una solicitud completa al servidor remoto.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Success">
      <summary>No se ha encontrado ningún error.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.UnknownError">
      <summary>Se ha producido una excepción de tipo desconocido.</summary>
    </member>
    <member name="T:System.Net.WebRequest">
      <summary>Realiza una solicitud a un identificador uniforme de recursos (URI).Esta es una clase abstract.</summary>
    </member>
    <member name="M:System.Net.WebRequest.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.WebRequest" />.</summary>
    </member>
    <member name="M:System.Net.WebRequest.Abort">
      <summary>Anula la solicitud </summary>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>Cuando se reemplaza en una clase descendiente, proporciona una versión asincrónica del método <see cref="M:System.Net.WebRequest.GetRequestStream" />.</summary>
      <returns>
        <see cref="T:System.IAsyncResult" /> que hace referencia a la solicitud asincrónica.</returns>
      <param name="callback">Delegado <see cref="T:System.AsyncCallback" />. </param>
      <param name="state">Objeto que contiene información de estado para esta solicitud asincrónica. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>Cuando se reemplaza en una clase descendiente, comienza una solicitud asincrónica de un recurso de Internet.</summary>
      <returns>
        <see cref="T:System.IAsyncResult" /> que hace referencia a la solicitud asincrónica.</returns>
      <param name="callback">Delegado <see cref="T:System.AsyncCallback" />. </param>
      <param name="state">Objeto que contiene información de estado para esta solicitud asincrónica. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="P:System.Net.WebRequest.ContentType">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene o establece el tipo de contenido de los datos solicitados que se envían.</summary>
      <returns>Tipo de contenido de los datos de la solicitud.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.String)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Net.WebRequest" /> para el esquema URI especificado.</summary>
      <returns>Descendiente <see cref="T:System.Net.WebRequest" /> para un esquema URI específico.</returns>
      <param name="requestUriString">URI que identifica el recurso de Internet. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> has not been registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.Uri)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Net.WebRequest" /> para el esquema URI especificado.</summary>
      <returns>Descendiente <see cref="T:System.Net.WebRequest" /> para el esquema URI especificado.</returns>
      <param name="requestUri">
        <see cref="T:System.Uri" /> que contiene el identificador URI del recurso solicitado. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is not registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.String)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Net.HttpWebRequest" /> para la cadena de URI especificada.</summary>
      <returns>Devuelve <see cref="T:System.Net.HttpWebRequest" />.Instancia de <see cref="T:System.Net.HttpWebRequest" /> para la cadena de URI concreta.</returns>
      <param name="requestUriString">Cadena de URI que identifica el recurso de Internet. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.Uri)">
      <summary>Inicializa una nueva instancia de <see cref="T:System.Net.HttpWebRequest" /> para el URI especificado.</summary>
      <returns>Devuelve <see cref="T:System.Net.HttpWebRequest" />.Instancia de <see cref="T:System.Net.HttpWebRequest" /> para la cadena de URI concreta.</returns>
      <param name="requestUri">URI que identifica el recurso de Internet.</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUri" /> is not a valid URI. </exception>
    </member>
    <member name="P:System.Net.WebRequest.Credentials">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene o establece las credenciales de red utilizadas para autenticar la solicitud con el recurso de Internet.</summary>
      <returns>
        <see cref="T:System.Net.ICredentials" /> que contiene las credenciales de autenticación asociadas a la solicitud.De manera predeterminada, es null.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.DefaultWebProxy">
      <summary>Obtiene o establece el proxy HTTP global.</summary>
      <returns>Objeto <see cref="T:System.Net.IWebProxy" /> usado en cada llamada a las instancias de <see cref="T:System.Net.WebRequest" />.</returns>
    </member>
    <member name="M:System.Net.WebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>Cuando se reemplaza en una clase descendiente, devuelve <see cref="T:System.IO.Stream" /> para escribir datos en el recurso de Internet.</summary>
      <returns>
        <see cref="T:System.IO.Stream" /> donde se escribirán datos.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> que hace referencia a una solicitud pendiente de una secuencia. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>Cuando se reemplaza en una clase descendiente, devuelve <see cref="T:System.Net.WebResponse" />.</summary>
      <returns>
        <see cref="T:System.Net.WebResponse" /> que contiene una respuesta a la solicitud de Internet.</returns>
      <param name="asyncResult">
        <see cref="T:System.IAsyncResult" /> que hace referencia a una solicitud de respuesta pendiente. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.GetRequestStreamAsync">
      <summary>Cuando se invalida en una clase descendiente, devuelve un objeto <see cref="T:System.IO.Stream" /> para escribir datos en el recurso de Internet como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.WebRequest.GetResponseAsync">
      <summary>Cuando se invalida en una clase descendiente, devuelve una respuesta a una solicitud de Internet como una operación asincrónica.</summary>
      <returns>Devuelve <see cref="T:System.Threading.Tasks.Task`1" />.Objeto de tarea que representa la operación asincrónica.</returns>
    </member>
    <member name="P:System.Net.WebRequest.Headers">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene o establece la colección de pares de nombre/valor de encabezado asociados a la solicitud.</summary>
      <returns>
        <see cref="T:System.Net.WebHeaderCollection" /> que contiene los pares de nombre/valor de encabezado que están asociados a esta solicitud.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Method">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene o establece el método de protocolo que se va a utilizar en esta solicitud.</summary>
      <returns>Método de protocolo que se utilizará en esta solicitud.</returns>
      <exception cref="T:System.NotImplementedException">If the property is not overridden in a descendant class, any attempt is made to get or set the property. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Proxy">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene o establece el proxy de red que se va a utilizar para tener acceso a este recurso de Internet.</summary>
      <returns>
        <see cref="T:System.Net.IWebProxy" /> que se va a utilizar para tener acceso al recurso de Internet.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.RegisterPrefix(System.String,System.Net.IWebRequestCreate)">
      <summary>Registra un descendiente <see cref="T:System.Net.WebRequest" /> para el identificador URI especificado.</summary>
      <returns>Es true si el registro es correcto; en caso contrario, es false.</returns>
      <param name="prefix">Identificador URI o prefijo URI completo que resuelve el descendiente de <see cref="T:System.Net.WebRequest" />. </param>
      <param name="creator">Método de creación al que llama <see cref="T:System.Net.WebRequest" /> para crear el descendiente <see cref="T:System.Net.WebRequest" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prefix" /> is null-or- <paramref name="creator" /> is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.RequestUri">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene el identificador URI del recurso de Internet asociado a la solicitud.</summary>
      <returns>
        <see cref="T:System.Uri" /> que representa el recurso asociado a la solicitud </returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.UseDefaultCredentials">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene o establece un valor <see cref="T:System.Boolean" /> que controla si se envían <see cref="P:System.Net.CredentialCache.DefaultCredentials" /> con las solicitudes.</summary>
      <returns>Es true si se utilizan las credenciales predeterminadas; en caso contrario, es false.El valor predeterminado es false.</returns>
      <exception cref="T:System.InvalidOperationException">You attempted to set this property after the request was sent.</exception>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.WebResponse">
      <summary>Proporciona una respuesta desde un identificador de recursos uniforme (URI).Esta es una clase abstract.</summary>
    </member>
    <member name="M:System.Net.WebResponse.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Net.WebResponse" />.</summary>
    </member>
    <member name="P:System.Net.WebResponse.ContentLength">
      <summary>Cuando se reemplaza en una clase descendiente, obtiene o establece la longitud del contenido de los datos recibidos.</summary>
      <returns>Número de bytes devuelto desde el recurso de Internet.</returns>
      <exception cref="T:System.NotSupportedException">Se intenta por todos los medios obtener o establecer la propiedad, cuando la propiedad no se reemplaza en una clase descendiente. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ContentType">
      <summary>Cuando se realizan omisiones en una clase derivada, obtiene o establece el tipo de contenido de los datos recibidos.</summary>
      <returns>Cadena que contiene el tipo de contenido de la respuesta.</returns>
      <exception cref="T:System.NotSupportedException">Se intenta por todos los medios obtener o establecer la propiedad, cuando la propiedad no se reemplaza en una clase descendiente. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebResponse.Dispose">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.WebResponse" />.</summary>
    </member>
    <member name="M:System.Net.WebResponse.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que usa el objeto <see cref="T:System.Net.WebResponse" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar tanto recursos administrados como no administrados; es false para liberar únicamente recursos no administrados. </param>
    </member>
    <member name="M:System.Net.WebResponse.GetResponseStream">
      <summary>Cuando se reemplaza en una clase descendiente, se devuelve el flujo de datos desde el recurso de Internet.</summary>
      <returns>Instancia de la clase <see cref="T:System.IO.Stream" /> para leer los datos procedentes del recurso de Internet.</returns>
      <exception cref="T:System.NotSupportedException">Se intenta por todos los medios tener acceso al método, cuando el método no se reemplaza en una clase descendiente. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.Headers">
      <summary>Cuando se realizan omisiones en una clase derivada, obtiene una colección de pares de nombre-valor de encabezado asociados a esta solicitud.</summary>
      <returns>Instancia de la clase <see cref="T:System.Net.WebHeaderCollection" /> que contiene los valores de encabezado asociados a esta respuesta.</returns>
      <exception cref="T:System.NotSupportedException">Se intenta por todos los medios obtener o establecer la propiedad, cuando la propiedad no se reemplaza en una clase descendiente. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ResponseUri">
      <summary>Cuando se reemplaza en una clase derivada, obtiene el identificador URI del recurso de Internet que respondió a la solicitud.</summary>
      <returns>Instancia de la clase <see cref="T:System.Uri" /> que contiene el identificador URI del recurso de Internet que respondió a la solicitud.</returns>
      <exception cref="T:System.NotSupportedException">Se intenta por todos los medios obtener o establecer la propiedad, cuando la propiedad no se reemplaza en una clase descendiente. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.SupportsHeaders">
      <summary>Obtiene un valor que indica si se admiten encabezados.</summary>
      <returns>Devuelve <see cref="T:System.Boolean" />.Es true si se admiten encabezados; de lo contrario, es false.</returns>
    </member>
  </members>
</doc>