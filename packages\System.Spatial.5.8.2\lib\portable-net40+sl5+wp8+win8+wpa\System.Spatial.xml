<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Spatial</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Data.Edm.PlatformHelper">
            <summary>
            Helper methods that provide a common API surface on all platforms.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Edm.PlatformHelper.EmptyTypes">
            <summary>
            Use this instead of Type.EmptyTypes.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Edm.PlatformHelper.UriSchemeHttp">
            <summary>
            Replacement for Uri.UriSchemeHttp, which does not exist on.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Edm.PlatformHelper.UriSchemeHttps">
            <summary>
            Replacement for Uri.UriSchemeHttps, which does not exist on.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetAssembly(System.Type)">
            <summary>
            Replacement for Type.Assembly.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsValueType(System.Type)">
            <summary>
            Replacement for Type.IsValueType.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsGenericParameter(System.Type)">
            <summary>
            Replacement for Type.IsGenericParameter.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsAbstract(System.Type)">
            <summary>
            Replacement for Type.IsAbstract.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsGenericType(System.Type)">
            <summary>
            Replacement for Type.IsGenericType.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsGenericTypeDefinition(System.Type)">
            <summary>
            Replacement for Type.IsGenericTypeDefinition.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsVisible(System.Type)">
            <summary>
            Replacement for Type.IsVisible.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsInterface(System.Type)">
            <summary>
            Replacement for Type.IsInterface.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsClass(System.Type)">
            <summary>
            Replacement for Type.IsClass.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsEnum(System.Type)">
            <summary>
            Replacement for Type.IsEnum.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetBaseType(System.Type)">
            <summary>
            Replacement for Type.BaseType.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.ContainsGenericParameters(System.Type)">
            <summary>
            Replacement for Type.ContainsGenericParameters.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.AsReadOnly``1(``0[])">
            <summary>
            Replacement for Array.AsReadOnly(T[]).
            </summary>
            <typeparam name="T">Type of items in the array.</typeparam>
            <param name="array">Array to use to create the ReadOnlyCollection.</param>
            <returns>ReadOnlyCollection containing the specified array items.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.ConvertStringToDateTime(System.String)">
            <summary>
            Converts a string to a DateTime.
            </summary>
            <param name="text">String to be converted.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.ConvertStringToDateTimeOffset(System.String)">
            <summary>
            Converts a string to a DateTimeOffset.
            </summary>
            <param name="text">String to be converted.</param>
            <returns>See documentation for method being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.AddSecondsPaddingIfMissing(System.String)">
            <summary>
            Adds the seconds padding as zeros to the date time string if seconds part is missing.
            </summary>
            <param name="text">String that needs seconds padding</param>
            <returns>DateTime string after adding seconds padding</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.ConvertDateTimeToStringInternal(System.DateTime)">
            <summary>
            Converts the DateTime to a string, internal method.
            </summary>
            <param name="dateTime">DateTime to convert to String.</param>
            <returns>Converted String.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.ConvertDateTimeToString(System.DateTime)">
            <summary>
            Converts a DateTime to a string.
            </summary>
            <param name="dateTime">DateTime to be converted.</param>
            <returns>See documentation for property being accessed in the body of the method.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetTypeOrThrow(System.String)">
            <summary>
            Gets the specified type.
            </summary>
            <param name="typeName">Name of the type to get.</param>
            <exception cref="T:System.TypeLoadException">Throws if the type could not be found.</exception>
            <returns>Type instance that represents the specified type name.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetTypeCode(System.Type)">
            <summary>
            Gets the TypeCode for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>TypeCode representing the specified type.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetUnicodeCategory(System.Char)">
            <summary>
            Gets the Unicode Category of the specified character.
            </summary>
            <param name="c">Character to get category of.</param>
            <returns>Category of the character.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsProperty(System.Reflection.MemberInfo)">
            <summary>
            Replacement for usage of MemberInfo.MemberType property.
            </summary>
            <param name="member">MemberInfo on which to access this method.</param>
            <returns>True if the specified member is a property, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsPrimitive(System.Type)">
            <summary>
            Replacement for usage of Type.IsPrimitive property.
            </summary>
            <param name="type">Type on which to access this method.</param>
            <returns>True if the specified type is primitive, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsSealed(System.Type)">
            <summary>
            Replacement for usage of Type.IsSealed property.
            </summary>
            <param name="type">Type on which to access this method.</param>
            <returns>True if the specified type is sealed, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.IsMethod(System.Reflection.MemberInfo)">
            <summary>
            Replacement for usage of MemberInfo.MemberType property.
            </summary>
            <param name="member">MemberInfo on which to access this method.</param>
            <returns>True if the specified member is a method, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.AreMembersEqual(System.Reflection.MemberInfo,System.Reflection.MemberInfo)">
            <summary>
            Compares two methodInfos and returns true if they represent the same method.
            Need this for Windows Phone as the method Infos of the same method are not always instance equivalent.
            </summary>
            <param name="member1">MemberInfo to compare.</param>
            <param name="member2">MemberInfo to compare.</param>
            <returns>True if the specified member is a method, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetPublicProperties(System.Type,System.Boolean)">
            <summary>
            Gets public properties for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="instanceOnly">True if method should return only instance properties, false if it should return both instance and static properties.</param>
            <returns>Enumerable of public properties for the type.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetPublicProperties(System.Type,System.Boolean,System.Boolean)">
            <summary>
            Gets public properties for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="instanceOnly">True if method should return only instance properties, false if it should return both instance and static properties.</param>
            <param name="declaredOnly">True if method should return only properties that are declared on the type, false if it should return properties declared on the type as well as those inherited from any base types.</param>
            <returns>Enumerable of public properties for the type.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetInstanceConstructors(System.Type,System.Boolean)">
            <summary>
            Gets instance constructors for the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="isPublic">True if method should return only public constructors, false if it should return only non-public constructors.</param>
            <returns>Enumerable of instance constructors for the specified type.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetInstanceConstructor(System.Type,System.Boolean,System.Type[])">
            <summary>
            Gets a instance constructor for the type that takes the specified argument types.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="isPublic">True if method should search only public constructors, false if it should search only non-public constructors.</param>
            <param name="argTypes">Array of argument types for the constructor.</param>
            <returns>ConstructorInfo for the constructor with the specified characteristics if found, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.TryGetMethod(System.Type,System.String,System.Type[],System.Reflection.MethodInfo@)">
            <summary>
            Tries to the get method from the type, returns null if not found.
            </summary>
            <param name="type">The type.</param>
            <param name="name">The name.</param>
            <param name="parameterTypes">The parameter types.</param>
            <returns>Returns True if found.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetMethod(System.Type,System.String,System.Boolean,System.Boolean)">
            <summary>
            Gets a method on the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of the method on the type.</param>
            <param name="isPublic">True if method should search only public methods, false if it should search only non-public methods.</param>
            <param name="isStatic">True if method should search only static methods, false if it should search only instance methods.</param>
            <returns>MethodInfo for the method with the specified characteristics if found, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetMethod(System.Type,System.String,System.Type[],System.Boolean,System.Boolean)">
            <summary>
            Gets a method on the specified type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <param name="name">Name of the method on the type.</param>
            <param name="types">Argument types for the method.</param>
            <param name="isPublic">True if method should search only public methods, false if it should search only non-public methods.</param>
            <param name="isStatic">True if method should search only static methods, false if it should search only instance methods.</param>
            <returns>MethodInfo for the method with the specified characteristics if found, otherwise null.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetPublicStaticMethods(System.Type)">
            <summary>
            Gets all public static methods for a type.
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>Enumerable of all public static methods for the specified type.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.GetNonPublicNestedTypes(System.Type)">
            <summary>
            Replacement for Type.GetNestedTypes(BindingFlags.NonPublic)
            </summary>
            <param name="type">Type on which to call this helper method.</param>
            <returns>All types nested in the current type</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.CheckTypeArgs(System.Reflection.ConstructorInfo,System.Type[])">
            <summary>
            Checks if the specified constructor takes arguments of the specified types.
            </summary>
            <param name="constructorInfo">ConstructorInfo on which to call this helper method.</param>
            <param name="types">Array of type arguments to check against the constructor parameters.</param>
            <returns>True if the constructor takes arguments of the specified types, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Data.Edm.PlatformHelper.CreateCompiled(System.String,System.Text.RegularExpressions.RegexOptions)">
            <summary>
            Creates a Compiled Regex expression
            </summary>
            <param name="pattern">Pattern to match.</param>
            <param name="options">Options to use.</param>
            <returns>Regex expression to match supplied patter</returns>
            <remarks>Is marked as compiled option only in platforms otherwise RegexOption.None is used</remarks>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation">
            <summary>
            Formatter for Json Object
            </summary>
        </member>
        <member name="T:System.Spatial.GeoJsonObjectFormatter">
            <summary>Represents a formatter for Json object.</summary>
        </member>
        <member name="M:System.Spatial.GeoJsonObjectFormatter.Create">
            <summary>Creates the implementation of the formatter.</summary>
            <returns>The created <see cref="T:System.Spatial.GeoJsonObjectFormatter" /> implementation.</returns>
        </member>
        <member name="M:System.Spatial.GeoJsonObjectFormatter.Read``1(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>Reads from the source.</summary>
            <returns>The <see cref="T:System.Spatial.GeoJsonObjectFormatter" /> object that was read.</returns>
            <param name="source">The source json object.</param>
            <typeparam name="T">The spatial type to read.</typeparam>
        </member>
        <member name="M:System.Spatial.GeoJsonObjectFormatter.Write(System.Spatial.ISpatial)">
            <summary>Converts spatial value to a Json object.</summary>
            <returns>The json object.</returns>
            <param name="value">The spatial value.</param>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation.builder">
            <summary>
            Spatial builder
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation.parsePipeline">
            <summary>
            The parse pipeline
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The SpatialImplementation that created this instance</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation.Read``1(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Read from the source
            </summary>
            <typeparam name="T">The spatial type to read</typeparam>
            <param name="source">The source json object</param>
            <returns>The read instance</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation.Write(System.Spatial.ISpatial)">
            <summary>
            Convert spatial value to a Json Object
            </summary>
            <param name="value">The spatial value</param>
            <returns>The json object</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectFormatterImplementation.EnsureParsePipeline">
            <summary>
            Initialize the pipeline
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeoJsonObjectWriter">
            <summary>
            Convert Spatial objects into json writer
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeoJsonWriterBase">
            <summary>
            Base Writer for GeoJson
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.DrawBoth">
            <summary>
            Base class to create a unified set of handlers for Geometry and Geography
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.op_Implicit(Microsoft.Data.Spatial.DrawBoth)~System.Spatial.SpatialPipeline">
            <summary>
            Performs an implicit conversion from <see cref="T:Microsoft.Data.Spatial.DrawBoth"/> to <see cref="T:System.Spatial.SpatialPipeline"/>.
            </summary>
            <param name="both">The instance to convert.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnLineTo(System.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnLineTo(System.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnBeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnBeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnBeginGeography(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnBeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.OnSetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>the coordinate system to be passed down the pipeline</returns>
        </member>
        <member name="P:Microsoft.Data.Spatial.DrawBoth.GeographyPipeline">
            <summary>
            Gets the draw geography.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.DrawBoth.GeometryPipeline">
            <summary>
            Gets the draw geometry.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput">
            <summary>
            This class is responsible for taking the calls to DrawGeography and delegating them to the unified
            handlers
            </summary>
        </member>
        <member name="T:System.Spatial.GeographyPipeline">
            <summary>Represents the pipeline of geography.</summary>
        </member>
        <member name="M:System.Spatial.GeographyPipeline.BeginGeography(System.Spatial.SpatialType)">
            <summary>Begins drawing a spatial object.</summary>
            <param name="type">The spatial type of the object.</param>
        </member>
        <member name="M:System.Spatial.GeographyPipeline.BeginFigure(System.Spatial.GeographyPosition)">
            <summary>Begins drawing a figure.</summary>
            <param name="position">The position of the figure.</param>
        </member>
        <member name="M:System.Spatial.GeographyPipeline.LineTo(System.Spatial.GeographyPosition)">
            <summary>Draws a point in the specified coordinate.</summary>
            <param name="position">The position of the line.</param>
        </member>
        <member name="M:System.Spatial.GeographyPipeline.EndFigure">
            <summary>Ends the current figure.</summary>
        </member>
        <member name="M:System.Spatial.GeographyPipeline.EndGeography">
            <summary>Ends the current spatial object.</summary>
        </member>
        <member name="M:System.Spatial.GeographyPipeline.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>Sets the coordinate system.</summary>
            <param name="coordinateSystem">The coordinate system to set.</param>
        </member>
        <member name="M:System.Spatial.GeographyPipeline.Reset">
            <summary>Resets the pipeline.</summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.both">
            <summary>
            the DrawBoth instance that should be delegated to
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.#ctor(Microsoft.Data.Spatial.DrawBoth)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput"/> class.
            </summary>
            <param name="both">The both.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.LineTo(System.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.BeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.BeginGeography(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.EndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeographyInput.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput">
            <summary>
            This class is responsible for taking the calls to DrawGeometry and delegating them to the unified
            handlers
            </summary>
        </member>
        <member name="T:System.Spatial.GeometryPipeline">
            <summary>Represents the pipeline of geometry.</summary>
        </member>
        <member name="M:System.Spatial.GeometryPipeline.BeginGeometry(System.Spatial.SpatialType)">
            <summary>Begins drawing a spatial object.</summary>
            <param name="type">The spatial type of the object.</param>
        </member>
        <member name="M:System.Spatial.GeometryPipeline.BeginFigure(System.Spatial.GeometryPosition)">
            <summary>Begins drawing a figure.</summary>
            <param name="position">The position of the figure.</param>
        </member>
        <member name="M:System.Spatial.GeometryPipeline.LineTo(System.Spatial.GeometryPosition)">
            <summary>Draws a point in the specified coordinate.</summary>
            <param name="position">The position of the line.</param>
        </member>
        <member name="M:System.Spatial.GeometryPipeline.EndFigure">
            <summary>Ends the current figure.</summary>
        </member>
        <member name="M:System.Spatial.GeometryPipeline.EndGeometry">
            <summary>Ends the current spatial object.</summary>
        </member>
        <member name="M:System.Spatial.GeometryPipeline.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>Sets the coordinate system.</summary>
            <param name="coordinateSystem">The coordinate system to set.</param>
        </member>
        <member name="M:System.Spatial.GeometryPipeline.Reset">
            <summary>Resets the pipeline.</summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.both">
            <summary>
            the DrawBoth instance that should be delegated to
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.#ctor(Microsoft.Data.Spatial.DrawBoth)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput"/> class.
            </summary>
            <param name="both">The both.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.LineTo(System.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.BeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.BeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.EndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DrawBoth.DrawGeometryInput.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonWriterBase.stack">
            <summary>
            Stack to track the current type being written.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonWriterBase.currentCoordinateSystem">
            <summary>
            CoordinateSystem for the types being written.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonWriterBase.figureDrawn">
            <summary>
            Figure added in current shape
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.#ctor">
            <summary>
            Creates a new instance of the GeoJsonWriter.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnLineTo(System.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnLineTo(System.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnBeginGeography(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnBeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnBeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnBeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnSetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>
            the coordinate system to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.AddPropertyName(System.String)">
            <summary>
            Add a property name to the current json object
            </summary>
            <param name="name">The name to add</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.AddValue(System.String)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.AddValue(System.Double)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.StartObjectScope">
            <summary>
            Start a new json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.StartArrayScope">
            <summary>
            Start a new json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.EndObjectScope">
            <summary>
            End the current json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.EndArrayScope">
            <summary>
            End the current json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.GetSpatialTypeName(System.Spatial.SpatialType)">
            <summary>
            Gets the GeoJson type name to use when writing the specified type.
            </summary>
            <param name="type">SpatialType being written.</param>
            <returns>GeoJson type name corresponding to the specified <paramref name="type"/>.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.GetDataName(System.Spatial.SpatialType)">
            <summary>
            Gets the name of the GeoJson member to use when writing the body of the spatial object.
            </summary>
            <param name="type">SpatialType being written.</param>
            <returns>Name of the GeoJson member to use when writing the body of the spatial object.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.TypeHasArrayScope(System.Spatial.SpatialType)">
            <summary>
            Whether or not the specified type wraps its data in an outer array.
            </summary>
            <param name="type">SpatialType being written.</param>
            <returns>True if the type uses an outer array, otherwise false.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Sets the CoordinateSystem for Geography and Geometry shapes.
            </summary>
            <param name="coordinateSystem">CoordinateSystem value to set.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.BeginShape(System.Spatial.SpatialType,System.Spatial.CoordinateSystem)">
            <summary>
            Start writing a Geography or Geometry shape.
            </summary>
            <param name="type">SpatialType to use when writing the shape.</param>
            <param name="defaultCoordinateSystem">Default CoordinateSystem to use if SetCoordinateSystem is never called on this shape.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.WriteShapeHeader(System.Spatial.SpatialType)">
            <summary>
            Write the type header information for a shape.
            </summary>
            <param name="type">SpatialType being written.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.BeginFigure">
            <summary>
            Start writing a figure in a Geography or Geometry shape.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.WriteControlPoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Write a position in a Geography or Geometry figure.
            </summary>
            <param name="first">First (X/Longitude) Coordinate</param>
            <param name="second">Second (Y/Latitude) Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.EndFigure">
            <summary>
            Ends a Geography or Geometry figure.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.EndShape">
            <summary>
            Ends a Geography or Geometry shape.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonWriterBase.WriteCrs">
            <summary>
            Writes the coordinate reference system footer for the GeoJson object.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeoJsonWriterBase.ShapeHasObjectScope">
            <summary>
            True if the shape should write start and end object scope, otherwise false.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeoJsonWriterBase.IsTopLevel">
            <summary>
            True if the shape is not a child of another shape.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeoJsonWriterBase.FigureHasArrayScope">
            <summary>
            True if the shape should write start and end object scope, otherwise false.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonObjectWriter.containers">
            <summary>
            Stack of json objects
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonObjectWriter.currentPropertyName">
            <summary>
            Buffered key of the current name-value pair
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonObjectWriter.lastCompletedObject">
            <summary>
            Stores the last object fully serialized
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.StartObjectScope">
            <summary>
            Start a new json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.StartArrayScope">
            <summary>
            Start a new json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.AddPropertyName(System.String)">
            <summary>
            Add a property name to the current json object
            </summary>
            <param name="name">The name to add</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.AddValue(System.String)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.AddValue(System.Double)">
            <summary>
            Add a value to the current json scope
            </summary>
            <param name="value">The value to add</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.EndArrayScope">
            <summary>
            End the current json array scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.EndObjectScope">
            <summary>
            End the current json object scope
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.AddToScope(System.Object)">
            <summary>
            Add an json object to the current scope
            </summary>
            <param name="jsonObject">The json object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.GetAndClearCurrentPropertyName">
            <summary>
            Return the current property name, and clear the buffer
            </summary>
            <returns>The current property name</returns>
            <remarks>
            When inserting to a dictionary, the name-value pair comes across multiple pipeline calls
            Therefore we need to buffer the name part and wait for the value part.
            You can get into an incorrect state (caught by asserts) if you add a property name without 
            using it immediately next.
            </remarks>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.AsList">
            <summary>
            Access the current container as a List
            </summary>
            <returns>The current container as list</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectWriter.AsDictionary">
            <summary>
            Access the current container as a Dictionary
            </summary>
            <returns>The current container as dictionary</returns>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeoJsonObjectWriter.JsonObject">
            <summary>
            Get the top level json object
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeoJsonObjectWriter.IsArray">
            <summary>
            Test if the current container is an array
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline">
            <summary>
            Adapter from the type washed API to Geography, where it assumes that coord1 is Latitude.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.TypeWashedPipeline">
            <summary>
            Internal pipeline Inteface that washes the distinction between Geography and Geometry
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given EPSG ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedPipeline.BeginGeo(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">X or Latitude Coordinate</param>
            <param name="coordinate2">Y or Longitude Coordinate</param>
            <param name="coordinate3">Z Coordinate</param>
            <param name="coordinate4">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Add a control point to the current figure
            </summary>
            <param name="coordinate1">First coordinate</param>
            <param name="coordinate2">Second coordinate</param>
            <param name="coordinate3">Third coordinate</param>
            <param name="coordinate4">Fourth coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.TypeWashedPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="F:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.output">
            <summary>
            The pipeline to redirect the calls to
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="output">The pipeline to redirect the calls to</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.BeginGeo(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a line to a point in the specified coordinate
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.TypeWashedToGeographyLatLongPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline">
            <summary>
            Adapter from the type washed API to Geography, where it assumes that coord1 is Longitude.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.output">
            <summary>
            The pipeline to redirect the calls to
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="output">The pipeline to redirect the calls to</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.BeginGeo(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a line to a point in the specified coordinate
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.TypeWashedToGeographyLongLatPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeoJsonObjectReader">
            <summary>
            The spatial reader that can read from a pre parsed GeoJson payload
            </summary>
        </member>
        <member name="T:System.Spatial.SpatialReader`1">
            <summary>
            Reader to be used by spatial formats
            </summary>
            <typeparam name="TSource">The type of source that the reader operates on.</typeparam>
        </member>
        <member name="M:System.Spatial.SpatialReader`1.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Creates a reader
            </summary>
            <param name="destination">the instance of the pipeline that the reader will message while it is reading.</param>
        </member>
        <member name="M:System.Spatial.SpatialReader`1.ReadGeography(`0)">
            <summary>
              Parses some serialized format that represents one or more Geography spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref="T:System.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name="input">The input string</param>
        </member>
        <member name="M:System.Spatial.SpatialReader`1.ReadGeometry(`0)">
            <summary>
              Parses some serialized format that represents one or more Geometry spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref="T:System.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name="input">The input string</param>
        </member>
        <member name="M:System.Spatial.SpatialReader`1.Reset">
            <summary>
            Sets the reader and underlying Destination back to a clean
            starting state after an exception
            </summary>
        </member>
        <member name="M:System.Spatial.SpatialReader`1.ReadGeometryImplementation(`0)">
            <summary>
              Parses some serialized format that represents one or more Geometry spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref="T:System.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name="input">The input string</param>
        </member>
        <member name="M:System.Spatial.SpatialReader`1.ReadGeographyImplementation(`0)">
            <summary>
              Parses some serialized format that represents one or more Geography spatial values, passing the first one down the pipeline.
            </summary>
            <exception cref="T:System.Spatial.ParseErrorException">Throws if the input is not valid. In that case, guarantees that it will not pass anything down the pipeline, or will clear the pipeline by passing down a Reset.</exception>
            <param name="input">The input string</param>
        </member>
        <member name="P:System.Spatial.SpatialReader`1.Destination">
            <summary>
            The pipeline that is messaged while the reader is reading.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.GeoJsonObjectReader"/> class.
            </summary>
            <param name="destination">The pipeline.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.ReadGeographyImplementation(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Parses some serialized format that represents a geography value, passing the result down the pipeline.
            </summary>
            <param name="input">The jsonObject to read from.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.ReadGeometryImplementation(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Parses some serialized format that represents a geometry value, passing the result down the pipeline.
            </summary>
            <param name="input">The jsonObject to read from.</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline">
            <summary>
            A common way to call Geography and Geometry pipeline apis from the structured Json
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.pipeline">
            <summary>
            Pipeline to use for the output of the translation of the GeoJSON object into pipeline method calls.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.#ctor(Microsoft.Data.Spatial.TypeWashedPipeline)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline"/> class.
            </summary>
            <param name="pipeline">Spatial pipeline that will receive the pipeline method calls.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendToPipeline(System.Collections.Generic.IDictionary{System.String,System.Object},System.Boolean)">
            <summary>
            Translates a dictionary of parsed GeoJSON members and values into method calls on the spatial pipeline.
            </summary>
            <param name="members">Dictionary containing GeoJSON members and values.</param>
            <param name="requireSetCoordinates">Coordinate System must be set for this pipeline</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendArrayOfArray(System.Collections.IEnumerable,System.Action{System.Collections.IEnumerable})">
            <summary>
            Iterates over an object array, verifies that each element in the array is another array, and calls a delgate on the contained array.
            </summary>
            <param name="array">Array to iterate over.</param>
            <param name="send">Delegate to invoke for each element once it has been validated to be an array.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsNullableDouble(System.Object)">
            <summary>
            Convert an object to a nullable double value.
            </summary>
            <param name="value">Object to convert.</param>
            <returns>If the specified element was null, returns null, otherwise returns the converted double value.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsDouble(System.Object)">
            <summary>
            Convert an object to a non-null double value.
            </summary>
            <param name="value">Object to convert.</param>
            <returns>Converted double value.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsJsonArray(System.Object)">
            <summary>
            Values as json array.
            </summary>
            <param name="value">The value.</param>
            <returns>The value cast as a json array.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsJsonObject(System.Object)">
            <summary>
            Values as json object.
            </summary>
            <param name="value">The value.</param>
            <returns>The value cast as IDictionary&lt;string, object&gt;</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ValueAsString(System.String,System.Object)">
            <summary>
            Values as string.
            </summary>
            <param name="propertyName">Name of the property.</param>
            <param name="value">The value.</param>
            <returns>The value cast as a string.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.GetSpatialType(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Get the type member value from the specified GeoJSON member dictionary.
            </summary>
            <param name="geoJsonObject">Dictionary containing the GeoJSON members and their values.</param>
            <returns>SpatialType for the GeoJSON object.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.TryGetCoordinateSystemId(System.Collections.Generic.IDictionary{System.String,System.Object},System.Nullable{System.Int32}@)">
            <summary>
            Tries to get a coordinate system id from the geo json object's 'crs' property
            </summary>
            <param name="geoJsonObject">The geo json object.</param>
            <param name="epsgId">The coordinate system id.</param>
            <returns>True if the object had a coordinate system</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.GetCoordinateSystemIdFromCrs(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Gets the coordinate system ID from a representation of the CRS object
            </summary>
            <param name="crsJsonObject">The parsed representation of the CRS object.</param>
            <returns>The coordinate system ID</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.GetMemberValueAsJsonArray(System.Collections.Generic.IDictionary{System.String,System.Object},System.String)">
            <summary>
            Get the designated member value from the specified GeoJSON member dictionary.
            </summary>
            <param name="geoJsonObject">Dictionary containing the GeoJSON members and their values.</param>
            <param name="memberName">The member's tag name</param>
            <returns>Member value for the GeoJSON object.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.EnumerableAny(System.Collections.IEnumerable)">
            <summary>
            This method assumes a non forward only enumerable
            </summary>
            <param name="enumerable">The enumerable to check</param>
            <returns>true if there is at least one element</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.ReadTypeName(System.String)">
            <summary>
            Reads GeoJson 'type' value and maps it a valid SpatialType.
            </summary>
            <param name="typeName">The GeoJson standard type name</param>
            <returns>SpatialType corresponding to the GeoJson type name.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendShape(System.Spatial.SpatialType,System.Collections.IEnumerable)">
            <summary>
            Sends a shape to the spatial pipeline.
            </summary>
            <param name="spatialType">SpatialType of the shape.</param>
            <param name="contentMembers">Content member for the shape</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendCoordinates(System.Spatial.SpatialType,System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value into method calls on the spatial pipeline.
            </summary>
            <param name="spatialType">SpatialType of the GeoJSON object.</param>
            <param name="contentMembers">Coordinates value of the GeoJSON object, or inner geometries for collection</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPoint(System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a Point object into method calls on the spatial pipeline.
            </summary>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendLineString(System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a LineString object into method calls on the spatial pipeline.
            </summary>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPolygon(System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a Polygon object into method calls on the spatial pipeline.
            </summary>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendMultiShape(System.Spatial.SpatialType,System.Collections.IEnumerable)">
            <summary>
            Translates the coordinates member value of a MultiPoint, MultiLineString, or MultiPolygon object into method calls on the spatial pipeline.
            </summary>
            <param name="containedSpatialType">Type of the shape contained in the Multi shape.</param>
            <param name="coordinates">Parsed coordinates array.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPositionArray(System.Collections.IEnumerable)">
            <summary>
            Translates an array of positions into method calls on the spatial pipeline.
            </summary>
            <param name="positionArray">List containing the positions.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeoJsonObjectReader.SendToTypeWashedPipeline.SendPosition(System.Collections.IEnumerable,System.Boolean)">
            <summary>
            Translates an individual position into a method call on the spatial pipeline.
            </summary>
            <param name="positionElements">List containing elements of the position.</param>
            <param name="first">True if the position is the first one being written to a figure, otherwise false.</param>
        </member>
        <member name="T:System.Spatial.CompositeKey`2">
            <summary>
            A key consisting of multiple fields
            </summary>
            <typeparam name="T1">The type of the first field.</typeparam>
            <typeparam name="T2">The type of the second field.</typeparam>
        </member>
        <member name="F:System.Spatial.CompositeKey`2.first">
            <summary>
            The first field
            </summary>
        </member>
        <member name="F:System.Spatial.CompositeKey`2.second">
            <summary>
            The second field
            </summary>
        </member>
        <member name="M:System.Spatial.CompositeKey`2.#ctor(`0,`1)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Spatial.CompositeKey`2"/> class.
            </summary>
            <param name="first">The first.</param>
            <param name="second">The second.</param>
        </member>
        <member name="M:System.Spatial.CompositeKey`2.op_Equality(System.Spatial.CompositeKey{`0,`1},System.Spatial.CompositeKey{`0,`1})">
            <summary>
            Implements the operator ==.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:System.Spatial.CompositeKey`2.op_Inequality(System.Spatial.CompositeKey{`0,`1},System.Spatial.CompositeKey{`0,`1})">
            <summary>
            Implements the operator !=.
            </summary>
            <param name="left">The left.</param>
            <param name="right">The right.</param>
            <returns>
            The result of the operator.
            </returns>
        </member>
        <member name="M:System.Spatial.CompositeKey`2.Equals(System.Spatial.CompositeKey{`0,`1})">
            <summary>
            Indicates whether the current object is equal to another object of the same type.
            </summary>
            <param name="other">An object to compare with this object.</param>
            <returns>
            true if the current object is equal to the <paramref name="other"/> parameter; otherwise, false.
            </returns>
        </member>
        <member name="M:System.Spatial.CompositeKey`2.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object"/> is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object"/> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object"/> is equal to this instance; otherwise, <c>false</c>.
            </returns>
            <exception cref="T:System.NullReferenceException">The <paramref name="obj"/> parameter is null.</exception>
        </member>
        <member name="M:System.Spatial.CompositeKey`2.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table. 
            </returns>
        </member>
        <member name="T:System.Spatial.FormatterExtensions">
            <summary>Represents the extensions to formatters.</summary>
        </member>
        <member name="M:System.Spatial.FormatterExtensions.Write(System.Spatial.SpatialFormatter{System.IO.TextReader,System.IO.TextWriter},System.Spatial.ISpatial)">
            <summary>Writes the specified formatter.</summary>
            <returns>A string value of the formatted object.</returns>
            <param name="formatter">The formatter.</param>
            <param name="spatial">The spatial object.</param>
        </member>
        <member name="M:System.Spatial.FormatterExtensions.Write(System.Spatial.SpatialFormatter{System.Xml.XmlReader,System.Xml.XmlWriter},System.Spatial.ISpatial)">
            <summary>Writes the specified formatter.</summary>
            <returns>A string value of the formatted object.</returns>
            <param name="formatter">The formatter.</param>
            <param name="spatial">The spatial object.</param>
        </member>
        <member name="T:System.Spatial.GeographyFullGlobe">
            <summary>Represents the full globe of geography.</summary>
        </member>
        <member name="T:System.Spatial.GeographySurface">
            <summary>Represents the geography surface.</summary>
        </member>
        <member name="T:System.Spatial.Geography">
            <summary>Represents a base class of geography shapes.</summary>
        </member>
        <member name="T:System.Spatial.ISpatial">
            <summary>Represents the spatial interface.</summary>
        </member>
        <member name="P:System.Spatial.ISpatial.CoordinateSystem">
            <summary>Gets the coordinate system.</summary>
            <returns>The coordinate system.</returns>
        </member>
        <member name="P:System.Spatial.ISpatial.IsEmpty">
            <summary>Gets a value that indicates whether the spatial type is empty.</summary>
            <returns>true if the spatial type is empty; otherwise, false.</returns>
        </member>
        <member name="F:System.Spatial.Geography.creator">
            <summary>
            The implementation that created this instance
            </summary>
        </member>
        <member name="F:System.Spatial.Geography.coordinateSystem">
            <summary>
            The CoordinateSystem of this geography
            </summary>
        </member>
        <member name="M:System.Spatial.Geography.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.Geography" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this geography.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.Geography.SendTo(System.Spatial.GeographyPipeline)">
            <summary>Sends the current spatial object to the given pipeline.</summary>
            <param name="chain">The spatial pipeline.</param>
        </member>
        <member name="M:System.Spatial.Geography.ComputeHashCodeFor``1(System.Spatial.CoordinateSystem,System.Collections.Generic.IEnumerable{``0})">
            <summary>
            Computes the hashcode for the given CoordinateSystem and the fields
            </summary>
            <typeparam name="T">Spatial type instances or doubles for base types (Geography/Geometry types).</typeparam>
            <param name="coords">CoordinateSystem instance.</param>
            <param name="fields">Spatial type instances or doubles for base types (Geography/Geometry types).</param>
            <returns>hashcode for the CoordinateSystem instance and Spatial type instances.</returns>
        </member>
        <member name="M:System.Spatial.Geography.BaseEquals(System.Spatial.Geography)">
            <summary>
            Check for basic equality due to emptyness, nullness, referential equality and difference in coordinate system
            </summary>
            <param name="other">The other geography</param>
            <returns>Boolean value indicating equality, or null to indicate inconclusion</returns>
        </member>
        <member name="P:System.Spatial.Geography.CoordinateSystem">
            <summary>Gets the coordinate system of the geography.</summary>
            <returns>The coordinate system of the geography.</returns>
        </member>
        <member name="P:System.Spatial.Geography.IsEmpty">
            <summary>Gets a value that indicates whether the geography is empty.</summary>
            <returns>true if the geography is empty; otherwise, false.</returns>
        </member>
        <member name="P:System.Spatial.Geography.Creator">
            <summary>
            Gets the implementation that created this instance.
            </summary>
        </member>
        <member name="M:System.Spatial.GeographySurface.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographySurface" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyFullGlobe.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyFullGlobe" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyFullGlobe.Equals(System.Spatial.GeographyFullGlobe)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyFullGlobe.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyFullGlobe.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="T:System.Spatial.GeographyCurve">
            <summary>Represents the curve of geography.</summary>
        </member>
        <member name="M:System.Spatial.GeographyCurve.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this geography curve.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:System.Spatial.GeographyLineString">
            <summary>Represents a geography line string consist of an array of geo points.</summary>
        </member>
        <member name="M:System.Spatial.GeographyLineString.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyLineString.Equals(System.Spatial.GeographyLineString)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeographyLineString.Points">
            <summary>Gets the point list.</summary>
            <returns>The point list.</returns>
        </member>
        <member name="T:System.Spatial.GeographyMultiCurve">
            <summary>Represents the multi-curve of geography.</summary>
        </member>
        <member name="T:System.Spatial.GeographyCollection">
            <summary>Represents the collection of geographies.</summary>
        </member>
        <member name="M:System.Spatial.GeographyCollection.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyCollection" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this geography collection.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyCollection.Equals(System.Spatial.GeographyCollection)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyCollection.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyCollection.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeographyCollection.Geographies">
            <summary>Gets the collection of geographies.</summary>
            <returns>The collection of geographies.</returns>
        </member>
        <member name="M:System.Spatial.GeographyMultiCurve.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyMultiCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:System.Spatial.GeographyMultiLineString">
            <summary>Represents the multi-line string of geography.</summary>
        </member>
        <member name="M:System.Spatial.GeographyMultiLineString.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyMultiLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiLineString.Equals(System.Spatial.GeographyMultiLineString)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeographyMultiLineString.LineStrings">
            <summary>Gets the line strings.</summary>
            <returns>A collection of line strings.</returns>
        </member>
        <member name="T:System.Spatial.GeographyMultiPoint">
            <summary>Represents the multi-point of geography.</summary>
        </member>
        <member name="M:System.Spatial.GeographyMultiPoint.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyMultiPoint" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiPoint.Equals(System.Spatial.GeographyMultiPoint)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiPoint.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiPoint.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeographyMultiPoint.Points">
            <summary>Gets a collection of points.</summary>
            <returns>A collection of points.</returns>
        </member>
        <member name="T:System.Spatial.GeographyMultiPolygon">
            <summary>Represents the multi-polygon of geography.</summary>
        </member>
        <member name="T:System.Spatial.GeographyMultiSurface">
            <summary>Represents the multi-surface of geography.</summary>
        </member>
        <member name="M:System.Spatial.GeographyMultiSurface.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyMultiSurface" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiPolygon.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyMultiPolygon" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiPolygon.Equals(System.Spatial.GeographyMultiPolygon)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiPolygon.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyMultiPolygon.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeographyMultiPolygon.Polygons">
            <summary>Gets a collection of polygons.</summary>
            <returns>A collection of polygons.</returns>
        </member>
        <member name="T:System.Spatial.GeographyPoint">
            <summary>Represents a geography point.</summary>
        </member>
        <member name="M:System.Spatial.GeographyPoint.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyPoint" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyPoint.Create(System.Double,System.Double)">
            <summary>Creates a geography point using the specified latitude and longitude.</summary>
            <returns>The geography point that was created.</returns>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
        </member>
        <member name="M:System.Spatial.GeographyPoint.Create(System.Double,System.Double,System.Nullable{System.Double})">
            <summary>Creates a geography point using the specified latitude, longitude and dimension.</summary>
            <returns>The geography point that was created.</returns>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
            <param name="z">The z dimension.</param>
        </member>
        <member name="M:System.Spatial.GeographyPoint.Create(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a geography point using the specified latitude, longitude and dimensions.</summary>
            <returns>The geography point that was created.</returns>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:System.Spatial.GeographyPoint.Create(System.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a geography point using the specified coordinate system, latitude, longitude and dimensions.</summary>
            <returns>The geography point that was created.</returns>
            <param name="coordinateSystem">The coordinate system to use.</param>
            <param name="latitude">The latitude.</param>
            <param name="longitude">The longitude.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:System.Spatial.GeographyPoint.Equals(System.Spatial.GeographyPoint)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyPoint.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyPoint.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPoint.Latitude">
            <summary>Gets the latitude.</summary>
            <returns>The latitude.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPoint.Longitude">
            <summary>Gets the longitude.</summary>
            <returns>The longitude.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPoint.Z">
            <summary>Gets the nullable Z.</summary>
            <returns>The nullable Z.</returns>
            <remarks>Z is the altitude portion of position.</remarks>
        </member>
        <member name="P:System.Spatial.GeographyPoint.M">
            <summary>Gets the nullable M.</summary>
            <returns>The nullable M.</returns>
            <remarks>M is the arbitrary measure associated with a position.</remarks>
        </member>
        <member name="T:System.Spatial.GeographyPolygon">
            <summary>Represents the geography polygon.</summary>
        </member>
        <member name="M:System.Spatial.GeographyPolygon.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeographyPolygon" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyPolygon.Equals(System.Spatial.GeographyPolygon)">
            <summary>Determines whether this instance and another specified geography instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyPolygon.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeographyPolygon.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPolygon.Rings">
            <summary>Gets a collection of rings.</summary>
            <returns>A collection of rings.</returns>
        </member>
        <member name="T:System.Spatial.GeometryCollection">
            <summary>Represents the geometry collection.</summary>
        </member>
        <member name="T:System.Spatial.Geometry">
            <summary>Represents the base class of geography shapes.</summary>
        </member>
        <member name="F:System.Spatial.Geometry.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:System.Spatial.Geometry.coordinateSystem">
            <summary>
            The CoordinateSystem of this geometry
            </summary>
        </member>
        <member name="M:System.Spatial.Geometry.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.Geometry" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.Geometry.SendTo(System.Spatial.GeometryPipeline)">
            <summary>Sends the current spatial object to the given pipeline.</summary>
            <param name="chain">The spatial pipeline.</param>
        </member>
        <member name="M:System.Spatial.Geometry.BaseEquals(System.Spatial.Geometry)">
            <summary>
            Check for basic equality due to emptyness, nullness, referential equality and difference in coordinate system
            </summary>
            <param name="other">The other geography</param>
            <returns>Boolean value indicating equality, or null to indicate inconclusion</returns>
        </member>
        <member name="P:System.Spatial.Geometry.CoordinateSystem">
            <summary>Gets the SRID of this instance of geometry.</summary>
            <returns>The SRID of this instance of geometry.</returns>
        </member>
        <member name="P:System.Spatial.Geometry.IsEmpty">
            <summary>Gets a value that indicates whether geometry is empty.</summary>
            <returns>true if the geometry is empty; otherwise, false.</returns>
        </member>
        <member name="P:System.Spatial.Geometry.Creator">
            <summary>
            Gets the implementation that created this instance.
            </summary>
        </member>
        <member name="M:System.Spatial.GeometryCollection.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryCollection" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryCollection.Equals(System.Spatial.GeometryCollection)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryCollection.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryCollection.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeometryCollection.Geometries">
            <summary>Gets the geometry instances in this collection.</summary>
            <returns>A collection of geometries.</returns>
        </member>
        <member name="T:System.Spatial.GeometryCurve">
            <summary>Represents the geometry curve.</summary>
        </member>
        <member name="M:System.Spatial.GeometryCurve.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:System.Spatial.GeometryLineString">
            <summary>Represents the geometry line string.</summary>
        </member>
        <member name="M:System.Spatial.GeometryLineString.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryLineString.Equals(System.Spatial.GeometryLineString)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeometryLineString.Points">
            <summary>Gets the point list.</summary>
            <returns>The point list.</returns>
        </member>
        <member name="T:System.Spatial.GeometryMultiCurve">
            <summary>Represents the geometry multi-curve.</summary>
        </member>
        <member name="M:System.Spatial.GeometryMultiCurve.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryMultiCurve" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="T:System.Spatial.GeometryMultiLineString">
            <summary>Represents the geometry multi-line string.</summary>
        </member>
        <member name="M:System.Spatial.GeometryMultiLineString.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryMultiLineString" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiLineString.Equals(System.Spatial.GeometryMultiLineString)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiLineString.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiLineString.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeometryMultiLineString.LineStrings">
            <summary>Gets a collection of line strings.</summary>
            <returns>A collection of line strings.</returns>
        </member>
        <member name="T:System.Spatial.GeometryMultiPoint">
            <summary>Represents the geometry multi-point.</summary>
        </member>
        <member name="M:System.Spatial.GeometryMultiPoint.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryMultiPoint" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiPoint.Equals(System.Spatial.GeometryMultiPoint)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiPoint.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiPoint.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeometryMultiPoint.Points">
            <summary>Gets a collection of points.</summary>
            <returns>A collection of points.</returns>
        </member>
        <member name="T:System.Spatial.GeometryMultiPolygon">
            <summary>Represents the geometry multi-polygon.</summary>
        </member>
        <member name="T:System.Spatial.GeometryMultiSurface">
            <summary>Represents the geometry multi-surface.</summary>
        </member>
        <member name="M:System.Spatial.GeometryMultiSurface.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiPolygon.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryMultiPolygon" /> class.</summary>
            <param name="coordinateSystem">The coordinate system of this instance.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiPolygon.Equals(System.Spatial.GeometryMultiPolygon)">
            <summary>Determines whether this instance and another specified geometry instance have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geometry to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiPolygon.Equals(System.Object)">
            <summary>Determines whether this instance and the specified object have the same value.</summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The object to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryMultiPolygon.GetHashCode">
            <summary>Gets the hash code.</summary>
            <returns>The hash code.</returns>
        </member>
        <member name="P:System.Spatial.GeometryMultiPolygon.Polygons">
            <summary>Gets a collection of polygons.</summary>
            <returns>A collection of polygons.</returns>
        </member>
        <member name="T:System.Spatial.GeometryPoint">
            <summary>Represents the Geometry Point.</summary>
        </member>
        <member name="M:System.Spatial.GeometryPoint.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryPoint" /> class. Empty Point constructor.</summary>
            <param name="coordinateSystem">The CoordinateSystem.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryPoint.Create(System.Double,System.Double)">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
        </member>
        <member name="M:System.Spatial.GeometryPoint.Create(System.Double,System.Double,System.Nullable{System.Double})">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
            <param name="z">The z dimension.</param>
        </member>
        <member name="M:System.Spatial.GeometryPoint.Create(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:System.Spatial.GeometryPoint.Create(System.Spatial.CoordinateSystem,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary> Creates the specified latitude. </summary>
            <returns>The GeographyPoint that was created.</returns>
            <param name="coordinateSystem">The coordinate system to use.</param>
            <param name="x">The x dimension.</param>
            <param name="y">The y dimension.</param>
            <param name="z">The z dimension.</param>
            <param name="m">The m dimension.</param>
        </member>
        <member name="M:System.Spatial.GeometryPoint.Equals(System.Spatial.GeometryPoint)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryPoint.Equals(System.Object)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryPoint.GetHashCode">
            <summary> Gets the Hashcode.</summary>
            <returns>The hashcode.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPoint.X">
            <summary>Gets the Latitude.</summary>
            <returns>The Latitude.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPoint.Y">
            <summary>Gets the Longitude.</summary>
            <returns>The Longitude.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPoint.Z">
            <summary>Gets the Nullable Z.</summary>
            <returns>The Nullable Z.</returns>
            <remarks>Z is the altitude portion of position.</remarks>
        </member>
        <member name="P:System.Spatial.GeometryPoint.M">
            <summary>Gets the Nullable M.</summary>
            <returns>The Nullable M.</returns>
            <remarks>M is the arbitrary measure associated with a position.</remarks>
        </member>
        <member name="T:System.Spatial.GeometryPolygon">
            <summary>Represents the Geometry polygon.</summary>
        </member>
        <member name="T:System.Spatial.GeometrySurface">
            <summary>Represents a geometry surface.</summary>
        </member>
        <member name="M:System.Spatial.GeometrySurface.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryPolygon.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GeometryPolygon" /> class.</summary>
            <param name="coordinateSystem">The CoordinateSystem.</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryPolygon.Equals(System.Spatial.GeometryPolygon)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="other">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryPolygon.Equals(System.Object)">
            <summary> Determines whether this instance and another specified geography instance have the same value.  </summary>
            <returns>true if the value of the value parameter is the same as this instance; otherwise, false.</returns>
            <param name="obj">The geography to compare to this instance.</param>
        </member>
        <member name="M:System.Spatial.GeometryPolygon.GetHashCode">
            <summary>Indicates the Get Hashcode.</summary>
            <returns>The hashcode.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPolygon.Rings">
            <summary>Gets the set of rings.</summary>
        </member>
        <member name="T:System.Spatial.IGeographyProvider">
            <summary>Provides access to the geography objects that this object constructs.</summary>
        </member>
        <member name="E:System.Spatial.IGeographyProvider.ProduceGeography">
            <summary>Fires when the provider constructs a geography object.</summary>
        </member>
        <member name="P:System.Spatial.IGeographyProvider.ConstructedGeography">
            <summary>Gets the geography object that was constructed most recently.</summary>
            <returns>The geography object that was constructed.</returns>
        </member>
        <member name="T:System.Spatial.IGeometryProvider">
            <summary>Provides access to the geometry objects that this object constructs.</summary>
        </member>
        <member name="E:System.Spatial.IGeometryProvider.ProduceGeometry">
            <summary>Fires when the provider constructs a geometry object.</summary>
        </member>
        <member name="P:System.Spatial.IGeometryProvider.ConstructedGeometry">
            <summary>Gets the geometry object that was constructed most recently.</summary>
            <returns>The geometry object that was constructed.</returns>
        </member>
        <member name="T:System.Spatial.IShapeProvider">
            <summary>Provides access to the constructed geography or geometry.</summary>
        </member>
        <member name="T:System.Spatial.ParseErrorException">
            <summary>The exception that is thrown on an unsuccessful parsing of the serialized format.</summary>
        </member>
        <member name="M:System.Spatial.ParseErrorException.#ctor">
            <summary>Creates a new instance of the <see cref="T:System.Spatial.ParseErrorException" /> class.</summary>
        </member>
        <member name="M:System.Spatial.ParseErrorException.#ctor(System.String,System.Exception)">
            <summary>Creates a new instance of the <see cref="T:System.Spatial.ParseErrorException" /> class from a message and previous exception.</summary>
            <param name="message">The message about the exception.</param>
            <param name="innerException">The exception that preceeded this one.</param>
        </member>
        <member name="M:System.Spatial.ParseErrorException.#ctor(System.String)">
            <summary>Creates a new instance of the <see cref="T:System.Spatial.ParseErrorException" /> class from a message.</summary>
            <param name="message">The message about the exception.</param>
        </member>
        <member name="T:System.Spatial.SpatialBuilder">
            <summary>Creates a geometry or geography instances from spatial data pipelines.</summary>
        </member>
        <member name="T:System.Spatial.SpatialPipeline">
            <summary>
            One link of a geospatial pipeline
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialPipeline.geographyPipeline">
            <summary>
            the geography side of the pipeline
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialPipeline.geometryPipeline">
            <summary>
            the geometry side of the pipeline
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialPipeline.startingLink">
            <summary>
            A reference to the begining link of the chain
            useful for getting the startingLink when creating the chain fluently
            e.g.  new ForwardingSegment(new Node()).ChainTo(new Node()).ChainTo(new Node).StartingLink
            </summary>
        </member>
        <member name="M:System.Spatial.SpatialPipeline.#ctor">
            <summary> Initializes a new instance of the <see cref="T:System.Spatial.SpatialPipeline" /> class. </summary>
        </member>
        <member name="M:System.Spatial.SpatialPipeline.#ctor(System.Spatial.GeographyPipeline,System.Spatial.GeometryPipeline)">
            <summary> Initializes a new instance of the <see cref="T:System.Spatial.SpatialPipeline" /> class. </summary>
            <param name="geographyPipeline">The geography chain.</param>
            <param name="geometryPipeline">The geometry chain.</param>
        </member>
        <member name="M:System.Spatial.SpatialPipeline.op_Implicit(System.Spatial.SpatialPipeline)~System.Spatial.GeographyPipeline">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Spatial.SpatialPipeline"/> to <see cref="T:System.Spatial.GeographyPipeline"/>.
            </summary>
            <param name="spatialPipeline">The spatial chain.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:System.Spatial.SpatialPipeline.op_Implicit(System.Spatial.SpatialPipeline)~System.Spatial.GeometryPipeline">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Spatial.SpatialPipeline"/> to <see cref="T:System.Spatial.GeometryPipeline"/>.
            </summary>
            <param name="spatialPipeline">The spatial chain.</param>
            <returns>
            The result of the conversion.
            </returns>
        </member>
        <member name="M:System.Spatial.SpatialPipeline.ChainTo(System.Spatial.SpatialPipeline)">
            <summary> Adds the next pipeline.</summary>
            <returns>The last pipesegment in the chain, usually the one just created.</returns>
            <param name="destination">The next pipeline.</param>
        </member>
        <member name="P:System.Spatial.SpatialPipeline.GeographyPipeline">
            <summary> Gets the geography side of the pipeline. </summary>
        </member>
        <member name="P:System.Spatial.SpatialPipeline.GeometryPipeline">
            <summary> Gets the geometry side of the pipeline. </summary>
        </member>
        <member name="P:System.Spatial.SpatialPipeline.StartingLink">
            <summary> Gets or sets the starting link. </summary>
            <returns> The starting link. </returns>
        </member>
        <member name="F:System.Spatial.SpatialBuilder.geographyOutput">
            <summary>
              The builder to be delegated to when this class is accessed from the IGeographyPipeline or IGeographyProvider interfaces.
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialBuilder.geometryOutput">
            <summary>
              The builder to be delegated to when this class is accessed from the IGeometryPipeline or IGeometryProvider interfaces.
            </summary>
        </member>
        <member name="M:System.Spatial.SpatialBuilder.#ctor(System.Spatial.GeographyPipeline,System.Spatial.GeometryPipeline,System.Spatial.IGeographyProvider,System.Spatial.IGeometryProvider)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.SpatialBuilder" /> class.</summary>
            <param name="geographyInput">The geography input.</param>
            <param name="geometryInput">The geometry input.</param>
            <param name="geographyOutput">The geography output.</param>
            <param name="geometryOutput">The geometry output.</param>
        </member>
        <member name="M:System.Spatial.SpatialBuilder.Create">
            <summary>Creates an implementation of the builder.</summary>
            <returns>The created SpatialBuilder implementation.</returns>
        </member>
        <member name="E:System.Spatial.SpatialBuilder.ProduceGeography">
            <summary>Fires when the provider constructs geography object.</summary>
        </member>
        <member name="E:System.Spatial.SpatialBuilder.ProduceGeometry">
            <summary>Fires when the provider constructs geometry object.</summary>
        </member>
        <member name="P:System.Spatial.SpatialBuilder.ConstructedGeography">
            <summary>Gets the geography object that was constructed most recently.</summary>
            <returns>The geography object that was constructed.</returns>
        </member>
        <member name="P:System.Spatial.SpatialBuilder.ConstructedGeometry">
            <summary>Gets the geometry object that was constructed most recently.</summary>
            <returns>The geometry object that was constructed.</returns>
        </member>
        <member name="T:System.Spatial.SpatialFormatter`2">
            <summary>Represents the base class for all Spatial Formats.</summary>
            <typeparam name="TReaderStream">The type of reader to be read from.</typeparam>
            <typeparam name="TWriterStream">The type of reader to be read from.</typeparam>
        </member>
        <member name="F:System.Spatial.SpatialFormatter`2.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.#ctor(System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the &lt;see cref="T:System.Spatial.SpatialFormatter`2" /&gt; class. </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.Read``1(`0)">
            <summary> Parses the input, and produces the object.</summary>
            <returns>The input.</returns>
            <param name="input">The input to be parsed.</param>
            <typeparam name="TResult">The type of object to produce.</typeparam>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.Read``1(`0,System.Spatial.SpatialPipeline)">
            <summary> Parses the input, and produces the object.</summary>
            <param name="input">The input to be parsed.</param>
            <param name="pipeline">The pipeline to call during reading.</param>
            <typeparam name="TResult">The type of object to produce.</typeparam>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.Write(System.Spatial.ISpatial,`1)">
            <summary> Creates a valid format from the spatial object.</summary>
            <param name="spatial">The object that the format is being created for.</param>
            <param name="writerStream">The stream to write the formatted object to.</param>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.CreateWriter(`1)">
            <summary> Creates the writerStream. </summary>
            <returns>The writerStream that was created.</returns>
            <param name="writerStream">The stream that should be written to.</param>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.ReadGeography(`0,System.Spatial.SpatialPipeline)">
            <summary> Reads the Geography from the readerStream and call the appropriate pipeline methods.</summary>
            <param name="readerStream">The stream to read from.</param>
            <param name="pipeline">The pipeline to call based on what is read.</param>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.ReadGeometry(`0,System.Spatial.SpatialPipeline)">
            <summary> Reads the Geometry from the readerStream and call the appropriate pipeline methods.</summary>
            <param name="readerStream">The stream to read from.</param>
            <param name="pipeline">The pipeline to call based on what is read.</param>
        </member>
        <member name="M:System.Spatial.SpatialFormatter`2.MakeValidatingBuilder">
            <summary> Creates the builder that will be called by the parser to build the new type. </summary>
            <returns>The builder that was created.</returns>
        </member>
        <member name="T:System.Spatial.SpatialType">
            <summary> Defines a list of allowed OpenGisTypes types.  </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.Unknown">
            <summary>
            Unknown
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.Point">
            <summary>
            Point
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.LineString">
            <summary>
            Line String
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.Polygon">
            <summary>
            Polygon
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.MultiPoint">
            <summary>
            Multi-Point
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.MultiLineString">
            <summary>
            Multi-Line-String
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.MultiPolygon">
            <summary>
            Multi-Polygon
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.Collection">
            <summary>
            Collection
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialType.FullGlobe">
            <summary>
            Full Globe
            </summary>
        </member>
        <member name="T:System.Spatial.SpatialTypeExtensions">
            <summary>Provides a place to add extension methods that work with ISpatial.</summary>
        </member>
        <member name="M:System.Spatial.SpatialTypeExtensions.SendTo(System.Spatial.ISpatial,System.Spatial.SpatialPipeline)">
            <summary> Allows the delegation of the call to the proper type (geography or Geometry).</summary>
            <param name="shape">The instance that will have SendTo called.</param>
            <param name="destination">The pipeline that the instance will be sent to.</param>
        </member>
        <member name="T:System.Spatial.CoordinateSystem">
            <summary>
              Coordinate System Reference
            </summary>
        </member>
        <member name="F:System.Spatial.CoordinateSystem.DefaultGeometry">
            <summary>
              Default Geometry Reference
            </summary>
        </member>
        <member name="F:System.Spatial.CoordinateSystem.DefaultGeography">
            <summary>
              Default Geography Reference (SRID 4326, WGS84)
            </summary>
        </member>
        <member name="F:System.Spatial.CoordinateSystem.References">
            <summary>
              List of registered references
            </summary>
        </member>
        <member name="F:System.Spatial.CoordinateSystem.referencesLock">
            <summary>
              A lock object for the References static dict
            </summary>
        </member>
        <member name="F:System.Spatial.CoordinateSystem.topology">
            <summary>
              The shape of the space that this coordinate system measures.
            </summary>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.#cctor">
            <summary>Initializes a static instance of the <see cref="T:System.Spatial.CoordinateSystem" /> class.</summary>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.#ctor(System.Int32,System.String,System.Spatial.CoordinateSystem.Topology)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.CoordinateSystem" /> class.</summary>
            <param name = "epsgId">The coordinate system ID, according to the EPSG</param>
            <param name = "name">The Name of the system</param>
            <param name = "topology">The topology of this coordinate system</param>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.Geography(System.Nullable{System.Int32})">
            <summary>Gets or creates a Geography coordinate system with the ID, or the default if null is given.</summary>
            <returns>The coordinate system.</returns>
            <param name="epsgId">The coordinate system id, according to the EPSG. Null indicates the default should be returned.</param>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.Geometry(System.Nullable{System.Int32})">
            <summary>Gets or creates a Geometry coordinate system with the ID, or the default if null is given.</summary>
            <returns>The coordinate system.</returns>
            <param name="epsgId">The coordinate system id, according to the EPSG. Null indicates the default should be returned.</param>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.ToString">
            <summary>Displays the coordinate system for debugging.</summary>
            <returns>The coordinate system, for debugging.</returns>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.ToWktId">
            <summary>Displays a string that can be used with extended WKT.</summary>
            <returns>String representation in the form of SRID=#;</returns>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.Equals(System.Object)">
            <summary>Indicates the Equals overload.</summary>
            <returns>True if equal.</returns>
            <param name="obj">The other CoordinateSystem.</param>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.Equals(System.Spatial.CoordinateSystem)">
            <summary>Indicates the Equals overload.</summary>
            <returns>True if equal.</returns>
            <param name="other">The other CoordinateSystem.</param>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.GetHashCode">
            <summary>Returns a hash code for this instance.</summary>
            <returns>A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.</returns>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.TopologyIs(System.Spatial.CoordinateSystem.Topology)">
            <summary>
              For tests only. Identifies whether the coordinate system is of the designated topology.
            </summary>
            <param name = "expected">The expected topology.</param>
            <returns>True if this coordinate system is of the expected topology.</returns>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.GetOrCreate(System.Int32,System.Spatial.CoordinateSystem.Topology)">
            <summary>
              Get or create a CoordinateSystem with ID
            </summary>
            <param name = "epsgId">The SRID</param>
            <param name = "topology">The topology.</param>
            <returns>
              A CoordinateSystem object
            </returns>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.AddRef(System.Spatial.CoordinateSystem)">
            <summary>
              Remember this coordinate system in the references dictionary.
            </summary>
            <param name = "coords">The coords.</param>
        </member>
        <member name="M:System.Spatial.CoordinateSystem.KeyFor(System.Int32,System.Spatial.CoordinateSystem.Topology)">
            <summary>
              Gets the key for a coordinate system
            </summary>
            <param name = "epsgId">ID</param>
            <param name = "topology">topology</param>
            <returns>The key to use with the references dict.</returns>
        </member>
        <member name="P:System.Spatial.CoordinateSystem.EpsgId">
            <summary>Gets the coordinate system ID according to the EPSG, or NULL if this is not an EPSG coordinate system.</summary>
            <returns>The coordinate system ID according to the EPSG.</returns>
        </member>
        <member name="P:System.Spatial.CoordinateSystem.Id">
            <summary>Gets the coordinate system Id, no matter what scheme is used.</summary>
            <returns>The coordinate system Id.</returns>
        </member>
        <member name="P:System.Spatial.CoordinateSystem.Name">
            <summary>Gets the Name of the Reference.</summary>
            <returns>The Name of the Reference.</returns>
        </member>
        <member name="T:System.Spatial.CoordinateSystem.Topology">
            <summary>
              The shapes of the spaces measured by coordinate systems.
            </summary>
        </member>
        <member name="F:System.Spatial.CoordinateSystem.Topology.Geography">
            <summary>
              Ellipsoidal coordinates
            </summary>
        </member>
        <member name="F:System.Spatial.CoordinateSystem.Topology.Geometry">
            <summary>
              Planar coordinates
            </summary>
        </member>
        <member name="T:System.Spatial.ActionOnDispose">
            <summary>
            This class is responsible for executing an action the first time dispose is called on it.
            </summary>
        </member>
        <member name="F:System.Spatial.ActionOnDispose.action">
            <summary>The action to be executed on dispose</summary>
        </member>
        <member name="M:System.Spatial.ActionOnDispose.#ctor(System.Action)">
            <summary>
            Constructs an instance of the ActonOnDispose object
            </summary>
            <param name="action">the action to be execute on dispose</param>
        </member>
        <member name="M:System.Spatial.ActionOnDispose.Dispose">
            <summary>
            The dipose method of the IDisposable insterface
            </summary>
        </member>
        <member name="T:System.Spatial.GmlFormatter">
            <summary>
              The object to move spatial types to and from the GML format
            </summary>
        </member>
        <member name="M:System.Spatial.GmlFormatter.#ctor(System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.GmlFormatter" /> class.</summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.GmlFormatter.Create">
            <summary>Creates the implementation of the formatter.</summary>
            <returns>The created GmlFormatter implementation.</returns>
        </member>
        <member name="T:System.Spatial.WellKnownTextSqlFormatter">
            <summary>
            The object to move spatial types to and from the WellKnownTextSql format
            </summary>
        </member>
        <member name="M:System.Spatial.WellKnownTextSqlFormatter.#ctor(System.Spatial.SpatialImplementation)">
            <summary>Initializes a new instance of the <see cref="T:System.Spatial.WellKnownTextSqlFormatter" /> class.</summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:System.Spatial.WellKnownTextSqlFormatter.Create">
            <summary>Creates the implementation of the formatter.</summary>
            <returns>Returns the created WellKnownTextSqlFormatter implementation.</returns>
        </member>
        <member name="M:System.Spatial.WellKnownTextSqlFormatter.Create(System.Boolean)">
            <summary>Creates the implementation of the formatter and checks whether the specified formatter has Z.</summary>
            <returns>The created WellKnownTextSqlFormatter.</returns>
            <param name="allowOnlyTwoDimensions">Restricts the formatter to allow only two dimensions.</param>
        </member>
        <member name="T:System.Spatial.GeographyPosition">
            <summary>
            Represents one position in the Geographyal coordinate system
            </summary>
        </member>
        <member name="F:System.Spatial.GeographyPosition.latitude">
            <summary>lattitude portion of position</summary>
        </member>
        <member name="F:System.Spatial.GeographyPosition.longitude">
            <summary>longitude portion of position</summary>
        </member>
        <member name="F:System.Spatial.GeographyPosition.m">
            <summary>arbitrary measure associated with a position</summary>
        </member>
        <member name="F:System.Spatial.GeographyPosition.z">
            <summary>altitude portion of position</summary>
        </member>
        <member name="M:System.Spatial.GeographyPosition.#ctor(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a new instance of the <see cref="T:System.Spatial.GeographyPosition" /> class from components.</summary>
            <param name="latitude">The latitude portion of a position.</param>
            <param name="longitude">The longitude portion of a position.</param>
            <param name="z">The altitude portion of a position.</param>
            <param name="m">The arbitrary measure associated with a position.</param>
        </member>
        <member name="M:System.Spatial.GeographyPosition.#ctor(System.Double,System.Double)">
            <summary>Creates a new instance of the <see cref="T:System.Spatial.GeographyPosition" /> class from components.</summary>
            <param name="latitude">The latitude portion of a position.</param>
            <param name="longitude">The longitude portion of a position.</param>
        </member>
        <member name="M:System.Spatial.GeographyPosition.op_Equality(System.Spatial.GeographyPosition,System.Spatial.GeographyPosition)">
            <summary>Performs equality comparison.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The second position.</param>
        </member>
        <member name="M:System.Spatial.GeographyPosition.op_Inequality(System.Spatial.GeographyPosition,System.Spatial.GeographyPosition)">
            <summary>Performs inequality comparison.</summary>
            <returns>true if left is not equal to right; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The other position.</param>
        </member>
        <member name="M:System.Spatial.GeographyPosition.Equals(System.Object)">
            <summary>Performs equality comparison on an object.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="obj">The object for comparison.</param>
        </member>
        <member name="M:System.Spatial.GeographyPosition.Equals(System.Spatial.GeographyPosition)">
            <summary>Performs equality comparison on a spatial geographic position.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="other">The other position.</param>
        </member>
        <member name="M:System.Spatial.GeographyPosition.GetHashCode">
            <summary>Computes a hash code.</summary>
            <returns>A hash code.</returns>
        </member>
        <member name="M:System.Spatial.GeographyPosition.ToString">
            <summary>Formats this instance to a readable string.</summary>
            <returns>The string representation of this instance.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPosition.Latitude">
            <summary>Gets the latitude portion of a position.</summary>
            <returns>The latitude portion of a position.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPosition.Longitude">
            <summary>Gets the longitude portion of a position.</summary>
            <returns>The longitude portion of a position.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPosition.M">
            <summary>Gets the arbitrary measure associated with a position.</summary>
            <returns>The arbitrary measure associated with a position.</returns>
        </member>
        <member name="P:System.Spatial.GeographyPosition.Z">
            <summary>Gets the altitude portion of a position.</summary>
            <returns>The altitude portion of a position.</returns>
        </member>
        <member name="T:System.Spatial.GeometryPosition">
            <summary>
            Represents one position in the Geometry coordinate system
            </summary>
        </member>
        <member name="F:System.Spatial.GeometryPosition.m">
            <summary>arbitrary measure associated with a position</summary>
        </member>
        <member name="F:System.Spatial.GeometryPosition.x">
            <summary>x portion of position</summary>
        </member>
        <member name="F:System.Spatial.GeometryPosition.y">
            <summary>y portion of position</summary>
        </member>
        <member name="F:System.Spatial.GeometryPosition.z">
            <summary>altitude portion of position</summary>
        </member>
        <member name="M:System.Spatial.GeometryPosition.#ctor(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>Creates a new instance of the <see cref="T:System.Spatial.GeometryPosition" /> from components.</summary>
            <param name="x">The X portion of position.</param>
            <param name="y">The Y portion of position.</param>
            <param name="z">The altitude portion of position.</param>
            <param name="m">The arbitrary measure associated with a position.</param>
        </member>
        <member name="M:System.Spatial.GeometryPosition.#ctor(System.Double,System.Double)">
            <summary>Creates a new instance of the <see cref="T:System.Spatial.GeometryPosition" /> from components.</summary>
            <param name="x">The X portion of position.</param>
            <param name="y">The Y portion of position.</param>
        </member>
        <member name="M:System.Spatial.GeometryPosition.op_Equality(System.Spatial.GeometryPosition,System.Spatial.GeometryPosition)">
            <summary>Performs the equality comparison.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The second position.</param>
        </member>
        <member name="M:System.Spatial.GeometryPosition.op_Inequality(System.Spatial.GeometryPosition,System.Spatial.GeometryPosition)">
            <summary>Performs the inequality comparison.</summary>
            <returns>true if left is not equal to right; otherwise, false.</returns>
            <param name="left">The first position.</param>
            <param name="right">The other position.</param>
        </member>
        <member name="M:System.Spatial.GeometryPosition.Equals(System.Object)">
            <summary>Performs the equality comparison on an object.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="obj">The object for comparison.</param>
        </member>
        <member name="M:System.Spatial.GeometryPosition.Equals(System.Spatial.GeometryPosition)">
            <summary>Performs the equality comparison on a spatial geometry position.</summary>
            <returns>true if each pair of coordinates is equal; otherwise, false.</returns>
            <param name="other">The other position.</param>
        </member>
        <member name="M:System.Spatial.GeometryPosition.GetHashCode">
            <summary>Computes a hash code.</summary>
            <returns>A hash code.</returns>
        </member>
        <member name="M:System.Spatial.GeometryPosition.ToString">
            <summary>Formats this instance to a readable string.</summary>
            <returns>The string representation of this instance.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPosition.M">
            <summary>Gets the arbitrary measure associated with a position.</summary>
            <returns>The arbitrary measure associated with a position.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPosition.X">
            <summary>Gets the X portion of position.</summary>
            <returns>The X portion of position.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPosition.Y">
            <summary>Gets the Y portion of position.</summary>
            <returns>The Y portion of position.</returns>
        </member>
        <member name="P:System.Spatial.GeometryPosition.Z">
            <summary>Gets the altitude portion of position.</summary>
            <returns>The altitude portion of position.</returns>
        </member>
        <member name="T:System.Spatial.SpatialValidator">
            <summary>
            Base class for Spatial Type Validator implementations
            </summary>
        </member>
        <member name="M:System.Spatial.SpatialValidator.Create">
            <summary>Creates the currently registered SpatialValidator implementation.</summary>
            <returns>The created SpatialValidator.</returns>
        </member>
        <member name="T:System.Spatial.GeographyOperationsExtensions">
            <summary>
              Extension methods for the Geography operations
            </summary>
        </member>
        <member name="M:System.Spatial.GeographyOperationsExtensions.Distance(System.Spatial.Geography,System.Spatial.Geography)">
            <summary>Determines the distance of the geography.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand.</param>
            <param name="operand2">The second operand.</param>
        </member>
        <member name="M:System.Spatial.GeographyOperationsExtensions.Length(System.Spatial.Geography)">
            <summary>Determines the Length of the geography LineString.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The LineString operand.</param>
        </member>
        <member name="M:System.Spatial.GeographyOperationsExtensions.Intersects(System.Spatial.Geography,System.Spatial.Geography)">
            <summary>Determines if geography point and polygon will intersect.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand.</param>
            <param name="operand2">The second operand.</param>
        </member>
        <member name="M:System.Spatial.GeographyOperationsExtensions.OperationsFor(System.Spatial.Geography[])">
            <summary>
            Finds the ops instance registered for the operands.
            </summary>
            <param name="operands">The operands.</param>
            <returns>The ops value, or null if any operand is null</returns>
        </member>
        <member name="T:System.Spatial.GeometryOperationsExtensions">
            <summary>
            Extension methods for the Geography operations
            </summary>
        </member>
        <member name="M:System.Spatial.GeometryOperationsExtensions.Distance(System.Spatial.Geometry,System.Spatial.Geometry)">
            <summary>Determines the distance of the geometry.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand.</param>
            <param name="operand2">The second operand.</param>
        </member>
        <member name="M:System.Spatial.GeometryOperationsExtensions.Length(System.Spatial.Geometry)">
            <summary>Determines the Length of the geometry LineString.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The LineString operand.</param>
        </member>
        <member name="M:System.Spatial.GeometryOperationsExtensions.Intersects(System.Spatial.Geometry,System.Spatial.Geometry)">
            <summary>Determines if geometry point and polygon will intersect.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The first operand, point.</param>
            <param name="operand2">The second operand, polygon.</param>
        </member>
        <member name="M:System.Spatial.GeometryOperationsExtensions.OperationsFor(System.Spatial.Geometry[])">
            <summary>
            Finds the ops instance registered for the operands.
            </summary>
            <param name="operands">The operands.</param>
            <returns>The ops value, or null if any operand is null</returns>
        </member>
        <member name="T:System.Spatial.PriorityQueue`1">
            <summary>
            Queue where things are seen in highest priority (highest compare) order 
            </summary>
            <typeparam name="TValue">The type of the values stored in priority order</typeparam>
        </member>
        <member name="F:System.Spatial.PriorityQueue`1.data">
            <summary>
            The list of queued items.
            This is non-generic to avoid issues with the NetCF's reflection stack.
            </summary>
        </member>
        <member name="M:System.Spatial.PriorityQueue`1.#ctor">
            <summary>
            Initializes a new instance of the PriorityQueue class
            DEVNOTE: this is only here for the FxCop suppression.
            </summary>
        </member>
        <member name="M:System.Spatial.PriorityQueue`1.Peek">
            <summary>
            Returns the top queue value without removing it.
            </summary>
            <returns>The top value of the queue</returns>
        </member>
        <member name="M:System.Spatial.PriorityQueue`1.Enqueue(System.Double,`0)">
            <summary>
            Adds a new value to the queue by priority.
            </summary>
            <param name="priority">The priority of the new item to add.</param>
            <param name="value">The new item being added.</param>
        </member>
        <member name="M:System.Spatial.PriorityQueue`1.Contains(System.Double)">
            <summary>
            Returns a value indicating whether there is already an item with the given priority in the queue
            </summary>
            <param name="priority">The priority to check</param>
            <returns>Whether or not an item with the given priority is in the queue</returns>
        </member>
        <member name="M:System.Spatial.PriorityQueue`1.DequeueByPriority(System.Double)">
            <summary>
            Removes the item with the priority specified from the queue
            </summary>
            <param name="priority">The priority of the item to be removed from the queue</param>
            <returns>The value of the removed item.</returns>
        </member>
        <member name="P:System.Spatial.PriorityQueue`1.Count">
            <summary>
            Gets the number of items in the queue
            </summary>
        </member>
        <member name="T:System.Spatial.SpatialImplementation">
            <summary>
            Class responsible for knowing how to create the Geography and Geometry builders for 
            a particular implemenation of Spatial types
            </summary>
        </member>
        <member name="F:System.Spatial.SpatialImplementation.spatialImplementation">
            <summary>Default Spatial Implementation.</summary>
        </member>
        <member name="M:System.Spatial.SpatialImplementation.CreateBuilder">
            <summary> Creates a SpatialBuilder for this implementation.</summary>
            <returns>The SpatialBuilder created.</returns>
        </member>
        <member name="M:System.Spatial.SpatialImplementation.CreateGeoJsonObjectFormatter">
            <summary> Creates a Formatter for Json Object.</summary>
            <returns>The JsonObjectFormatter created.</returns>
        </member>
        <member name="M:System.Spatial.SpatialImplementation.CreateGmlFormatter">
            <summary> Creates a GmlFormatter for this implementation.</summary>
            <returns>The GmlFormatter created.</returns>
        </member>
        <member name="M:System.Spatial.SpatialImplementation.CreateWellKnownTextSqlFormatter">
            <summary> Creates a WellKnownTextSqlFormatter for this implementation.</summary>
            <returns>The WellKnownTextSqlFormatter created.</returns>
        </member>
        <member name="M:System.Spatial.SpatialImplementation.CreateWellKnownTextSqlFormatter(System.Boolean)">
            <summary> Creates a WellKnownTextSqlFormatter for this implementation.</summary>
            <returns>The WellKnownTextSqlFormatter created.</returns>
            <param name="allowOnlyTwoDimensions">Controls the writing and reading of the Z and M dimension.</param>
        </member>
        <member name="M:System.Spatial.SpatialImplementation.CreateValidator">
            <summary> Creates a spatial Validator.</summary>
            <returns>The SpatialValidator created.</returns>
        </member>
        <member name="M:System.Spatial.SpatialImplementation.VerifyAndGetNonNullOperations">
            <summary>
            This method throws if the operations instance is null. It returns a non-null operations implementation.
            </summary>
            <returns>a SpatialOperations implementation.</returns>
        </member>
        <member name="P:System.Spatial.SpatialImplementation.CurrentImplementation">
            <summary> Returns an instance of SpatialImplementation that is currently being used. </summary>
        </member>
        <member name="P:System.Spatial.SpatialImplementation.Operations">
            <summary>Gets or sets the Spatial operations implementation.</summary>
        </member>
        <member name="T:System.Spatial.SpatialOperations">
            <summary>
            Class responsible for knowing how to perform operations for a particular implemenation of Spatial types
            </summary>
        </member>
        <member name="M:System.Spatial.SpatialOperations.Distance(System.Spatial.Geometry,System.Spatial.Geometry)">
            <summary>Indicates the Geometry Distance.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1.</param>
            <param name="operand2">The Operand 2.</param>
        </member>
        <member name="M:System.Spatial.SpatialOperations.Distance(System.Spatial.Geography,System.Spatial.Geography)">
            <summary>Indicates a Geography Distance.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1.</param>
            <param name="operand2">The Operand 2.</param>
        </member>
        <member name="M:System.Spatial.SpatialOperations.Length(System.Spatial.Geometry)">
            <summary>Indicates the Geometry LineString's length.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The Operand.</param>
        </member>
        <member name="M:System.Spatial.SpatialOperations.Length(System.Spatial.Geography)">
            <summary>Indicates a Geography LineString's length.</summary>
            <returns>The operation result.</returns>
            <param name="operand">The Operand.</param>
        </member>
        <member name="M:System.Spatial.SpatialOperations.Intersects(System.Spatial.Geometry,System.Spatial.Geometry)">
            <summary>Indicates the Geometry Intersects() method.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1, point.</param>
            <param name="operand2">The Operand 2, polygon.</param>
        </member>
        <member name="M:System.Spatial.SpatialOperations.Intersects(System.Spatial.Geography,System.Spatial.Geography)">
            <summary>Indicates a Geography Intersects() method.</summary>
            <returns>The operation result.</returns>
            <param name="operand1">The Operand 1, point.</param>
            <param name="operand2">The Operand 2, polygon.</param>
        </member>
        <member name="T:System.Spatial.OrcasExtensions">
            <summary>
            This class holds extension methods for objects that have new capabilities
            in newer versions of .net, and this lets us make the calls look the same and reduces the #if noise
            </summary>
        </member>
        <member name="M:System.Spatial.OrcasExtensions.Clear(System.Text.StringBuilder)">
            <summary>
            StringBuilder didn't have a clear method in Orcas, so we added and extension method to give it one.
            </summary>
            <param name="builder">The StringBuilder instance to clear.</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation">
            <summary>
            The object to move spatial types to and from the WellKnownTextSql format
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation.allowOnlyTwoDimensions">
            <summary>
            restricts the writer and reader to allow only two dimensions.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation.#ctor(System.Spatial.SpatialImplementation,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="allowOnlyTwoDimensions">restricts the reader to allow only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation.CreateWriter(System.IO.TextWriter)">
            <summary>
            Create the writer
            </summary>
            <param name="target">The object that should be the target of the ISpatialPipeline writer.</param>
            <returns>A writer that implements ISpatialPipeline.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation.ReadGeography(System.IO.TextReader,System.Spatial.SpatialPipeline)">
            <summary>
            Reads the geography.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlFormatterImplementation.ReadGeometry(System.IO.TextReader,System.Spatial.SpatialPipeline)">
            <summary>
            Reads the geometry.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.GmlWriter">
            <summary>
            Gml Writer
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlWriter.writer">
            <summary>
            The underlying writer
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlWriter.parentStack">
            <summary>
            Stack of spatial types currently been built
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlWriter.coordinateSystemWritten">
            <summary>
            If an SRID has been written already.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlWriter.currentCoordinateSystem">
            <summary>
            The Coordinate System to write
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlWriter.figureWritten">
            <summary>
            Figure has been written to the current spatial type
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlWriter.shouldWriteContainerWrapper">
            <summary>
            Whether there are shapes written in the current container
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.#ctor(System.Xml.XmlWriter)">
            <summary>
            Constructor
            </summary>
            <param name="writer">The Xml Writer to output to</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnBeginGeography(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnLineTo(System.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnBeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>The type to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnLineTo(System.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnSetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>The coordinateSystem to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnBeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnBeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin a figure
            </summary>
            <param name="x">The first coordinate</param>
            <param name="y">The second coordinate</param>
            <param name="z">The optional third coordinate</param>
            <param name="m">The optional fourth coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.BeginGeo(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.WriteStartElement(System.String)">
            <summary>
            Write the element with namespaces
            </summary>
            <param name="elementName">The element name</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.WriteCoordinateSystem">
            <summary>
            Write coordinate system
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.WritePoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Write a Point
            </summary>
            <param name="x">The first coordinate</param>
            <param name="y">The second coordinate</param>
            <param name="z">The optional third coordinate</param>
            <param name="m">The optional fourth coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlWriter.EndGeo">
            <summary>
            End Geography/Geometry
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GmlFormatterImplementation">
            <summary>
            The object to move spatial types to and from the GML format
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlFormatterImplementation.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.GmlFormatterImplementation"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlFormatterImplementation.CreateWriter(System.Xml.XmlWriter)">
            <summary>
            Create the writer
            </summary>
            <param name="target">The object that should be the target of the ISpatialPipeline writer.</param>
            <returns>A writer that implements ISpatialPipeline.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlFormatterImplementation.ReadGeography(System.Xml.XmlReader,System.Spatial.SpatialPipeline)">
            <summary>
            Reads the geography.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlFormatterImplementation.ReadGeometry(System.Xml.XmlReader,System.Spatial.SpatialPipeline)">
            <summary>
            Reads the geometry.
            </summary>
            <param name="readerStream">The reader stream.</param>
            <param name="pipeline">The pipeline.</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.WellKnownTextLexer">
            <summary>
            WellKnownText Lexer
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.TextLexerBase">
            <summary>
            Lexer base
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.TextLexerBase.reader">
            <summary>
            Input text
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.TextLexerBase.currentToken">
            <summary>
            Current lexer output
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.TextLexerBase.peekToken">
            <summary>
            Peek lexer output, if this is not null then we have advanced already
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TextLexerBase.#ctor(System.IO.TextReader)">
            <summary>
            Constructor
            </summary>
            <param name="text">The input text</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TextLexerBase.Peek(Microsoft.Data.Spatial.LexerToken@)">
            <summary>
            Peek one token ahead of the current position
            </summary>
            <param name="token">The peeked token</param>
            <returns>True if there is one more token after the current position, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.TextLexerBase.Next">
            <summary>
            Move to the next token
            </summary>
            <returns>True if lexer has moved, otherwise false</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.TextLexerBase.MatchTokenType(System.Char,System.Nullable{System.Int32},System.Int32@)">
            <summary>
            Examine the current character and determine its token type
            </summary>
            <param name="nextChar">The char that will be read next</param>
            <param name="currentType">The currently active token type</param>
            <param name="type">The matched token type</param>
            <returns>Whether the current character is a delimiter, thereby terminate the current token immediately</returns>
        </member>
        <member name="P:Microsoft.Data.Spatial.TextLexerBase.CurrentToken">
            <summary>
            Current token
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextLexer.#ctor(System.IO.TextReader)">
            <summary>
            Constructor
            </summary>
            <param name="text">Input text</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextLexer.MatchTokenType(System.Char,System.Nullable{System.Int32},System.Int32@)">
            <summary>
            Examine the current character and determine its token type
            </summary>
            <param name="nextChar">The next char that will be read.</param>
            <param name="activeTokenType">The currently active token type</param>
            <param name="tokenType">The matched token type</param>
            <returns>Whether the current character is a delimiter, thereby terminate the current token immediately</returns>
        </member>
        <member name="T:Microsoft.Data.Spatial.WellKnownTextTokenType">
            <summary>
            WellKnownText Lexer Token Type
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.Text">
            <summary>
            A-Z only support upper case text. i.e., POINT() instead of Point() or point()
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.Equals">
            <summary>
            character '='
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.Number">
            <summary>
            characters '0' to '9'
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.Semicolon">
            <summary>
            character ';'
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.LeftParen">
            <summary>
            character '('
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.RightParen">
            <summary>
            character ')'
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.Period">
            <summary>
            character '.'
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.Comma">
            <summary>
            character ','
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextTokenType.WhiteSpace">
            <summary>
            character ' ', '\t'
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.DataServicesSpatialImplementation">
            <summary>
            Class responsible for knowing how to create the Geography and Geometry builders for 
            the data services implemenation of Spatial types
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DataServicesSpatialImplementation.CreateBuilder">
            <summary>
            Creates a SpatialBuilder for this implemenation
            </summary>
            <returns>
            The SpatialBuilder created.
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DataServicesSpatialImplementation.CreateGmlFormatter">
            <summary>
            Creates a GmlFormatter for this implementation
            </summary>
            <returns>The GmlFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DataServicesSpatialImplementation.CreateGeoJsonObjectFormatter">
            <summary>
            Creates a GeoJsonObjectFormatter for this implementation
            </summary>
            <returns>The GeoJsonObjectFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DataServicesSpatialImplementation.CreateWellKnownTextSqlFormatter">
            <summary>
            Creates a WellKnownTextSqlFormatter for this implementation
            </summary>
            <returns>The WellKnownTextSqlFormatter created.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DataServicesSpatialImplementation.CreateWellKnownTextSqlFormatter(System.Boolean)">
            <summary>
            Creates a WellKnownTextSqlFormatter for this implementation
            </summary>
            <param name="allowOnlyTwoDimensions">Controls the writing and reading of the Z and M dimension</param>
            <returns>
            The WellKnownTextSqlFormatter created.
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.DataServicesSpatialImplementation.CreateValidator">
            <summary>
            Creates a SpatialValidator for this implementation
            </summary>
            <returns>The SpatialValidator created.</returns>
        </member>
        <member name="P:Microsoft.Data.Spatial.DataServicesSpatialImplementation.Operations">
            <summary>
            Property used to register Spatial operations implementation.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyBuilderImplementation">
            <summary>
            Builder for Geography types
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyBuilderImplementation.builder">
            <summary>
            The tree builder
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.LineTo(System.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.BeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.BeginGeography(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.EndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="E:Microsoft.Data.Spatial.GeographyBuilderImplementation.ProduceGeography">
            <summary>
            Fires when the provider constructs a geography object.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyBuilderImplementation.ConstructedGeography">
            <summary>
            Constructed Geography
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder">
            <summary>
            Geography Tree Builder
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialTreeBuilder`1">
            <summary>
            Tree based builder for spatial types
            </summary>
            <typeparam name="T">Geography or Geometry</typeparam>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialTreeBuilder`1.currentFigure">
            <summary>
            The figure this builder is currently building
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialTreeBuilder`1.currentNode">
            <summary>
            Current builder tree root
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialTreeBuilder`1.lastConstructedNode">
            <summary>
            lastConstructed
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="x">X or Latitude Coordinate</param>
            <param name="y">Y or Longitude Coordinate</param>
            <param name="z">Z Coordinate</param>
            <param name="m">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">X or Latitude Coordinate</param>
            <param name="coordinate2">Y or Longitude Coordinate</param>
            <param name="coordinate3">Z Coordinate</param>
            <param name="coordinate4">M Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.BeginGeo(System.Spatial.SpatialType)">
            <summary>
            Begin a new spatial type
            </summary>
            <param name="type">The spatial type</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.EndFigure">
            <summary>
            Ends the figure set on the current node
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.CreatePoint(System.Boolean,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a new instance of Point
            </summary>
            <param name="isEmpty">Whether the point is empty</param>
            <param name="x">X</param>
            <param name="y">Y</param>
            <param name="z">Z</param>
            <param name="m">M</param>
            <returns>A new instance of point</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.CreateShapeInstance(System.Spatial.SpatialType,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Create a new instance of T
            </summary>
            <param name="type">The spatial type to create</param>
            <param name="spatialData">The arguments</param>
            <returns>A new instance of T</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.NotifyIfWeJustFinishedBuildingSomething">
            <summary>
            Notifies if we just finished building something.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.TraverseUpTheTree">
            <summary>
            Traverses up the tree.
            </summary>
        </member>
        <member name="E:Microsoft.Data.Spatial.SpatialTreeBuilder`1.ProduceInstance">
            <summary>
            Fires when the builder creates a top-level spatial object.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialTreeBuilder`1.ConstructedInstance">
            <summary>
            Get the constructed spatial instance
            </summary>
            <returns>The constructed spatial instance</returns>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialTreeBuilder`1.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode">
            <summary>
            A spatial instance node in the builder tree
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.CreateChildren(System.Spatial.SpatialType)">
            <summary>
            Create a child node
            </summary>
            <param name="type">The node type</param>
            <returns>The child node</returns>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Children">
            <summary>
            Children nodes
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Instance">
            <summary>
            Instance
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Parent">
            <summary>
            Parent node
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialTreeBuilder`1.SpatialBuilderNode.Type">
            <summary>
            Spatial Type
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.currentCoordinateSystem">
            <summary>
            CoordinateSystem for the building geography
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.CreatePoint(System.Boolean,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a new instance of Point
            </summary>
            <param name="isEmpty">Whether the point is empty</param>
            <param name="x">X</param>
            <param name="y">Y</param>
            <param name="z">Z</param>
            <param name="m">M</param>
            <returns>A new instance of point</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyBuilderImplementation.GeographyTreeBuilder.CreateShapeInstance(System.Spatial.SpatialType,System.Collections.Generic.IEnumerable{System.Spatial.Geography})">
            <summary>
            Create a new instance of T
            </summary>
            <param name="type">The spatial type to create</param>
            <param name="spatialData">The arguments</param>
            <returns>A new instance of T</returns>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryBuilderImplementation">
            <summary>
            Builder for Geometry types
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryBuilderImplementation.builder">
            <summary>
            The tree builder
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.LineTo(System.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.BeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.BeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.EndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="E:Microsoft.Data.Spatial.GeometryBuilderImplementation.ProduceGeometry">
            <summary>
            Fires when the provider constructs a geometry object.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryBuilderImplementation.ConstructedGeometry">
            <summary>
            Constructed Geography
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder">
            <summary>
            Geography Tree Builder
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.creator">
            <summary>
            The implementation that created this instance.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.buildCoordinateSystem">
            <summary>
            CoordinateSystem for the building geography
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder"/> class.
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.CreatePoint(System.Boolean,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Create a new instance of Point
            </summary>
            <param name="isEmpty">Whether the point is empty</param>
            <param name="x">X</param>
            <param name="y">Y</param>
            <param name="z">Z</param>
            <param name="m">M</param>
            <returns>A new instance of point</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryBuilderImplementation.GeometryTreeBuilder.CreateShapeInstance(System.Spatial.SpatialType,System.Collections.Generic.IEnumerable{System.Spatial.Geometry})">
            <summary>
            Create a new instance of T
            </summary>
            <param name="type">The spatial type to create</param>
            <param name="spatialData">The arguments</param>
            <returns>A new instance of T</returns>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyLineStringImplementation">
            <summary>
            A Geography linestring consist of an array of GeoPoints
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyLineStringImplementation.points">
            <summary>
            Points array
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyLineStringImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeographyPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">The point list</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyLineStringImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyLineStringImplementation.IsEmpty">
            <summary>
            Is LineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyLineStringImplementation.Points">
            <summary>
            Point list
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation">
            <summary>
            Geography Multi-LineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation.lineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation.IsEmpty">
            <summary>
            Is MultiLineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation.Geographies">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiLineStringImplementation.LineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyMultiPointImplementation">
            <summary>
            Geography Multi-Point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyMultiPointImplementation.points">
            <summary>
            Points
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiPointImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeographyPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiPointImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeographyPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiPointImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiPointImplementation.IsEmpty">
            <summary>
            Is MultiPoint Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiPointImplementation.Geographies">
            <summary>
            Geography
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiPointImplementation.Points">
            <summary>
            Points
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation">
            <summary>
            Geography Multi-Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation.polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeographyPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeographyPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation.IsEmpty">
            <summary>
            Is MultiPolygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation.Geographies">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyMultiPolygonImplementation.Polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyPointImplementation">
            <summary>
            This class is an implementation of Geography point.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyPointImplementation.latitude">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyPointImplementation.longitude">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyPointImplementation.z">
            <summary>
            Z
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyPointImplementation.m">
            <summary>
            M
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyPointImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Point constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="latitude">latitude</param>
            <param name="longitude">longitude</param>
            <param name="zvalue">Z</param>
            <param name="mvalue">M</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyPointImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>
            Create a empty point
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyPointImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyPointImplementation.Latitude">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyPointImplementation.Longitude">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyPointImplementation.IsEmpty">
            <summary>
            Is Point Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyPointImplementation.Z">
            <summary>
            Nullable Z
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyPointImplementation.M">
            <summary>
            Nullable M
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyPolygonImplementation">
            <summary>
            Geography polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyPolygonImplementation.rings">
            <summary>
            Rings
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyPolygonImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyPolygonImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeographyLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyPolygonImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyPolygonImplementation.IsEmpty">
            <summary>
            Is Polygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyPolygonImplementation.Rings">
            <summary>
            Set of rings
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyCollectionImplementation">
            <summary>
            Geography Collection
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeographyCollectionImplementation.geographyArray">
            <summary>
            Collection of geography instances
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyCollectionImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.Geography[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geography">Collection of geography instances</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyCollectionImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.Geography[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geography">Collection of geography instances</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyCollectionImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyCollectionImplementation.IsEmpty">
            <summary>
            Is Geography Collection Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyCollectionImplementation.Geographies">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyHelperMethods">
            <summary>
            Helper methods for the geography type.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyHelperMethods.SendFigure(System.Spatial.GeographyLineString,System.Spatial.GeographyPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline with a figure that represents this LineString
            </summary>
            <param name="lineString">GeographyLineString instance to serialize.</param>
            <param name="pipeline">The pipeline to populate to</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeographyFullGlobeImplementation">
            <summary>
            Implementation of FullGlobe
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyFullGlobeImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyFullGlobeImplementation.#ctor(System.Spatial.SpatialImplementation)">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeographyFullGlobeImplementation.SendTo(System.Spatial.GeographyPipeline)">
            <summary>
            Sends the spatial geography object to the given sink
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeographyFullGlobeImplementation.IsEmpty">
            <summary>
            Is FullGlobe empty
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryLineStringImplementation">
            <summary>
            Geometry Line String
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryLineStringImplementation.points">
            <summary>
            Points array
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryLineStringImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">The point list</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryLineStringImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">The point list</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryLineStringImplementation.SendTo(System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryLineStringImplementation.IsEmpty">
            <summary>
            Is LineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryLineStringImplementation.Points">
            <summary>
            Point list
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation">
            <summary>
            Geometry Multi-LineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation.lineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="lineStrings">Line Strings</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation.SendTo(System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation.IsEmpty">
            <summary>
            Is MultiLineString Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation.Geometries">
            <summary>
            Geometry
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiLineStringImplementation.LineStrings">
            <summary>
            Line Strings
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryMultiPointImplementation">
            <summary>
            Geometry Multi-Point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryMultiPointImplementation.points">
            <summary>
            Points
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiPointImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiPointImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeometryPoint[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="points">Points</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiPointImplementation.SendTo(System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiPointImplementation.IsEmpty">
            <summary>
            Is MultiPoint Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiPointImplementation.Geometries">
            <summary>
            Geometry
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiPointImplementation.Points">
            <summary>
            Points
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation">
            <summary>
            Geometry Multi-Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation.polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeometryPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeometryPolygon[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="polygons">Polygons</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation.SendTo(System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation.IsEmpty">
            <summary>
            Is MultiPolygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation.Geometries">
            <summary>
            Geometry
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryMultiPolygonImplementation.Polygons">
            <summary>
            Polygons
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryPointImplementation">
            <summary>
            Geometry Point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryPointImplementation.x">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryPointImplementation.y">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryPointImplementation.z">
            <summary>
            Z
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryPointImplementation.m">
            <summary>
            M
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryPointImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation)">
            <summary>
            Empty Point constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryPointImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Point constructor
            </summary>
            <param name="coordinateSystem">CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="x">latitude</param>
            <param name="y">longitude</param>
            <param name="z">Z</param>
            <param name="m">M</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryPointImplementation.SendTo(System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryPointImplementation.X">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryPointImplementation.Y">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryPointImplementation.IsEmpty">
            <summary>
            Is Point Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryPointImplementation.Z">
            <summary>
            Nullable Z
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryPointImplementation.M">
            <summary>
            Nullable M
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryPolygonImplementation">
            <summary>
            Geometry polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryPolygonImplementation.rings">
            <summary>
            Rings
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryPolygonImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryPolygonImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.GeometryLineString[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="rings">The rings of this polygon</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryPolygonImplementation.SendTo(System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryPolygonImplementation.IsEmpty">
            <summary>
            Is Polygon Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryPolygonImplementation.Rings">
            <summary>
            Set of rings
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryCollectionImplementation">
            <summary>
            Geometry Collection
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeometryCollectionImplementation.geometryArray">
            <summary>
            Collection of Geometry instances
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryCollectionImplementation.#ctor(System.Spatial.CoordinateSystem,System.Spatial.SpatialImplementation,System.Spatial.Geometry[])">
            <summary>
            Constructor
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geometry">Collection of Geometry instances</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryCollectionImplementation.#ctor(System.Spatial.SpatialImplementation,System.Spatial.Geometry[])">
            <summary>
            Constructor
            </summary>
            <param name="creator">The implementation that created this instance.</param>
            <param name="geometry">Collection of Geometry instances</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryCollectionImplementation.SendTo(System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline
            </summary>
            <param name="pipeline">The spatial pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryCollectionImplementation.IsEmpty">
            <summary>
            Is Geometry Collection Empty
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.GeometryCollectionImplementation.Geometries">
            <summary>
            Geographies
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.DebugUtils">
            <summary>
            Dummy class for code that is shared with ODataLib.
            The ODataLib version of this class has an implementation, but this version is just provided
            so that we don't have to conditionally compile all references to it in the shared code.
            Since it is debug-only anyway, there is no harm in leaving this no-op version so that the shared code is cleaner.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.DebugUtils.CheckNoExternalCallers">
            <summary>
            Dummy method to allow shared code to compile.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeometryHelperMethods">
            <summary>
            Helper methods for Geometry types
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GeometryHelperMethods.SendFigure(System.Spatial.GeometryLineString,System.Spatial.GeometryPipeline)">
            <summary>
            Sends the current spatial object to the given pipeline with a figure that represents this LineString
            </summary>
            <param name="GeometryLineString">GeometryLineString instance for which the figure needs to be drawn.</param>
            <param name="pipeline">The pipeline to populate to</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.GmlReader">
            <summary>
            Gml Reader
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Creates a reader that that will send messages to the destination during read.
            </summary>
            <param name="destination">The instance to message to during read.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.ReadGeographyImplementation(System.Xml.XmlReader)">
            <summary>
            Parses some serialized format that represents a geography value, passing the result down the pipeline.
            </summary>
            <param name = "input">The XmlReader instance to read from.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.ReadGeometryImplementation(System.Xml.XmlReader)">
            <summary>
            Parses some serialized format that represents a geometry value, passing the result down the pipeline.
            </summary>
            <param name = "input">The XmlReader instance to read from.</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.GmlReader.Parser">
            <summary>
            This class parses the xml and calls the pipeline based on what is parsed
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlReader.Parser.coordinateDelimiter">
            <summary>
            Delimiters used in position arrays. As per Xml spec white space characters is: #x20 | #x9 | #xD | #xA
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlReader.Parser.skippableElements">
            <summary>
            List of known gml elements that can be ignored by the parser
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlReader.Parser.gmlNamespace">
            <summary>
            Atomized gml namespace
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlReader.Parser.fullGlobeNamespace">
            <summary>
            Atomized Full Globe namespace
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlReader.Parser.pipeline">
            <summary>
            Output pipeline
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlReader.Parser.reader">
            <summary>
            Input reader
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlReader.Parser.points">
            <summary>
            Number of points in the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.#ctor(System.Xml.XmlReader,Microsoft.Data.Spatial.TypeWashedPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="reader">Input Reader</param>
            <param name="pipeline">Output pipeline</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.Read">
            <summary>
            Read
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlGeometry(System.Boolean)">
            <summary>
            Parses the top level element in the document
            </summary>
            <param name="readCoordinateSystem">Whether coordinte system is expected</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ReadAttributes(System.Boolean)">
            <summary>
            Set the CoordinateSystem
            </summary>
            <param name="expectSrsName">Should we allow CRS attributes</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlPointShape">
            <summary>
            creates a shape and parses the element.
            This is used to parse a top level Point element, as opposed to
            a point which is embeded in a linestring or a polygon.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlLineStringShape">
            <summary>
             creates a shape and parses the element for top level LineString shapes
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlPolygonShape">
            <summary>
            Creates a shape and parses the Polygon element. 
            </summary>    
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlMultiPointShape">
            <summary>
            Creates a shape and parses the MultiPoint element. 
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlMultiCurveShape">
            <summary>
            Creates a shape and parses the MultiLineString(Gml MultiCurve) element.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlMultiSurfaceShape">
            <summary>
            Creates a shape and parses the MultiPolygon(Gml MultiSurface) element.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlMultiGeometryShape">
            <summary>
            Creates a shape and parses the Collection(Gml MultiGeometry) element.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlFullGlobeElement">
            <summary>
            Creates a shape and parses the FullGlobe element
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlPointElement(System.Boolean)">
            <summary>
            Parses a simple point.
            </summary>
            <param name="allowEmpty">Allow Empty Point</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlLineString">
            <summary>
            Parses the GmlLineStringElement.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlRingElement(System.String)">
            <summary>
            Parses the GmlExteriorLinearRingElement
            </summary>
            <param name="ringTag">The type or ring</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlLinearRingElement">
            <summary>
            ParseGmlLinearRingElement parses the GmlLinearRingElement
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseMultiItemElement(System.String,System.String,System.String,System.Action)">
            <summary>
            Common function for all item collections, since they are all parsed exactly the same way
            </summary>
            <param name="header">The wrapping header tag</param>
            <param name="member">The member tag</param>
            <param name="members">The members tag</param>
            <param name="parseItem">Parser for individual items</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlPosElement(System.Boolean)">
            <summary>
            parses a pos element, which eventually is used in most other top level elements.
            This represents a single point location with either two or zero coordinates.
            </summary>
            <param name="allowEmpty">Allow empty pos</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParsePosList(System.Boolean)">
            <summary>
            Parses a sequence of 1 or more pos and pointProperty elements
            </summary>
            <param name="allowEmpty">Allow Empty Point</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlPointPropertyElement(System.Boolean)">
            <summary>
            Parses a simple pointProperty.
            </summary>
            <param name="allowEmpty">Allow empty point</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ParseGmlPosListElement(System.Boolean)">
            <summary>
            parses a GmlPosListElement.
            </summary>
            <param name="allowEmpty">Alow empty posList</param>        
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ReadContentAsDoubleArray">
            <summary>
            Reads the current content in the xml element as a double array
            </summary>
            <remarks>
            XmlReader.ReadContentAs(typeof(double[])) basically does this but a lot slower, since it will handle a bunch of
            different splitters and formats. Here we simply parse it as a string and split in on one separator
            </remarks>
            <returns>The double array</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ReadStartOrEmptyElement(System.String)">
            <summary>
            Main element reading function.
            Returns true if it read a non-empty start element of the given name.
            possibilities:
                1- current element is not a start element named "element" - throw
                2- current element is named "element" but is an empty element - return false
                3- current element is named "element" and is not empty - return true
            If the funciton returns true, it means that a non-empty element of the given name
            was read, so the caller takes responsability to read the corresponding end element.
            </summary>
            <param name="element">The element name</param>
            <returns>Returns true if it read a non-empty start element of the given name.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.IsStartElement(System.String)">
            <summary>
            Is Start Element
            </summary>
            <param name="element">Expected Element Tag</param>
            <returns>True if reader is at the expected element</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.IsEndElement(System.String)">
            <summary>
            Is End Element
            </summary>
            <param name="element">Expected Element Tag</param>
            <returns>True if reader is at the end of the expected element</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ReadEndElement">
            <summary>
            Read End Element
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.ReadSkippableElements">
            <summary>
            Call MoveToContent, then skip a known set of irrelevant elements (gml:name, gml:description)
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.IsPosListStart">
            <summary>
            Is reader at the start of a pos or pointProperty
            </summary>
            <returns>True if reader is at the expected element</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.PrepareFigure">
            <summary>
            Prepare for figure drawing
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.AddPoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a point in the current figure
            </summary>
            <param name="x">X coordinate</param>
            <param name="y">Y coordinate</param>
            <param name="z">Z coordinate</param>
            <param name="m">M coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.GmlReader.Parser.EndFigure">
            <summary>
            End Current Figure
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GmlConstants">
            <summary>
            Gml Constants
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.GmlNamespace">
            <summary>
            Gml Namespace
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.FullGlobeNamespace">
            <summary>
            FullGlobe namespace
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.GmlPrefix">
            <summary>
            Gml Prefix
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.SrsName">
            <summary>
            System reference attribute name
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.IdName">
            <summary>
            gml:id attribute name
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.SrsPrefix">
            <summary>
            System Reference Prefix
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.Position">
            <summary>
            Gml representation of a point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.Name">
            <summary>
            The Gml:name element name
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.Description">
            <summary>
            the Gml:Description element name
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.MetadataProperty">
            <summary>
            the metadata property element name
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.DescriptionReference">
            <summary>
            Description Reference element name
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.IdentifierElement">
            <summary>
            identifier element name
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.PointProperty">
            <summary>
            Gml representation of a point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.PositionList">
            <summary>
            Gml representation of a point array
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.Point">
            <summary>
            Gml Point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.LineString">
            <summary>
            Gml representation of a linestring
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.Polygon">
            <summary>
            Gml Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.MultiPoint">
            <summary>
            Gml MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.MultiLineString">
            <summary>
            Gml MultiLineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.MultiPolygon">
            <summary>
            Gml MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.Collection">
            <summary>
            Gml Collection
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.FullGlobe">
            <summary>
            Gml FullGlobe
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.ExteriorRing">
            <summary>
            Gml Polygon exterior ring
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.InteriorRing">
            <summary>
            Gml Polygon interior ring
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.LinearRing">
            <summary>
            Gml Ring
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.PointMember">
            <summary>
            Member Tag for MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.PointMembers">
            <summary>
            Members Tag for MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.LineStringMember">
            <summary>
            Member Tag for MultiLineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.LineStringMembers">
            <summary>
            Members Tag for MultiLineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.PolygonMember">
            <summary>
            Member Tag for MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.PolygonMembers">
            <summary>
            Members Tag for MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.CollectionMember">
            <summary>
            Member Tag for Collection
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.CollectionMembers">
            <summary>
            Members Tag for Collection
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.AxisLabels">
            <summary>
            Attribute name for Axis Labels
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.UomLabels">
            <summary>
            Attribute name for unit of measure labels
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GmlConstants.Count">
            <summary>
            Attribute name for count 
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.WellKnownTextConstants">
            <summary>
            Well Known Text Constants
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktSrid">
            <summary>
            SRID
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktPoint">
            <summary>
            POINT
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktLineString">
            <summary>
            LINESTRING
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktPolygon">
            <summary>
            POLYGON
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktCollection">
            <summary>
            GEOMETRYCOLLECTION
            DEVNOTE: Because there is no inherent Geography support in the WKT specification,
            this constant is used for both GeographyCollection and GeometryCollection
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktMultiPoint">
            <summary>
            MULTIPOINT
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktMultiLineString">
            <summary>
            MULTILINESTRING
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktMultiPolygon">
            <summary>
            MULTIPOLYGON
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktFullGlobe">
            <summary>
            FULLGLOBE
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktEmpty">
            <summary>
            NULL
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktNull">
            <summary>
            NULL
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktEquals">
            <summary>
            Equals Operator '='
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktSemiColon">
            <summary>
            Semicolon ';'
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktDelimiterWithWhiteSpace">
            <summary>
            Delimiter ',' + WktWhiteSpace
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktOpenParen">
            <summary>
            Open Parenthesis '('
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktCloseParen">
            <summary>
            Close Parenthesis ');
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktWhitespace">
            <summary>
            Whitespace ' '
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextConstants.WktPeriod">
            <summary>
            Period/Dot '.'
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline">
            <summary>
            Adapter from the type washed API to Geometry, where it assumes that coord1 is X.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.output">
            <summary>
            The pipeline to redirect the calls to
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Constructor
            </summary>
            <param name="output">The pipeline to redirect the calls to</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.SetCoordinateSystem(System.Nullable{System.Int32})">
            <summary>
            Set the coordinate system based on the given ID
            </summary>
            <param name="epsgId">The coordinate system ID to set. Null indicates the default should be used</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.BeginGeo(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.BeginFigure(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.LineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Draw a line to a point in the specified coordinate
            </summary>
            <param name="coordinate1">1st Coordinate</param>
            <param name="coordinate2">2nd Coordinate</param>
            <param name="coordinate3">3rd Coordinate</param>
            <param name="coordinate4">4th Coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.EndGeo">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.TypeWashedToGeometryPipeline.IsGeography">
            <summary>
            Gets a value indicating whether this instance is geography.
            </summary>
            <value>
            <c>true</c> if this instance is geography; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation">
            <summary>
            Semantically validate a GeoData
            </summary>
            <remarks>
            Grammar, states, and actions:
            <![CDATA[
                <Document> := SetSRID <Geometry> { Finish }
                <Geometry> := (Begin_Point <Point> | ... | Begin_FullGlobe (: verify depth = 1 :) <FullGlobe>)
                <Point> := [ BeginFigure 1 EndFigure ] 2 End
                <LineString> := [ BeginFigure 1 { LineTo } EndFigure (: verify 2+ points :) ] 2 End
                <Polygon> := { BeginFigure 1 { LineTo } EndFigure (: verify 4+ points and closed :) } End
                <MultiPoint> := { { SetSRID } Begin_Point <Point> } End
                <MultiLineString> := { { SetSRID } Begin_LineString <LineString> } End
                <MultiPolygon> := { { SetSRID } Begin_Polygon <Polygon> } End
                <GeometryCollection> := { { SetSRID } <Geometry> } End
                <FullGlobe> := End
                <CircularString> := [ BeginFigure 1 { AddCircularArc } EndFigure ] 2 End
                <CompoundCurve> := [ BeginFigure 1 { LineTo | AddCircularArc } EndFigure ] | <StructuredCompoundCurve> 2 End
                <StructuredCompoundCurve> := <StructuredCompoundCurveStart> { <StructuredCompoundCurvePart> } EndFigure
                <StructuredCompoundCurveStart> := AddSegmentLine 0 BeginFigure { LineTo } | AddSegmentArc 0 BeginFigure { AddCircularArc }
                <StructuredCompoundCurvePart> := AddSegmentLine { LineTo } | AddSegmentArc { AddCircularArc }
                <CurvePolygon> := { <CurvePolygonImplicitRing> | <CurvePolygonSimpleRing> | <CurvePolygonCompoundCurveRing> EndFigure (: verify closed and three distinct :)} End
                <CurvePolygonImplicitRing> := BeginFigure 1 { LineTo | AddCircularArc }
                <CurvePolygonSimpleRing> := StartSimpleRing 0 <CurvePolygonImplicitRing>
                <CurvePolygonCompoundCurveRing> := <CurvePolygonCompoundCurveRingStart> { <CurvePolygonCompoundCurveRingPart> }
                <CurvePolygonCompoundCurveRingStart> := AddSegmentLine 0 BeginFigure { LineTo } | AddSegmentArc 0 BeginFigure { AddCircularArc }
                <CurvePolygonCompoundCurveRingPart> := AddSegmentLine { LineTo } | AddSegmentArc { AddCircularArc }    
            ]]>
            </remarks>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.MaxLongitude">
            <summary>
            Max value for Longitude
            </summary>
            <remarks>
            ~263 radians converted to degrees
            </remarks>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.MaxLatitude">
            <summary>
            Max value for latitude
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.geographyValidatorInstance">
            <summary>
            The DrawBoth derived instance of the geography Validator that is nested in this class
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.geometryValidatorInstance">
            <summary>
            The DrawBoth derived instance of the geometry Validator that is nested in this class
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialValidatorImplementation.GeographyPipeline">
            <summary>
            Gets the draw geography.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.SpatialValidatorImplementation.GeometryPipeline">
            <summary>
            Gets the draw geometry.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator">
            <summary>
            this is the actual validator, and derived from DrawBoth
            while the real SpatialValidator derives from DrawSpatial.
            We simple create an instance of this nested class and pass back
            the DrawGeometry, and DrawGeography when the outter classes DataSpatial
            properties are accessed.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MaxGeometryCollectionDepth">
            <summary>
            Geometry Functional Specification *******
            Max Geometry Collection Depth
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.CoordinateSystem">
            <summary>
            Set coordinate system
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.BeginSpatial">
            <summary>
            BeginGeo
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointStart">
            <summary>
            Starting a point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointBuilding">
            <summary>
            Building a point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointEnd">
            <summary>
            Ending a point
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringStart">
            <summary>
            Starting a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringBuilding">
            <summary>
            Building a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringEnd">
            <summary>
            Ending a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonStart">
            <summary>
            Starting a Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonBuilding">
            <summary>
            Building a Polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPoint">
            <summary>
            Starting a MultiPoint
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiLineString">
            <summary>
            Starting a LineString
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPolygon">
            <summary>
            Starting a MultiPolygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.Collection">
            <summary>
            Starting a Collection
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.FullGlobe">
            <summary>
            Starting a FullGlobe
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.stack">
            <summary>
            States
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.validationCoordinateSystem">
            <summary>
            CoordinateSystem
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ringCount">
            <summary>
            Number of rings in a polygon
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.initialFirstCoordinate">
            <summary>
            First point's X coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.initialSecondCoordinate">
            <summary>
            First point's Y coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.mostRecentFirstCoordinate">
            <summary>
            Last point's X coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.mostRecentSecondCoordinate">
            <summary>
            Last point's Y coordinate
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.processingGeography">
            <summary>
            we are validating a geography stream
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.pointCount">
            <summary>
            Number of points in the GeoData
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.depth">
            <summary>
            Stack depth
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.#ctor">
            <summary>
            Constructs a new SpatialValidatorImplementation segment
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnSetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Implemented by a subclass to handle the setting of a coordinate system
            </summary>
            <param name="coordinateSystem">the new coordinate system</param>
            <returns>the coordinate system to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginGeography(System.Spatial.SpatialType)">
            <summary>
            Implemented by a subclass to handle the start of drawing a Geography figure
            </summary>
            <param name="shape">the  shape to draw</param>
            <returns>the SpatialType to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnEndGeography">
            <summary>
            Implemented by a subclass to handle the end of drawing a Geography figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Implemented by a subclass to handle the start of drawing a Geometry figure
            </summary>
            <param name="shape">the  shape to draw</param>
            <returns>the SpatialType to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnEndGeometry">
            <summary>
            Implemented by a subclass to handle the end of drawing a Geometry figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Implemented by a subclass to handle the start of a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnBeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Implemented by a subclass to handle the start of a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnEndFigure">
            <summary>
            Implemented by a subclass to handle the end of a figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnReset">
            <summary>
            Implemented by a subclass to return to its initial state
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnLineTo(System.Spatial.GeographyPosition)">
            <summary>
            Implemented by a subclass to handle the addition of a waypoint to a Geography figure
            </summary>
            <param name="position">Next position</param>
            <returns>the GeographyPosition to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.OnLineTo(System.Spatial.GeometryPosition)">
            <summary>
            Implemented by a subclass to handle the addition of a waypoint to a Geometry figure
            </summary>
            <param name="position">Next position</param>
            <returns>the GeometryPosition to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.IsFinite(System.Double)">
            <summary>
            Test whether a double is finite
            </summary>
            <param name="value">The double value</param>
            <returns>True if the input double is not NaN or INF</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.IsPointValid(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Test whether a point is in valid format
            </summary>
            <param name="first">The first coordinate</param>
            <param name="second">The second coordinate</param>
            <param name="z">The z coordinate</param>
            <param name="m">The m coordinate</param>
            <returns>Whether the input coordinate is valid</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateOnePosition(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Validate one position
            </summary>
            <param name="first">the first two dimensional co-ordinate</param>
            <param name="second">the second two dimensional co-ordinate</param>
            <param name="z">the altitude</param>
            <param name="m">the measure</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeographyPosition(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Validate one Geography position
            </summary>
            <param name="latitude">the latitude</param>
            <param name="longitude">the longitude</param>
            <param name="z">the altitude</param>
            <param name="m">the measure</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeometryPosition(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Validate one Geography position
            </summary>
            <param name="x">the x coordinate</param>
            <param name="y">the y coordinate</param>
            <param name="z">the altitude</param>
            <param name="m">the measure</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.IsLatitudeValid(System.Double)">
            <summary>
            Test whether a latitude value is within acceptable range
            </summary>
            <param name="latitude">The latitude value</param>
            <returns>True if the latitude value is within range</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.IsLongitudeValid(System.Double)">
            <summary>
            Test whether a longitude value is within acceptable range
            </summary>
            <param name="longitude">The longitude value</param>
            <returns>True if the longitude value is within range</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeographyPolygon(System.Int32,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Validate a Geography polygon
            </summary>
            <param name="numOfPoints">The number of points in the ring</param>
            <param name="initialFirstCoordinate">its first latitude</param>
            <param name="initialSecondCoordinate">it first longitued</param>
            <param name="mostRecentFirstCoordinate">its last latitude</param>
            <param name="mostRecentSecondCoordinate">its last longitude</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidateGeometryPolygon(System.Int32,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Validate a Geometry polygon
            </summary>
            <param name="numOfPoints">The number of points in the ring</param>
            <param name="initialFirstCoordinate">its first x</param>
            <param name="initialSecondCoordinate">it first y</param>
            <param name="mostRecentFirstCoordinate">its last x</param>
            <param name="mostRecentSecondCoordinate">its last y</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.AreLongitudesEqual(System.Double,System.Double)">
            <summary>
            Test whether two longitude values are equal
            </summary>
            <param name="left">Left longitude</param>
            <param name="right">Right longitude</param>
            <returns>True if the two longitudes are equals</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.BeginFigure(System.Action{System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double}},System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Begins the figure.
            </summary>
            <param name="validate">The validate action.</param>
            <param name="x">The x.</param>
            <param name="y">The y.</param>
            <param name="z">The z.</param>
            <param name="m">The m.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.BeginShape(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.AddControlPoint(System.Double,System.Double)">
            <summary>
             Add a control point to the current figure.
            </summary>
            <param name="first">the first coordinate</param>
            <param name="second">the second coordinate</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.TrackPosition(System.Double,System.Double)">
            <summary>
            Tracks the position.
            </summary>
            <param name="first">The first.</param>
            <param name="second">The second.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.Execute(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Transit into a new state
            </summary>
            <param name="transition">The state to transit into</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.InitializeObject">
            <summary>
             initialize the object to a fresh clean smelling state
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.Call(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState)">
            <summary>
            Push a new state onto the stack
            </summary>
            <param name="state">The new state</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.Return">
            <summary>
            Pop a state from the stack
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.Jump(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState)">
            <summary>
            Replace the current state on the stack with the new state
            </summary>
            <param name="state">The new state</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall">
            <summary>
            Calls to the pipeline interface Represented as state transition
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.SetCoordinateSystem">
            <summary>
            Set CoordinateSystem
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.Begin">
            <summary>
            BeginGeo()
            </summary>
            <remarks>fake transition, just for exception</remarks>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginPoint">
            <summary>
            BeginGeo(point)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginLineString">
            <summary>
            BeginGeo(LineString)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginPolygon">
            <summary>
            BeginGeo(Polygon)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginMultiPoint">
            <summary>
            BeginGeo(MultiPoint)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginMultiLineString">
            <summary>
            BeginGeo(MultiLineString)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginMultiPolygon">
            <summary>
            BeginGeo(MultiPolygon)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginCollection">
            <summary>
            BeginGeo(Collection)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginFullGlobe">
            <summary>
            BeginGeo(FullGlobe)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.BeginFigure">
            <summary>
            BeginFigure
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.LineTo">
            <summary>
            LineTo
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.EndFigure">
            <summary>
            EndFigure
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall.End">
            <summary>
            EndGeo
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState">
            <summary>
            SpatialValidatorImplementation State
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ThrowExpected(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Throw an incorrect state exception
            </summary>
            <param name="transition">The expected state</param>
            <param name="actual">The actual state</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ThrowExpected(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Throw an incorrect state exception
            </summary>
            <param name="transition1">The expected state1</param>
            <param name="transition2">The expected state2</param>
            <param name="actual">The actual state</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.ValidatorState.ThrowExpected(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall)">
            <summary>
            Throw an incorrect state exception
            </summary>
            <param name="transition1">The expected state1</param>
            <param name="transition2">The expected state2</param>
            <param name="transition3">The expected state3</param>
            <param name="actual">The actual state</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.SetCoordinateSystemState">
            <summary>
            SetCoordinateSystem State
            Validator is currently waiting for a SetCoordinateSystemCall
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.SetCoordinateSystemState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.BeginGeoState">
            <summary>
            Beginning a GeoData
            Validator is currently waiting for a BeginGeo() call
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.BeginGeoState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointStartState">
            <summary>
            Point Start State
            After BeginGeo(Point), waiting for BeginFigure() or EndGeo()
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointStartState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointBuildingState">
            <summary>
            Point Building State
            After BeginFigure(), waiting for EndFigure() immediately
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointBuildingState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointEndState">
            <summary>
            Point End State
            After EndFigure() for a point, waiting for EndGeo()
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PointEndState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringStartState">
            <summary>
            LineString Start state
            After BeginGeo(LineString), waiting for BeginFigure/EndGeo
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringStartState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringBuildingState">
            <summary>
            LineString Building State
            After BeginFigure() for a line
            Waiting for LineTo/EndFigure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringBuildingState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringEndState">
            <summary>
            LineString End State
            After EndFigure() on Line
            Waiting for EndGeo
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.LineStringEndState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonStartState">
            <summary>
            PolygonStart State
            After polygon started, waiting for Rings to build
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonStartState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonBuildingState">
            <summary>
            Polygon Building State
            Drawing rings
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PolygonBuildingState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPointState">
            <summary>
            MultiPoint State
            Inside a MultiPoint Container
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPointState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiLineStringState">
            <summary>
            MultiLineString State
            Inside a MultiLineString container
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiLineStringState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPolygonState">
            <summary>
            MultiPolygon State
            Inside a MultiPolygon container
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.MultiPolygonState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.CollectionState">
            <summary>
            Collection State
            Inside a Collection container
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.CollectionState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.FullGlobeState">
            <summary>
            FullGlobe state
            Inside a FullGlobe container
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.FullGlobeState.ValidateTransition(Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator.PipelineCall,Microsoft.Data.Spatial.SpatialValidatorImplementation.NestedValidator)">
            <summary>
            Validate a call to the pipeline interface (a state transition)
            </summary>
            <param name="transition">The transition</param>
            <param name="validator">The validator instance</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.ExtensionMethods">
            <summary>
              Extension methods for TextWriter
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ExtensionMethods.WriteRoundtrippable(System.IO.TextWriter,System.Double)">
            <summary>
              Write a double to a TextWriter ensuring that the value will be roundtrippable thorugh double.parse
            </summary>
            <param name = "writer">the writer</param>
            <param name = "d">the double value to be written</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ExtensionMethods.IfValidReturningNullable``2(``0,System.Func{``0,``1})">
            <summary>
            If the arg is non-null, evaluate the op. Otherwise, propogate the null.
            </summary>
            <typeparam name="TArg">The type of the arg.</typeparam>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="arg">The arg.</param>
            <param name="op">The op.</param>
            <returns>op(arg) if arg is non-null; null if arg is null.</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.ExtensionMethods.IfValid``2(``0,System.Func{``0,``1})">
            <summary>
            If the arg is non-null, evaluate the op. Otherwise, propogate the null.
            </summary>
            <typeparam name="TArg">The type of the arg.</typeparam>
            <typeparam name="TResult">The type of the result.</typeparam>
            <param name="arg">The arg.</param>
            <param name="op">The op.</param>
            <returns>op(arg) if arg is non-null; null if arg is null.</returns>
        </member>
        <member name="T:Microsoft.Data.Spatial.LexerToken">
            <summary>
            Text Lexer Token
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.LexerToken.MatchToken(System.Int32,System.String,System.StringComparison)">
            <summary>
            Test whether this token matches the input criterion
            </summary>
            <param name="targetType">The target type</param>
            <param name="targetText">The target text, or null</param>
            <param name="comparison">The StringComparison</param>
            <returns>True if this token matches the input criterion</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.LexerToken.ToString">
            <summary>
            String representation of this token
            </summary>
            <returns>String representation of this token</returns>
        </member>
        <member name="P:Microsoft.Data.Spatial.LexerToken.Text">
            <summary>
            The Token Text
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.LexerToken.Type">
            <summary>
            Token Type
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.WellKnownTextSqlReader">
            <summary>
            Reader for Extended Well Known Text, Case sensitive
            example:
            SRID=1234;POINT(10.0 20.0 NULL 30.0)
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlReader.allowOnlyTwoDimensions">
            <summary>
            restricts the reader to allow only two dimensions.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Creates a reader that that will send messages to the destination during read.
            </summary>
            <param name="destination">The instance to message to during read.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.#ctor(System.Spatial.SpatialPipeline,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.WellKnownTextSqlReader"/> class.
            </summary>
            <param name="destination">The destination.</param>
            <param name="allowOnlyTwoDimensions">if set to <c>true</c> allows only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.ReadGeographyImplementation(System.IO.TextReader)">
            <summary>
            Parses some serialized format that represents a geography value, passing the result down the pipeline.
            </summary>
            <param name = "input">TextReader instance to read from.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.ReadGeometryImplementation(System.IO.TextReader)">
            <summary>
            Parses some serialized format that represents a geometry value, passing the result down the pipeline.
            </summary>
            <param name = "input">TextReader instance to read from.</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser">
            <summary>
            This class parses the text and calls the pipeline based on what is parsed
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.allowOnlyTwoDimensions">
            <summary>
            restricts the parser to allow only two dimensions.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.lexer">
            <summary>
            Text lexer
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.pipeline">
            <summary>
            Output pipeline
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.#ctor(System.IO.TextReader,Microsoft.Data.Spatial.TypeWashedPipeline,System.Boolean)">
            <summary>
            Creates a parser with the given reader and pipeline
            </summary>
            <param name="reader">The reader that is the source of what is parsed.</param>
            <param name="pipeline">The pipeline to be called as the parser recognizes tokens.</param>
            <param name="allowOnlyTwoDimensions">if set to <c>true</c> allows only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.Read">
            <summary>
            Read WellKnownText into an instance of Geography
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.IsTokenMatch(Microsoft.Data.Spatial.WellKnownTextTokenType,System.String)">
            <summary>
            Test whether the current token matches the expected token
            </summary>
            <param name="type">The expected token type</param>
            <param name="text">The expected token text</param>
            <returns>True if the two tokens match</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.NextToken">
            <summary>
            Move the lexer to the next non-whitespace token
            </summary>
            <returns>True if the lexer gets a new token</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParseCollectionText">
            <summary>
            Parse Collection Text
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParseLineStringText">
            <summary>
            Parse a LineString text
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParseMultiGeoText(System.Spatial.SpatialType,System.Action)">
            <summary>
            Parse a Multi* text
            </summary>
            <param name="innerType">The inner spatial type</param>
            <param name="innerReader">The inner reader</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParsePoint(System.Boolean)">
            <summary>
            Parse Point Representation
            </summary>
            <param name="firstFigure">Whether this is the first point in the figure</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParsePointText">
            <summary>
            Parse a point text
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParsePolygonText">
            <summary>
            Parse a Polygon text
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParseSRID">
            <summary>
            Parse an instance of SRID
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ParseTaggedText">
            <summary>
            Parse Tagged Text
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ReadDouble">
            <summary>
            Read a double literal
            </summary>
            <returns>The read double</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ReadEmptySet">
            <summary>
            Check to see if the content is EMPTY
            </summary>
            <returns>True if the content is declared as EMPTY</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ReadInteger">
            <summary>
            Read an integer literal
            </summary>
            <returns>The read integer</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.TryReadOptionalNullableDouble(System.Nullable{System.Double}@)">
            <summary>
            Read an optional double literal
            </summary>
            <param name="value">The value that was read.</param>
            <returns>true if a value was read, otherwise returns false</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ReadOptionalToken(Microsoft.Data.Spatial.WellKnownTextTokenType,System.String)">
            <summary>
            Read an optional token. If the read token matches the expected optional token, then consume it.
            </summary>
            <param name="expectedTokenType">The expected token type</param>
            <param name="expectedTokenText">The expected token text, or null</param>
            <returns>True if the optional token matches the next token in stream</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlReader.Parser.ReadToken(Microsoft.Data.Spatial.WellKnownTextTokenType,System.String)">
            <summary>
            Read and consume a token from the lexer, throw if the read token does not match the expected token
            </summary>
            <param name="type">The expected token type</param>
            <param name="text">The expected token text</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.WellKnownTextSqlWriter">
            <summary>
            WellKnownText Writer
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlWriter.allowOnlyTwoDimensions">
            <summary>
            restricts the writer to allow only two dimensions.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlWriter.writer">
            <summary>
            The underlying writer
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlWriter.parentStack">
            <summary>
            Stack of spatial types currently been built
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlWriter.coordinateSystemWritten">
            <summary>
            Detects if a CoordinateSystem (SRID) has been written already.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlWriter.figureWritten">
            <summary>
            Figure has been written to the current spatial type
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.WellKnownTextSqlWriter.shapeWritten">
            <summary>
            A shape has been written in the current nesting level
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.#ctor(System.IO.TextWriter)">
            <summary>
            Wells the known text SQL format. -- 2D writer
            </summary>
            <param name="writer">The writer.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.#ctor(System.IO.TextWriter,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.WellKnownTextSqlWriter"/> class.
            </summary>
            <param name="writer">The writer.</param>
            <param name="allowOnlyTwoDimensions">if set to <c>true</c> allows only two dimensions.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnLineTo(System.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnLineTo(System.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
            <returns>
            The position to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnBeginGeography(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnBeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
            <returns>
            The type to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnBeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnBeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
            <returns>The position to be passed down the pipeline</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnEndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnEndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnEndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnSetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
            <returns>
            the coordinate system to be passed down the pipeline
            </returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.OnReset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.WriteCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Write the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.BeginGeo(System.Spatial.SpatialType)">
            <summary>
            Start to write a new Geography/Geometry
            </summary>
            <param name="type">The SpatialType to write</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.AddLineTo(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Adds the control point.
            </summary>
            <param name="x">The x.</param>
            <param name="y">The y.</param>
            <param name="z">The z.</param>
            <param name="m">The m.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.EndFigure">
            <summary>
            Ends the figure.
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.WriteTaggedText(System.Spatial.SpatialType)">
            <summary>
            write tagged text for type
            </summary>
            <param name="type">the spatial type</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.WriteFigureScope(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Start to write a figure
            </summary>
            <param name="coordinate1">The coordinate1.</param>
            <param name="coordinate2">The coordinate2.</param>
            <param name="coordinate3">The coordinate3.</param>
            <param name="coordinate4">The coordinate4.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.EndGeo">
            <summary>
            End the current Geography/Geometry
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.WellKnownTextSqlWriter.WritePoint(System.Double,System.Double,System.Nullable{System.Double},System.Nullable{System.Double})">
            <summary>
            Write out a point
            </summary>
            <param name="x">The x coordinate</param>
            <param name="y">The y coordinate</param>
            <param name="z">The z coordinate</param>
            <param name="m">The m coordinate</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeoJsonMember">
            <summary>
            Defines the members that may be found in a GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonMember.Type">
            <summary>
            "type" member in a GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonMember.Coordinates">
            <summary>
            "coordinates" member in GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonMember.Geometries">
            <summary>
            "geometries" member in GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonMember.Crs">
            <summary>
            "crs" member in GeoJSON object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonMember.Properties">
            <summary>
            'properties' member in GeoJSON object
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonMember.Name">
            <summary>
            'name' member in GeoJSON object
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.GeoJsonConstants">
            <summary>
            Constants for the GeoJSON format
            See http://geojson.org/geojson-spec.html for full details on GeoJson format.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberName">
            <summary>
            Name of the type member that identifies the spatial type.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberValuePoint">
            <summary>
            Value of the type member for Point values.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberValueLineString">
            <summary>
            Value of the type member for LineString values.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberValuePolygon">
            <summary>
            Value of the type member for Polygon values.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberValueMultiPoint">
            <summary>
            Value of the type member for MultiPoint values.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberValueMultiLineString">
            <summary>
            Value of the type member for MultiLineString values.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberValueMultiPolygon">
            <summary>
            Value of the type member for MultiPolygon values.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.TypeMemberValueGeometryCollection">
            <summary>
            Value of the type member for GeometryCollection values.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.CoordinatesMemberName">
            <summary>
            Name of the coordinates member that contains the spatial data.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.GeometriesMemberName">
            <summary>
            Name of the geometries member that contains the spatial data.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.CrsMemberName">
            <summary>
            Name of the crs member that contains the coordinate reference system details.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.CrsTypeMemberValue">
            <summary>
            Value of the type member inside of the crs object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.CrsNameMemberName">
            <summary>
            Name of the name member inside of the properties member in the crs object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.CrsPropertiesMemberName">
            <summary>
            Name of the properties member inside of the crs object.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.GeoJsonConstants.CrsValuePrefix">
            <summary>
            Prefix to use when specifying the coordinate reference system inside the crs object.
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.ForwardingSegment">
            <summary>
            This is a forwarding transform pipe segment
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.ForwardingSegment.SpatialPipelineNoOp">
            <summary>
            The singleton NoOp implementation of the DrawGeography
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.ForwardingSegment.current">
            <summary>
            The current drawspatial that will be called and whose results will be forwarded to the 
            next segment
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.ForwardingSegment.next">
            <summary>
            The SpatialPipeline to forward the calls to
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.ForwardingSegment.geographyForwarder">
            <summary>
            the cached GeographyForwarder for this instance
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.ForwardingSegment.geometryForwarder">
            <summary>
            the cached GeometryForwarder for this instance
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.#ctor(System.Spatial.SpatialPipeline)">
            <summary>
            Constructs a new SpatialPipeline segment
            </summary>
            <param name="current">The DrawSpatial to draw to before calling next.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.#ctor(System.Spatial.GeographyPipeline,System.Spatial.GeometryPipeline)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.ForwardingSegment"/> class.
            </summary>
            <param name="currentGeography">The current geography.</param>
            <param name="currentGeometry">The current geometry.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.ChainTo(System.Spatial.SpatialPipeline)">
            <summary>
            Add the next pipeline
            </summary>
            <param name="destination">the next pipleine</param>
            <returns>The last pipesegment in the chain, usually the one just created</returns>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.DoAction(System.Action,System.Action,System.Action,System.Action)">
            <summary>
            Run one action on a pipeline
            </summary>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="handlerReset">The handler reset.</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="delegationReset">The delegation reset.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.DoAction``1(System.Action{``0},System.Action,System.Action{``0},System.Action,``0)">
            <summary>
            Run one action on a pipeline
            </summary>
            <typeparam name="T">The type taken and returned by the transform style methods.</typeparam>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="handlerReset">The handler reset.</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="delegationReset">The delegation reset.</param>
            <param name="argument">The argument to pass to both this node and the rest of the pipeline</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.GeographyPipeline">
            <summary>
            Gets the geography.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.GeometryPipeline">
            <summary>
            Gets the geometry.
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.NextDrawGeography">
            <summary>
            The next geography sink in  the pipeline
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.NextDrawGeometry">
            <summary>
            The next geometry sink in the pipeline
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder">
            <summary>
            The forwarding implementation of DrawGeography
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.segment">
            <summary>
            The ForwardingSegment instance that this pipe is 
            associated with
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.#ctor(Microsoft.Data.Spatial.ForwardingSegment)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder"/> class.
            </summary>
            <param name="segment">The segment.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the system reference to be used by this run of the pipeline
            </summary>
            <param name="coordinateSystem">the coordinate reference system</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.BeginGeography(System.Spatial.SpatialType)">
            <summary>
            start processing Geography data
            </summary>
            <param name="type">the sort of Geography data being processed</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.EndGeography">
            <summary>
            finish processing Geography data
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.BeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a Geography figure 
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.EndFigure">
            <summary>
            Finish drawing a Geography figure 
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.LineTo(System.Spatial.GeographyPosition)">
            <summary>
            Continue drawing a Geography figure 
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.Reset">
            <summary>
            Reset the piprline
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.DoAction``1(System.Action{``0},System.Action{``0},``0)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <typeparam name="T">The type taken and returned by the transform style methods.</typeparam>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="argument">The argument to pass to both this node and the rest of the pipeline</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.DoAction(System.Action,System.Action)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.Current">
            <summary>
            Gets the current DrawGeography from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.GeographyForwarder.Next">
            <summary>
            Gets the next GeographyPipeline from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder">
            <summary>
            The forwarding implementation of DrawGeography
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.segment">
            <summary>
            The ForwardingSegment instance that this pipe is 
            associated with
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.#ctor(Microsoft.Data.Spatial.ForwardingSegment)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder"/> class.
            </summary>
            <param name="segment">The segment.</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the system reference to be used by this run of the pipeline
            </summary>
            <param name="coordinateSystem">the coordinate reference system</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.BeginGeometry(System.Spatial.SpatialType)">
            <summary>
            start processing Geometry data
            </summary>
            <param name="type">the sort of Geometry data being processed</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.EndGeometry">
            <summary>
            finish processing Geometry data
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.BeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a Geometry figure 
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.EndFigure">
            <summary>
            Finish drawing a Geometry figure 
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.LineTo(System.Spatial.GeometryPosition)">
            <summary>
            Continue drawing a Geometry figure 
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.Reset">
            <summary>
            Reset the piprline
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.DoAction``1(System.Action{``0},System.Action{``0},``0)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <typeparam name="T">The type taken and returned by the transform style methods.</typeparam>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
            <param name="argument">The argument to pass to both this node and the rest of the pipeline</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.DoAction(System.Action,System.Action)">
            <summary>
            Run one action on a Geography pipeline
            </summary>
            <param name="handler">what to do at this stage of the pipeline</param>
            <param name="delegation">what the rest of the pipeline should do</param>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.Current">
            <summary>
            Gets the current DrawGeometry from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="P:Microsoft.Data.Spatial.ForwardingSegment.GeometryForwarder.Next">
            <summary>
            Gets the next GeometryPipeline from the associated ForwardingSegment instance
            </summary>
        </member>
        <member name="T:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline">
            <summary>
            A noop implementation of DrawGeography
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline.LineTo(System.Spatial.GeographyPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline.BeginFigure(System.Spatial.GeographyPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline.BeginGeography(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline.EndGeography">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeographyPipeline.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline">
            <summary>
            a noop implementation of DrawGeometry
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline.LineTo(System.Spatial.GeometryPosition)">
            <summary>
            Draw a point in the specified coordinate
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline.BeginFigure(System.Spatial.GeometryPosition)">
            <summary>
            Begin drawing a figure
            </summary>
            <param name="position">Next position</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline.BeginGeometry(System.Spatial.SpatialType)">
            <summary>
            Begin drawing a spatial object
            </summary>
            <param name="type">The spatial type of the object</param>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline.EndFigure">
            <summary>
            Ends the current figure
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline.EndGeometry">
            <summary>
            Ends the current spatial object
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline.Reset">
            <summary>
            Setup the pipeline for reuse
            </summary>
        </member>
        <member name="M:Microsoft.Data.Spatial.ForwardingSegment.NoOpGeometryPipeline.SetCoordinateSystem(System.Spatial.CoordinateSystem)">
            <summary>
            Set the coordinate system
            </summary>
            <param name="coordinateSystem">The CoordinateSystem</param>
        </member>
        <member name="T:System.Spatial.Util">
            <summary>
            Util class
            </summary>
        </member>
        <member name="F:System.Spatial.Util.OutOfMemoryType">
            <summary>OutOfMemoryException exception type</summary>
        </member>
        <member name="F:System.Spatial.Util.NullReferenceType">
            <summary>NullReferenceException exception type</summary>
        </member>
        <member name="F:System.Spatial.Util.SecurityType">
            <summary>SecurityException exception type</summary>
        </member>
        <member name="M:System.Spatial.Util.CheckArgumentNull(System.Object,System.String)">
            <summary>
            Check if input is null, throw an ArgumentNullException if it is.
            </summary>
            <param name="arg">The input argument</param>
            <param name="errorMessage">The error to throw</param>
        </member>
        <member name="M:System.Spatial.Util.IsCatchableExceptionType(System.Exception)">
            <summary>
            Determines if the exception is one of the prohibited types that should not be caught.
            </summary>
            <param name="e">The exception to be checked against the prohibited list.</param>
            <returns>True if the exception is ok to be caught, false otherwise.</returns>
        </member>
        <member name="T:System.Spatial.Util.ValidatedNotNullAttribute">
            <summary>
            A workaround to a problem with FxCop which does not recognize the CheckArgumentNotNull method
            as the one which validates the argument is not null.
            </summary>
            <remarks>This has been suggested as a workaround in msdn forums by the VS team. Note that even though this is production code
            the attribute has no effect on anything else.</remarks>
        </member>
        <member name="T:Microsoft.Data.Spatial.XmlConstants">
            <summary>
            Class that contains all the constants for various schemas.
            </summary>
        </member>
        <member name="F:Microsoft.Data.Spatial.XmlConstants.XmlnsNamespace">
            <summary>
            Namespace for xmlns
            </summary>
        </member>
        <member name="T:System.Spatial.TextRes">
             <summary>
                AutoGenerated resource class. Usage:
            
                    string s = TextRes.GetString(TextRes.MyIdenfitier);
             </summary>
        </member>
        <member name="T:System.Spatial.Strings">
            <summary>
               Strongly-typed and parameterized string resources.
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.PriorityQueueDoesNotContainItem(System.Object)">
            <summary>
            A string like "The queue doesn't contain an item with the priority {0}."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.InvalidPointCoordinate(System.Object,System.Object)">
            <summary>
            A string like "The value '{0}' is not valid for the coordinate '{1}'."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GmlReader_UnexpectedElement(System.Object)">
            <summary>
            A string like "Incorrect GML Format: The XmlReader instance encountered an unexpected element "{0}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GmlReader_InvalidSpatialType(System.Object)">
            <summary>
            A string like "Incorrect GML Format: unknown spatial type tag "{0}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GmlReader_InvalidSrsName(System.Object)">
            <summary>
            A string like "Incorrect GML Format: a srsName attribute must begin with the namespace "{0}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GmlReader_InvalidAttribute(System.Object,System.Object)">
            <summary>
            A string like "The attribute '{0}' on element '{1}' is not supported."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.WellKnownText_UnexpectedToken(System.Object,System.Object,System.Object)">
            <summary>
            A string like "Expecting token type "{0}" with text "{1}" but found "{2}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.WellKnownText_UnexpectedCharacter(System.Object)">
            <summary>
            A string like "Unexpected character '{0}' found in text."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.WellKnownText_UnknownTaggedText(System.Object)">
            <summary>
            A string like "Unknown Tagged Text "{0}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.Validator_InvalidType(System.Object)">
            <summary>
            A string like "Invalid spatial data: Invalid spatial type "{0}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.Validator_NestingOverflow(System.Object)">
            <summary>
            A string like "Invalid spatial data: only {0} levels of nesting are supported in collection types."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.Validator_InvalidPointCoordinate(System.Object,System.Object,System.Object,System.Object)">
            <summary>
            A string like "Invalid spatial data: the coordinates ({0} {1} {2} {3}) are not valid."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.Validator_UnexpectedCall(System.Object,System.Object)">
            <summary>
            A string like "Invalid spatial data: expected call to "{0}" but got call to "{1}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.Validator_UnexpectedCall2(System.Object,System.Object,System.Object)">
            <summary>
            A string like "Invalid spatial data: expected call to "{0}" or "{1}" but got call to "{2}"."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.Validator_InvalidLatitudeCoordinate(System.Object)">
            <summary>
            A string like "Invalid latitude coordinate {0}. A latitude coordinate must be a value between -90.0 and +90.0 degrees."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.Validator_InvalidLongitudeCoordinate(System.Object)">
            <summary>
            A string like "Invalid longitude coordinate {0}. A longitude coordinate must be a value between -15069.0 and +15069.0 degrees"
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GeoJsonReader_MissingRequiredMember(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The '{0}' member is required, but was not found."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GeoJsonReader_InvalidTypeName(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The value '{0}' is not a valid value for the 'type' member."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GeoJsonReader_InvalidCrsType(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The value '{0}' is not a recognized CRS type."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.GeoJsonReader_InvalidCrsName(System.Object)">
            <summary>
            A string like "Invalid GeoJSON. The value '{0}' is not a recognized CRS name."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.JsonReaderExtensions_CannotReadPropertyValueAsString(System.Object,System.Object)">
            <summary>
            A string like "Cannot read the value '{0}' for the property '{1}' as a quoted JSON string value."
            </summary>
        </member>
        <member name="M:System.Spatial.Strings.JsonReaderExtensions_CannotReadValueAsJsonObject(System.Object)">
            <summary>
            A string like "Cannot read the value '{0}' as a JSON object."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.PriorityQueueOperationNotValidOnEmptyQueue">
            <summary>
            A string like "The operation is not valid on an empty queue."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.PriorityQueueEnqueueExistingPriority">
            <summary>
            A string like "An item with the same priority already exists."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.SpatialImplementation_NoRegisteredOperations">
            <summary>
            A string like "No operations are registered. Please provide operations using SpatialImplementation.CurrentImplementation.Operations property."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Point_AccessCoordinateWhenEmpty">
            <summary>
            A string like "Access to the coordinate properties of an empty point is not supported."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.SpatialBuilder_CannotCreateBeforeDrawn">
            <summary>
            A string like "The builder cannot create an instance until all pipeline calls are completed."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GmlReader_ExpectReaderAtElement">
            <summary>
            A string like "Incorrect GML Format: the XmlReader instance is expected to be at the start of a GML element."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GmlReader_EmptyRingsNotAllowed">
            <summary>
            A string like "Incorrect GML Format: a LinearRing element must not be empty."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GmlReader_PosNeedTwoNumbers">
            <summary>
            A string like "Incorrect GML Format: a pos element must contain at least two coordinates."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GmlReader_PosListNeedsEvenCount">
            <summary>
            A string like "Incorrect GML Format: a posList element must contain an even number of coordinates."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.WellKnownText_TooManyDimensions">
            <summary>
            A string like "The WellKnownTextReader is configured to allow only two dimensions, and a third dimension was encountered."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Validator_SridMismatch">
            <summary>
            A string like "Invalid spatial data: An instance of spatial type can have only one unique CoordinateSystem for all of its coordinates."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Validator_FullGlobeInCollection">
            <summary>
            A string like "Invalid spatial data: the spatial type "FullGlobe" cannot be part of a collection type."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Validator_LineStringNeedsTwoPoints">
            <summary>
            A string like "Invalid spatial data: the spatial type "LineString" must contain at least two points."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Validator_FullGlobeCannotHaveElements">
            <summary>
            A string like "Invalid spatial data: the spatial type "FullGlobe" cannot contain figures."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Validator_InvalidPolygonPoints">
            <summary>
            A string like "Invalid spatial data: A polygon ring must contain at least four points, and the last point must be equal to the first point."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Validator_UnexpectedGeography">
            <summary>
            A string like "A geography operation was called while processing a geometric shape."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.Validator_UnexpectedGeometry">
            <summary>
            A string like "A geometry operation was called while processing a geographic shape."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GeoJsonReader_InvalidPosition">
            <summary>
            A string like "Invalid GeoJSON. A position must contain at least two and no more than four elements."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GeoJsonReader_InvalidNullElement">
            <summary>
            A string like "Invalid GeoJSON. A null value was found in an array element where nulls are not allowed."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GeoJsonReader_ExpectedNumeric">
            <summary>
            A string like "Invalid GeoJSON. A non-numeric value was found in an array element where a numeric value was expected."
            </summary>
        </member>
        <member name="P:System.Spatial.Strings.GeoJsonReader_ExpectedArray">
            <summary>
            A string like "Invalid GeoJSON. A primitive value was found in an array element where an array was expected."
            </summary>
        </member>
        <member name="T:System.Spatial.Error">
            <summary>
               Strongly-typed and parameterized exception factory.
            </summary>
        </member>
        <member name="M:System.Spatial.Error.ArgumentNull(System.String)">
            <summary>
            The exception that is thrown when a null reference (Nothing in Visual Basic) is passed to a method that does not accept it as a valid argument.
            </summary>
        </member>
        <member name="M:System.Spatial.Error.ArgumentOutOfRange(System.String)">
            <summary>
            The exception that is thrown when the value of an argument is outside the allowable range of values as defined by the invoked method.
            </summary>
        </member>
        <member name="M:System.Spatial.Error.NotImplemented">
            <summary>
            The exception that is thrown when the author has yet to implement the logic at this point in the program. This can act as an exception based TODO tag.
            </summary>
        </member>
        <member name="M:System.Spatial.Error.NotSupported">
            <summary>
            The exception that is thrown when an invoked method is not supported, or when there is an attempt to read, seek, or write to a stream that does not support the invoked functionality. 
            </summary>
        </member>
    </members>
</doc>
