<%@ Control CodeBehind="ListView.ascx.cs" Language="c#" AutoEventWireup="false" Inherits="SplendidCRM.Administration.ConfigureTabs.ListView" %>
<script runat="server">
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005-2007 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
</script>
<div id="divListView">
	<%@ Register TagPrefix="SplendidCRM" Tagname="ModuleHeader" Src="~/_controls/ModuleHeader.ascx" %>
	<SplendidCRM:ModuleHeader ID="ctlModuleHeader" Module="Administration" Title="Administration.LBL_CONFIGURE_TABS" EnablePrint="true" HelpName="index" EnableHelp="true" Runat="Server" />
	<%@ Register TagPrefix="SplendidCRM" Tagname="ListHeader" Src="~/_controls/ListHeader.ascx" %>

	<br />
	<SplendidCRM:ListHeader ID="ctlListHeader" Title="Administration.LBL_CONFIGURE_TABS" Visible="false" Runat="Server" />
	
	<asp:UpdatePanel runat="server">
		<ContentTemplate>
			<asp:Panel CssClass="button-panel" Visible="<%# !PrintView %>" runat="server">
				<asp:Label ID="lblError" CssClass="error" EnableViewState="false" Runat="server" />
			</asp:Panel>
			
			<SplendidCRM:SplendidGrid id="grdMain" AllowPaging="false" AllowSorting="false" EnableViewState="true" runat="server">
				<Columns>
					<asp:BoundColumn    HeaderText="Dropdown.LBL_KEY"    DataField="MODULE_NAME" ItemStyle-Width="55%" />
					<asp:BoundColumn    HeaderText="Administration.LBL_TAB_ORDER" DataField="TAB_ORDER"   ItemStyle-Width="5%" />
					<asp:TemplateColumn HeaderText="" ItemStyle-Width="10%" ItemStyle-Wrap="false">
						<ItemTemplate>
							<asp:ImageButton CommandName="ConfigureTabs.MoveUp"   Visible='<%# Sql.ToBoolean(Eval("TAB_ENABLED")) || Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Dropdown.LNK_UP"  ) %>' SkinID="uparrow_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.MoveUp"   Visible='<%# Sql.ToBoolean(Eval("TAB_ENABLED")) || Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Dropdown.LNK_UP") %>' Runat="server" />
							&nbsp;
							<asp:ImageButton CommandName="ConfigureTabs.MoveDown" Visible='<%# Sql.ToBoolean(Eval("TAB_ENABLED")) || Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Dropdown.LNK_DOWN") %>' SkinID="downarrow_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.MoveDown" Visible='<%# Sql.ToBoolean(Eval("TAB_ENABLED")) || Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Dropdown.LNK_DOWN") %>' Runat="server" />
						</ItemTemplate>
					</asp:TemplateColumn>
					<asp:TemplateColumn HeaderText="" ItemStyle-Width="5%" ItemStyle-Wrap="false">
						<ItemTemplate>
							<asp:Label Visible='<%#  Sql.ToBoolean(Eval("TAB_ENABLED")) %>' Text='<%# L10n.Term(".LBL_YES") %>' Runat="server" />
							<asp:Label Visible='<%# !Sql.ToBoolean(Eval("TAB_ENABLED")) %>' Text='<%# L10n.Term(".LBL_NO" ) %>' Runat="server" />
						</ItemTemplate>
					</asp:TemplateColumn>
					<asp:TemplateColumn HeaderText="Administration.LBL_VISIBLE" ItemStyle-Width="10%" ItemStyle-Wrap="false">
						<ItemTemplate>
							<asp:ImageButton CommandName="ConfigureTabs.Hide"     Visible='<%#  Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_HIDE") %>' SkinID="minus_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.Hide"     Visible='<%#  Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_HIDE"         ) %>' Runat="server" />
							<asp:ImageButton CommandName="ConfigureTabs.Show"     Visible='<%# !Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_SHOW") %>' SkinID="plus_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.Show"     Visible='<%# !Sql.ToBoolean(Eval("TAB_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_SHOW"         ) %>' Runat="server" />
						</ItemTemplate>
					</asp:TemplateColumn>

					<asp:TemplateColumn HeaderText="" ItemStyle-Width="5%" ItemStyle-Wrap="false">
						<ItemTemplate>
							<asp:Label Visible='<%#  Sql.ToBoolean(Eval("MOBILE_ENABLED")) %>' Text='<%# L10n.Term(".LBL_YES") %>' Runat="server" />
							<asp:Label Visible='<%# !Sql.ToBoolean(Eval("MOBILE_ENABLED")) %>' Text='<%# L10n.Term(".LBL_NO" ) %>' Runat="server" />
						</ItemTemplate>
					</asp:TemplateColumn>
					<asp:TemplateColumn HeaderText="Administration.LBL_MOBILE" ItemStyle-Width="10%" ItemStyle-Wrap="false">
						<ItemTemplate>
							<asp:ImageButton CommandName="ConfigureTabs.HideMobile" Visible='<%#  Sql.ToBoolean(Eval("MOBILE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_HIDE") %>' SkinID="minus_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.HideMobile" Visible='<%#  Sql.ToBoolean(Eval("MOBILE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_HIDE"         ) %>' Runat="server" />
							<asp:ImageButton CommandName="ConfigureTabs.ShowMobile" Visible='<%# !Sql.ToBoolean(Eval("MOBILE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_SHOW") %>' SkinID="plus_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.ShowMobile" Visible='<%# !Sql.ToBoolean(Eval("MOBILE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_SHOW"         ) %>' Runat="server" />
						</ItemTemplate>
					</asp:TemplateColumn>

					<asp:TemplateColumn HeaderText="" ItemStyle-Width="5%" ItemStyle-Wrap="false">
						<ItemTemplate>
							<asp:Label Visible='<%#  Sql.ToBoolean(Eval("MODULE_ENABLED")) %>' Text='<%# L10n.Term(".LBL_YES") %>' Runat="server" />
							<asp:Label Visible='<%# !Sql.ToBoolean(Eval("MODULE_ENABLED")) %>' Text='<%# L10n.Term(".LBL_NO" ) %>' Runat="server" />
						</ItemTemplate>
					</asp:TemplateColumn>
					<asp:TemplateColumn HeaderText="Administration.LNK_ENABLED" ItemStyle-Width="10%" ItemStyle-Wrap="false">
						<ItemTemplate>
							<asp:ImageButton CommandName="ConfigureTabs.Disable"  Visible='<%#  Sql.ToBoolean(Eval("MODULE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_DISABLE") %>' SkinID="minus_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.Disable"  Visible='<%#  Sql.ToBoolean(Eval("MODULE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_DISABLE"         ) %>' Runat="server" />
							<asp:ImageButton CommandName="ConfigureTabs.Enable"   Visible='<%# !Sql.ToBoolean(Eval("MODULE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_ENABLE" ) %>' SkinID="plus_inline" Runat="server" />
							<asp:LinkButton  CommandName="ConfigureTabs.Enable"   Visible='<%# !Sql.ToBoolean(Eval("MODULE_ENABLED")) %>' CommandArgument='<%# Eval("ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_ENABLE"          ) %>' Runat="server" />
						</ItemTemplate>
					</asp:TemplateColumn>
				</Columns>
			</SplendidCRM:SplendidGrid>
		</ContentTemplate>
	</asp:UpdatePanel>

	<%@ Register TagPrefix="SplendidCRM" Tagname="DumpSQL" Src="~/_controls/DumpSQL.ascx" %>
	<SplendidCRM:DumpSQL ID="ctlDumpSQL" Visible="<%# !PrintView %>" Runat="Server" />
</div>
