﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Requests</name>
  </assembly>
  <members>
    <member name="T:System.Net.HttpWebRequest">
      <summary>Provides an HTTP-specific implementation of the <see cref="T:System.Net.WebRequest" /> class.</summary>
    </member>
    <member name="M:System.Net.HttpWebRequest.Abort">
      <summary>Cancels a request to an Internet resource.</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Accept">
      <summary>Gets or sets the value of the Accept HTTP header.</summary>
      <returns>The value of the Accept HTTP header. The default value is null.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.AllowReadStreamBuffering">
      <summary>Gets or sets a value that indicates whether to buffer the received from the Internet resource.</summary>
      <returns>true to buffer the received from the Internet resource; otherwise, false.true to enable buffering of the data received from the Internet resource; false to disable buffering. The default is true.</returns>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request for a <see cref="T:System.IO.Stream" /> object to use to write data.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous request.</returns>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate. </param>
      <param name="state">The state object for this request. </param>
      <exception cref="T:System.Net.ProtocolViolationException">The <see cref="P:System.Net.HttpWebRequest.Method" /> property is GET or HEAD.-or- <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> is true, <see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> is false, <see cref="P:System.Net.HttpWebRequest.ContentLength" /> is -1, <see cref="P:System.Net.HttpWebRequest.SendChunked" /> is false, and <see cref="P:System.Net.HttpWebRequest.Method" /> is POST or PUT. </exception>
      <exception cref="T:System.InvalidOperationException">The stream is being used by a previous call to <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />-or- <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> is set to a value and <see cref="P:System.Net.HttpWebRequest.SendChunked" /> is false.-or- The thread pool is running out of threads. </exception>
      <exception cref="T:System.NotSupportedException">The request cache validator indicated that the response for this request can be served from the cache; however, requests that write data must not use the cache. This exception can occur if you are using a custom cache validator that is incorrectly implemented. </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> was previously called. </exception>
      <exception cref="T:System.ObjectDisposedException">In a .NET Compact Framework application, a request stream with zero content length was not obtained and closed correctly. For more information about handling zero content length requests, see Network Programming in the .NET Compact Framework.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>Begins an asynchronous request to an Internet resource.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous request for a response.</returns>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate </param>
      <param name="state">The state object for this request. </param>
      <exception cref="T:System.InvalidOperationException">The stream is already in use by a previous call to <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />-or- <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> is set to a value and <see cref="P:System.Net.HttpWebRequest.SendChunked" /> is false.-or- The thread pool is running out of threads. </exception>
      <exception cref="T:System.Net.ProtocolViolationException">
        <see cref="P:System.Net.HttpWebRequest.Method" /> is GET or HEAD, and either <see cref="P:System.Net.HttpWebRequest.ContentLength" /> is greater than zero or <see cref="P:System.Net.HttpWebRequest.SendChunked" /> is true.-or- <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> is true, <see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> is false, and either <see cref="P:System.Net.HttpWebRequest.ContentLength" /> is -1, <see cref="P:System.Net.HttpWebRequest.SendChunked" /> is false and <see cref="P:System.Net.HttpWebRequest.Method" /> is POST or PUT.-or- The <see cref="T:System.Net.HttpWebRequest" /> has an entity body but the <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> method is called without calling the <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> method. -or- The <see cref="P:System.Net.HttpWebRequest.ContentLength" /> is greater than zero, but the application does not write all of the promised data.</exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> was previously called. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContentType">
      <summary>Gets or sets the value of the Content-type HTTP header.</summary>
      <returns>The value of the Content-type HTTP header. The default value is null.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContinueTimeout">
      <summary>Gets or sets a timeout, in milliseconds, to wait until the 100-Continue is received from the server. </summary>
      <returns>The timeout, in milliseconds, to wait until the 100-Continue is received. </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.CookieContainer">
      <summary>Gets or sets the cookies associated with the request.</summary>
      <returns>A <see cref="T:System.Net.CookieContainer" /> that contains the cookies associated with this request.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Credentials">
      <summary>Gets or sets authentication information for the request.</summary>
      <returns>An <see cref="T:System.Net.ICredentials" /> that contains the authentication credentials associated with the request. The default is null.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>Ends an asynchronous request for a <see cref="T:System.IO.Stream" /> object to use to write data.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> to use to write request data.</returns>
      <param name="asyncResult">The pending request for a stream. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null. </exception>
      <exception cref="T:System.IO.IOException">The request did not complete, and no stream is available. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by the current instance from a call to <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />. </exception>
      <exception cref="T:System.InvalidOperationException">This method was called previously using <paramref name="asyncResult" />. </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> was previously called.-or- An error occurred while processing the request. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>Ends an asynchronous request to an Internet resource.</summary>
      <returns>A <see cref="T:System.Net.WebResponse" /> that contains the response from the Internet resource.</returns>
      <param name="asyncResult">The pending request for a response. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> is null. </exception>
      <exception cref="T:System.InvalidOperationException">This method was called previously using <paramref name="asyncResult." />-or- The <see cref="P:System.Net.HttpWebRequest.ContentLength" /> property is greater than 0 but the data has not been written to the request stream. </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> was previously called.-or- An error occurred while processing the request. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by the current instance from a call to <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.HaveResponse">
      <summary>Gets a value that indicates whether a response has been received from an Internet resource.</summary>
      <returns>true if a response has been received; otherwise, false.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Headers">
      <summary>Specifies a collection of the name/value pairs that make up the HTTP headers.</summary>
      <returns>A <see cref="T:System.Net.WebHeaderCollection" /> that contains the name/value pairs that make up the headers for the HTTP request.</returns>
      <exception cref="T:System.InvalidOperationException">The request has been started by calling the <see cref="M:System.Net.HttpWebRequest.GetRequestStream" />, <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />, <see cref="M:System.Net.HttpWebRequest.GetResponse" />, or <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> method. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Method">
      <summary>Gets or sets the method for the request.</summary>
      <returns>The request method to use to contact the Internet resource. The default value is GET.</returns>
      <exception cref="T:System.ArgumentException">No method is supplied.-or- The method string contains invalid characters. </exception>
    </member>
    <member name="P:System.Net.HttpWebRequest.RequestUri">
      <summary>Gets the original Uniform Resource Identifier (URI) of the request.</summary>
      <returns>A <see cref="T:System.Uri" /> that contains the URI of the Internet resource passed to the <see cref="M:System.Net.WebRequest.Create(System.String)" /> method.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.SupportsCookieContainer">
      <summary>Gets a value that indicates whether the request provides support for a <see cref="T:System.Net.CookieContainer" />.</summary>
      <returns>true if the request provides support for a <see cref="T:System.Net.CookieContainer" />; otherwise, false.true if a <see cref="T:System.Net.CookieContainer" /> is supported; otherwise, false. </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.UseDefaultCredentials">
      <summary>Gets or sets a <see cref="T:System.Boolean" /> value that controls whether default credentials are sent with requests.</summary>
      <returns>true if the default credentials are used; otherwise false. The default value is false.</returns>
      <exception cref="T:System.InvalidOperationException">You attempted to set this property after the request was sent.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="USERNAME" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.HttpWebResponse">
      <summary>Provides an HTTP-specific implementation of the <see cref="T:System.Net.WebResponse" /> class.</summary>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentLength">
      <summary>Gets the length of the content returned by the request.</summary>
      <returns>The number of bytes returned by the request. Content length does not include header information.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentType">
      <summary>Gets the content type of the response.</summary>
      <returns>A string that contains the content type of the response.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Cookies">
      <summary>Gets or sets the cookies that are associated with this response.</summary>
      <returns>A <see cref="T:System.Net.CookieCollection" /> that contains the cookies that are associated with this response.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="M:System.Net.HttpWebResponse.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.HttpWebResponse" />, and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources. </param>
    </member>
    <member name="M:System.Net.HttpWebResponse.GetResponseStream">
      <summary>Gets the stream that is used to read the body of the response from the server.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> containing the body of the response.</returns>
      <exception cref="T:System.Net.ProtocolViolationException">There is no response stream. </exception>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebResponse.Headers">
      <summary>Gets the headers that are associated with this response from the server.</summary>
      <returns>A <see cref="T:System.Net.WebHeaderCollection" /> that contains the header information returned with the response.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Method">
      <summary>Gets the method that is used to return the response.</summary>
      <returns>A string that contains the HTTP method that is used to return the response.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ResponseUri">
      <summary>Gets the URI of the Internet resource that responded to the request.</summary>
      <returns>A <see cref="T:System.Uri" /> that contains the URI of the Internet resource that responded to the request.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusCode">
      <summary>Gets the status of the response.</summary>
      <returns>One of the <see cref="T:System.Net.HttpStatusCode" /> values.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusDescription">
      <summary>Gets the status description returned with the response.</summary>
      <returns>A string that describes the status of the response.</returns>
      <exception cref="T:System.ObjectDisposedException">The current instance has been disposed. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.SupportsHeaders">
      <summary>Gets a value that indicates if headers are supported.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if headers are supported; otherwise, false.</returns>
    </member>
    <member name="T:System.Net.IWebRequestCreate">
      <summary>Provides the base interface for creating <see cref="T:System.Net.WebRequest" /> instances.</summary>
    </member>
    <member name="M:System.Net.IWebRequestCreate.Create(System.Uri)">
      <summary>Creates a <see cref="T:System.Net.WebRequest" /> instance.</summary>
      <returns>A <see cref="T:System.Net.WebRequest" /> instance.</returns>
      <param name="uri">The uniform resource identifier (URI) of the Web resource. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="uri" /> is not supported by this <see cref="T:System.Net.IWebRequestCreate" /> instance. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> is null. </exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI specified in <paramref name="uri" /> is not a valid URI. </exception>
    </member>
    <member name="T:System.Net.ProtocolViolationException">
      <summary>The exception that is thrown when an error is made while using a network protocol.</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.ProtocolViolationException" /> class.</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.ProtocolViolationException" /> class with the specified message.</summary>
      <param name="message">The error message string. </param>
    </member>
    <member name="T:System.Net.WebException">
      <summary>The exception that is thrown when an error occurs while accessing the network through a pluggable protocol.</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.WebException" /> class.</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.WebException" /> class with the specified error message.</summary>
      <param name="message">The text of the error message. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.WebException" /> class with the specified error message and nested exception.</summary>
      <param name="message">The text of the error message. </param>
      <param name="innerException">A nested exception. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception,System.Net.WebExceptionStatus,System.Net.WebResponse)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.WebException" /> class with the specified error message, nested exception, status, and response.</summary>
      <param name="message">The text of the error message. </param>
      <param name="innerException">A nested exception. </param>
      <param name="status">One of the <see cref="T:System.Net.WebExceptionStatus" /> values. </param>
      <param name="response">A <see cref="T:System.Net.WebResponse" /> instance that contains the response from the remote host. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Net.WebExceptionStatus)">
      <summary>Initializes a new instance of the <see cref="T:System.Net.WebException" /> class with the specified error message and status.</summary>
      <param name="message">The text of the error message. </param>
      <param name="status">One of the <see cref="T:System.Net.WebExceptionStatus" /> values. </param>
    </member>
    <member name="P:System.Net.WebException.Response">
      <summary>Gets the response that the remote host returned.</summary>
      <returns>If a response is available from the Internet resource, a <see cref="T:System.Net.WebResponse" /> instance that contains the error response from an Internet resource; otherwise, null.</returns>
    </member>
    <member name="P:System.Net.WebException.Status">
      <summary>Gets the status of the response.</summary>
      <returns>One of the <see cref="T:System.Net.WebExceptionStatus" /> values.</returns>
    </member>
    <member name="T:System.Net.WebExceptionStatus">
      <summary>Defines status codes for the <see cref="T:System.Net.WebException" /> class.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.ConnectFailure">
      <summary>The remote service point could not be contacted at the transport level.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.MessageLengthLimitExceeded">
      <summary>A message was received that exceeded the specified limit when sending a request or receiving a response from the server.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Pending">
      <summary>An internal asynchronous request is pending.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.RequestCanceled">
      <summary>The request was canceled, the <see cref="M:System.Net.WebRequest.Abort" /> method was called, or an unclassifiable error occurred. This is the default value for <see cref="P:System.Net.WebException.Status" />.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.SendFailure">
      <summary>A complete request could not be sent to the remote server.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Success">
      <summary>No error was encountered.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.UnknownError">
      <summary>An exception of unknown type has occurred.</summary>
    </member>
    <member name="T:System.Net.WebRequest">
      <summary>Makes a request to a Uniform Resource Identifier (URI). This is an abstract class.</summary>
    </member>
    <member name="M:System.Net.WebRequest.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.WebRequest" /> class.</summary>
    </member>
    <member name="M:System.Net.WebRequest.Abort">
      <summary>Aborts the Request </summary>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>When overridden in a descendant class, provides an asynchronous version of the <see cref="M:System.Net.WebRequest.GetRequestStream" /> method.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous request.</returns>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate. </param>
      <param name="state">An object containing state information for this asynchronous request. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>When overridden in a descendant class, begins an asynchronous request for an Internet resource.</summary>
      <returns>An <see cref="T:System.IAsyncResult" /> that references the asynchronous request.</returns>
      <param name="callback">The <see cref="T:System.AsyncCallback" /> delegate. </param>
      <param name="state">An object containing state information for this asynchronous request. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="P:System.Net.WebRequest.ContentType">
      <summary>When overridden in a descendant class, gets or sets the content type of the request data being sent.</summary>
      <returns>The content type of the request data.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.String)">
      <summary>Initializes a new <see cref="T:System.Net.WebRequest" /> instance for the specified URI scheme.</summary>
      <returns>A <see cref="T:System.Net.WebRequest" /> descendant for the specific URI scheme.</returns>
      <param name="requestUriString">The URI that identifies the Internet resource. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> has not been registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.Uri)">
      <summary>Initializes a new <see cref="T:System.Net.WebRequest" /> instance for the specified URI scheme.</summary>
      <returns>A <see cref="T:System.Net.WebRequest" /> descendant for the specified URI scheme.</returns>
      <param name="requestUri">A <see cref="T:System.Uri" /> containing the URI of the requested resource. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is not registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.String)">
      <summary>Initializes a new <see cref="T:System.Net.HttpWebRequest" /> instance for the specified URI string.</summary>
      <returns>Returns <see cref="T:System.Net.HttpWebRequest" />.An <see cref="T:System.Net.HttpWebRequest" />  instance for the specific URI string.</returns>
      <param name="requestUriString">A URI string that identifies the Internet resource. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.Uri)">
      <summary>Initializes a new <see cref="T:System.Net.HttpWebRequest" /> instance for the specified URI.</summary>
      <returns>Returns <see cref="T:System.Net.HttpWebRequest" />.An <see cref="T:System.Net.HttpWebRequest" /> instance for the specific URI string.</returns>
      <param name="requestUri">A URI that identifies the Internet resource.</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUri" /> is not a valid URI. </exception>
    </member>
    <member name="P:System.Net.WebRequest.Credentials">
      <summary>When overridden in a descendant class, gets or sets the network credentials used for authenticating the request with the Internet resource.</summary>
      <returns>An <see cref="T:System.Net.ICredentials" /> containing the authentication credentials associated with the request. The default is null.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.DefaultWebProxy">
      <summary>Gets or sets the global HTTP proxy.</summary>
      <returns>An <see cref="T:System.Net.IWebProxy" /> used by every call to instances of <see cref="T:System.Net.WebRequest" />.</returns>
    </member>
    <member name="M:System.Net.WebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>When overridden in a descendant class, returns a <see cref="T:System.IO.Stream" /> for writing data to the Internet resource.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> to write data to.</returns>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that references a pending request for a stream. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>When overridden in a descendant class, returns a <see cref="T:System.Net.WebResponse" />.</summary>
      <returns>A <see cref="T:System.Net.WebResponse" /> that contains a response to the Internet request.</returns>
      <param name="asyncResult">An <see cref="T:System.IAsyncResult" /> that references a pending request for a response. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.GetRequestStreamAsync">
      <summary>When overridden in a descendant class, returns a <see cref="T:System.IO.Stream" /> for writing data to the Internet resource as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
    </member>
    <member name="M:System.Net.WebRequest.GetResponseAsync">
      <summary>When overridden in a descendant class, returns a response to an Internet request as an asynchronous operation.</summary>
      <returns>Returns <see cref="T:System.Threading.Tasks.Task`1" />.The task object representing the asynchronous operation.</returns>
    </member>
    <member name="P:System.Net.WebRequest.Headers">
      <summary>When overridden in a descendant class, gets or sets the collection of header name/value pairs associated with the request.</summary>
      <returns>A <see cref="T:System.Net.WebHeaderCollection" /> containing the header name/value pairs associated with this request.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Method">
      <summary>When overridden in a descendant class, gets or sets the protocol method to use in this request.</summary>
      <returns>The protocol method to use in this request.</returns>
      <exception cref="T:System.NotImplementedException">If the property is not overridden in a descendant class, any attempt is made to get or set the property. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Proxy">
      <summary>When overridden in a descendant class, gets or sets the network proxy to use to access this Internet resource.</summary>
      <returns>The <see cref="T:System.Net.IWebProxy" /> to use to access the Internet resource.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.RegisterPrefix(System.String,System.Net.IWebRequestCreate)">
      <summary>Registers a <see cref="T:System.Net.WebRequest" /> descendant for the specified URI.</summary>
      <returns>true if registration is successful; otherwise, false.</returns>
      <param name="prefix">The complete URI or URI prefix that the <see cref="T:System.Net.WebRequest" /> descendant services. </param>
      <param name="creator">The create method that the <see cref="T:System.Net.WebRequest" /> calls to create the <see cref="T:System.Net.WebRequest" /> descendant. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prefix" /> is null-or- <paramref name="creator" /> is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.RequestUri">
      <summary>When overridden in a descendant class, gets the URI of the Internet resource associated with the request.</summary>
      <returns>A <see cref="T:System.Uri" /> representing the resource associated with the request </returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.UseDefaultCredentials">
      <summary>When overridden in a descendant class, gets or sets a <see cref="T:System.Boolean" /> value that controls whether <see cref="P:System.Net.CredentialCache.DefaultCredentials" /> are sent with requests.</summary>
      <returns>true if the default credentials are used; otherwise false. The default value is false.</returns>
      <exception cref="T:System.InvalidOperationException">You attempted to set this property after the request was sent.</exception>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.WebResponse">
      <summary>Provides a response from a Uniform Resource Identifier (URI). This is an abstract class.</summary>
    </member>
    <member name="M:System.Net.WebResponse.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Net.WebResponse" /> class.</summary>
    </member>
    <member name="P:System.Net.WebResponse.ContentLength">
      <summary>When overridden in a descendant class, gets or sets the content length of data being received.</summary>
      <returns>The number of bytes returned from the Internet resource.</returns>
      <exception cref="T:System.NotSupportedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ContentType">
      <summary>When overridden in a derived class, gets or sets the content type of the data being received.</summary>
      <returns>A string that contains the content type of the response.</returns>
      <exception cref="T:System.NotSupportedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebResponse.Dispose">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.WebResponse" /> object.</summary>
    </member>
    <member name="M:System.Net.WebResponse.Dispose(System.Boolean)">
      <summary>Releases the unmanaged resources used by the <see cref="T:System.Net.WebResponse" /> object, and optionally disposes of the managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to releases only unmanaged resources. </param>
    </member>
    <member name="M:System.Net.WebResponse.GetResponseStream">
      <summary>When overridden in a descendant class, returns the data stream from the Internet resource.</summary>
      <returns>An instance of the <see cref="T:System.IO.Stream" /> class for reading data from the Internet resource.</returns>
      <exception cref="T:System.NotSupportedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.Headers">
      <summary>When overridden in a derived class, gets a collection of header name-value pairs associated with this request.</summary>
      <returns>An instance of the <see cref="T:System.Net.WebHeaderCollection" /> class that contains header values associated with this response.</returns>
      <exception cref="T:System.NotSupportedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ResponseUri">
      <summary>When overridden in a derived class, gets the URI of the Internet resource that actually responded to the request.</summary>
      <returns>An instance of the <see cref="T:System.Uri" /> class that contains the URI of the Internet resource that actually responded to the request.</returns>
      <exception cref="T:System.NotSupportedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.SupportsHeaders">
      <summary>Gets a value that indicates if headers are supported.</summary>
      <returns>Returns <see cref="T:System.Boolean" />.true if headers are supported; otherwise, false.</returns>
    </member>
  </members>
</doc>