﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Queryable</name>
  </assembly>
  <members>
    <member name="T:System.Linq.EnumerableExecutor">
      <summary>式ツリーを表し、式ツリーを書き換えた後で式ツリーを実行する機能を提供します。</summary>
    </member>
    <member name="M:System.Linq.EnumerableExecutor.#ctor">
      <summary>
        <see cref="T:System.Linq.EnumerableExecutor" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Linq.EnumerableExecutor`1">
      <summary>式ツリーを表し、式ツリーを書き換えた後で式ツリーを実行する機能を提供します。</summary>
      <typeparam name="T">式ツリーの実行結果の値のデータ型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableExecutor`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>
        <see cref="T:System.Linq.EnumerableExecutor`1" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="expression">新しいインスタンスに関連付ける式ツリー。</param>
    </member>
    <member name="T:System.Linq.EnumerableQuery">
      <summary>
        <see cref="T:System.Collections.IEnumerable" /> を <see cref="T:System.Linq.EnumerableQuery" /> データ ソースとして表します。</summary>
    </member>
    <member name="M:System.Linq.EnumerableQuery.#ctor">
      <summary>
        <see cref="T:System.Linq.EnumerableQuery" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="T:System.Linq.EnumerableQuery`1">
      <summary>
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> コレクションを <see cref="T:System.Linq.IQueryable`1" /> データ ソースとして表します。</summary>
      <typeparam name="T">コレクション内のデータの型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Linq.EnumerableQuery`1" /> クラスの新しいインスタンスを初期化し、それを <see cref="T:System.Collections.Generic.IEnumerable`1" /> コレクションに関連付けます。</summary>
      <param name="enumerable">新しいインスタンスに関連付けるコレクション。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>
        <see cref="T:System.Linq.EnumerableQuery`1" /> クラスの新しいインスタンスを初期化し、インスタンスを式ツリーに関連付けます。</summary>
      <param name="expression">新しいインスタンスに関連付ける式ツリー。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>関連付けられている <see cref="T:System.Collections.Generic.IEnumerable`1" /> コレクション、またはこれが null の場合は、関連付けられている式ツリーを <see cref="T:System.Collections.Generic.IEnumerable`1" /> データ ソースに対するクエリとして書き換え、それを実行することによって得られるコレクションを反復処理できる列挙子を返します。</summary>
      <returns>関連付けられたデータ ソースの反復処理に使用できる列挙子。</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>関連付けられている <see cref="T:System.Collections.Generic.IEnumerable`1" /> コレクション、またはこれが null の場合は、関連付けられている式ツリーを <see cref="T:System.Collections.Generic.IEnumerable`1" /> データ ソースに対するクエリとして書き換え、それを実行することによって得られるコレクションを反復処理できる列挙子を返します。</summary>
      <returns>関連付けられたデータ ソースの反復処理に使用できる列挙子。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#ElementType">
      <summary>このインスタンスが表すコレクション内のデータの型を取得します。</summary>
      <returns>このインスタンスが表すコレクション内のデータの型。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Expression">
      <summary>このインスタンスに関連付けられた、またはこのインスタンスを表す式ツリーを取得します。</summary>
      <returns>このインスタンスに関連付けられた、またはこのインスタンスを表す式ツリー。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Provider">
      <summary>このインスタンスに関連付けられたクエリ プロバイダーを取得します。</summary>
      <returns>このインスタンスに関連付けられたクエリ プロバイダー。</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>新しい <see cref="T:System.Linq.EnumerableQuery`1" /> オブジェクトを構築し、それを、データの <see cref="T:System.Linq.IQueryable`1" /> コレクションを表す指定した式ツリーに関連付けます。</summary>
      <returns>
        <paramref name="expression" /> に関連付けられた EnumerableQuery オブジェクト。</returns>
      <param name="expression">実行する式ツリー。</param>
      <typeparam name="S">
        <paramref name="expression" /> が表すコレクション内のデータの型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery(System.Linq.Expressions.Expression)">
      <summary>新しい <see cref="T:System.Linq.EnumerableQuery`1" /> オブジェクトを構築し、それを、データの <see cref="T:System.Linq.IQueryable" /> コレクションを表す指定した式ツリーに関連付けます。</summary>
      <returns>
        <paramref name="expression" /> に関連付ける <see cref="T:System.Linq.EnumerableQuery`1" /> オブジェクト。</returns>
      <param name="expression">データの <see cref="T:System.Linq.IQueryable" /> コレクションを表す式ツリー。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute``1(System.Linq.Expressions.Expression)">
      <summary>
        <see cref="T:System.Linq.Queryable" /> メソッドでクエリできない列挙可能なデータ ソースで、<see cref="T:System.Linq.Queryable" /> メソッドの代わりに <see cref="T:System.Linq.Enumerable" /> メソッドを呼び出すように式を書き換えた後で、式を実行します。</summary>
      <returns>
        <paramref name="expression" /> の実行結果の値。</returns>
      <param name="expression">実行する式ツリー。</param>
      <typeparam name="S">
        <paramref name="expression" /> が表すコレクション内のデータの型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute(System.Linq.Expressions.Expression)">
      <summary>
        <see cref="T:System.Linq.Queryable" /> メソッドでクエリできない列挙可能なデータ ソースで、<see cref="T:System.Linq.Queryable" /> メソッドの代わりに <see cref="T:System.Linq.Enumerable" /> メソッドを呼び出すように式を書き換えた後で、式を実行します。</summary>
      <returns>
        <paramref name="expression" /> の実行結果の値。</returns>
      <param name="expression">実行する式ツリー。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.ToString">
      <summary>列挙可能なコレクションのテキスト表現を返します。これが null の場合は、このインスタンスに関連付けられている式ツリーのテキスト表現を返します。</summary>
      <returns>列挙可能なコレクションのテキスト表現。これが null の場合は、このインスタンスに関連付けられている式ツリーのテキスト表現。</returns>
    </member>
    <member name="T:System.Linq.Queryable">
      <summary>
        <see cref="T:System.Linq.IQueryable`1" /> を実装するデータ構造を照会するための一連の static (Visual Basic の場合は Shared) メソッドを提供します。</summary>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0,``0}})">
      <summary>シーケンスにアキュムレータ関数を適用します。</summary>
      <returns>最終的なアキュムレータ値。</returns>
      <param name="source">集計対象のシーケンス。</param>
      <param name="func">各要素に適用するアキュムレータ関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="func" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``2(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}})">
      <summary>シーケンスにアキュムレータ関数を適用します。指定されたシード値が最初のアキュムレータ値として使用されます。</summary>
      <returns>最終的なアキュムレータ値。</returns>
      <param name="source">集計対象のシーケンス。</param>
      <param name="seed">最初のアキュムレータ値。</param>
      <param name="func">各要素に対して呼び出すアキュムレータ関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TAccumulate">アキュムレータ値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="func" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``3(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,``2}})">
      <summary>シーケンスにアキュムレータ関数を適用します。指定したシード値は最初のアキュムレータ値として使用され、指定した関数は結果値の選択に使用されます。</summary>
      <returns>変換された最終的なアキュムレータ値。</returns>
      <param name="source">集計対象のシーケンス。</param>
      <param name="seed">最初のアキュムレータ値。</param>
      <param name="func">各要素に対して呼び出すアキュムレータ関数。</param>
      <param name="selector">最終的なアキュムレータ値を結果値に変換する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TAccumulate">アキュムレータ値の型。</typeparam>
      <typeparam name="TResult">結果の値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="func" />、または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.All``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>シーケンスのすべての要素が条件を満たしているかどうかを判断します。</summary>
      <returns>指定された述語でソース シーケンスのすべての要素がテストに合格する場合は true。それ以外の場合は false。</returns>
      <param name="source">条件を満たしているかどうかをテストする要素を含むシーケンス。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスに要素が含まれているかどうかを判断します。</summary>
      <returns>ソース シーケンスに要素が含まれている場合は true。それ以外の場合は false。</returns>
      <param name="source">空かどうかを確認するシーケンス。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>シーケンスの任意の要素が条件を満たしているかどうかを判断します。</summary>
      <returns>指定された述語でソース シーケンスの要素がテストに合格する場合は true。それ以外の場合は false。</returns>
      <param name="source">条件を満たしているかどうかをテストする要素を含むシーケンス。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>ジェネリックの <see cref="T:System.Collections.Generic.IEnumerable`1" /> をジェネリックの <see cref="T:System.Linq.IQueryable`1" /> に変換します。</summary>
      <returns>入力シーケンスを表す <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">変換するシーケンス。</param>
      <typeparam name="TElement">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable(System.Collections.IEnumerable)">
      <summary>
        <see cref="T:System.Collections.IEnumerable" /> を <see cref="T:System.Linq.IQueryable" /> に変換します。</summary>
      <returns>入力シーケンスを表す <see cref="T:System.Linq.IQueryable" />。</returns>
      <param name="source">変換するシーケンス。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> が <paramref name="T" /> に対して <see cref="T:System.Collections.Generic.IEnumerable`1" /> を実装していません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Decimal})">
      <summary>
        <see cref="T:System.Decimal" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる <see cref="T:System.Decimal" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Double})">
      <summary>
        <see cref="T:System.Double" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる <see cref="T:System.Double" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int32})">
      <summary>
        <see cref="T:System.Int32" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる <see cref="T:System.Int32" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int64})">
      <summary>
        <see cref="T:System.Int64" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる <see cref="T:System.Int64" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>null 許容の <see cref="T:System.Decimal" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。ソース シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる null 許容の <see cref="T:System.Decimal" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>null 許容の <see cref="T:System.Double" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。ソース シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる null 許容の <see cref="T:System.Double" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>null 許容の <see cref="T:System.Int32" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。ソース シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる null 許容の <see cref="T:System.Int32" />  値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>null 許容の <see cref="T:System.Int64" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。ソース シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる null 許容の <see cref="T:System.Int64" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>null 許容の <see cref="T:System.Single" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。ソース シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる null 許容の <see cref="T:System.Single" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Single})">
      <summary>
        <see cref="T:System.Single" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる <see cref="T:System.Single" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Decimal" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値の計算に使用される値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Double" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Int32" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Int64" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Decimal" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。<paramref name="source" /> シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Double" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。<paramref name="source" /> シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Int32" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。<paramref name="source" /> シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Int64" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。<paramref name="source" /> シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Single" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。<paramref name="source" /> シーケンスが空か null 値のみを含む場合は null。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Single" /> 値のシーケンスの平均値を計算します。</summary>
      <returns>値のシーケンスの平均値。</returns>
      <param name="source">平均値計算の対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> に要素が含まれていません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Cast``1(System.Linq.IQueryable)">
      <summary>
        <see cref="T:System.Linq.IQueryable" /> の要素を、指定した型に変換します。</summary>
      <returns>指定した型に変換されたソース シーケンスの各要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">変換する要素が格納されている <see cref="T:System.Linq.IQueryable" />。</param>
      <typeparam name="TResult">
        <paramref name="source" /> の要素の変換後の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidCastException">シーケンスの要素を <paramref name="TResult" /> 型にキャストできません。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Concat``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>2 つのシーケンスを連結します。</summary>
      <returns>2 つの入力シーケンスの連結された要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source1">連結する最初のシーケンス。</param>
      <param name="source2">最初のシーケンスに連結するシーケンス。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0)">
      <summary>既定の等値比較子を使用して、指定した要素がシーケンスに含まれているかどうかを判断します。</summary>
      <returns>指定した値を持つ要素が入力シーケンスに含まれている場合は true。それ以外の場合は false。</returns>
      <param name="source">
        <paramref name="item" /> の検索対象となる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="item">シーケンス内で検索するオブジェクト。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>指定した <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用して、指定した要素がシーケンスに含まれているかどうかを判断します。</summary>
      <returns>指定した値を持つ要素が入力シーケンスに含まれている場合は true。それ以外の場合は false。</returns>
      <param name="source">
        <paramref name="item" /> の検索対象となる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="item">シーケンス内で検索するオブジェクト。</param>
      <param name="comparer">値を比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0})">
      <summary>シーケンス内の要素数を返します。</summary>
      <returns>入力シーケンス内の要素数。</returns>
      <param name="source">カウントする要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 内の要素数が <see cref="F:System.Int32.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定したシーケンス内の、条件を満たす要素の数を返します。</summary>
      <returns>述語関数の条件を満たす、シーケンス内の要素数。</returns>
      <param name="source">カウントする要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 内の要素数が <see cref="F:System.Int32.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0})">
      <summary>指定したシーケンスの要素を返します。シーケンスが空の場合はシングルトン コレクションにある型パラメーターの既定値を返します。</summary>
      <returns>
        <paramref name="source" /> が空の場合は default(<paramref name="TSource" />) が格納されている <see cref="T:System.Linq.IQueryable`1" />。それ以外の場合は <paramref name="source" />。</returns>
      <param name="source">空の場合に、既定値を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0},``0)">
      <summary>指定されたシーケンスの要素を返します。シーケンスが空の場合はシングルトン コレクションにある型パラメーターの既定値を返します。</summary>
      <returns>
        <paramref name="source" /> が空の場合は <paramref name="defaultValue" /> が格納されている <see cref="T:System.Linq.IQueryable`1" />。それ以外の場合は <paramref name="source" />。</returns>
      <param name="source">空の場合に、指定された値を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="defaultValue">シーケンスが空の場合に返す値。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0})">
      <summary>既定の等値比較子を使用して値を比較することにより、シーケンスから一意の要素を返します。</summary>
      <returns>
        <paramref name="source" /> の一意の要素が格納される <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">重複を削除する対象の <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>指定された <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用して値を比較することにより、シーケンスから一意の要素を返します。</summary>
      <returns>
        <paramref name="source" /> の一意の要素が格納される <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">重複を削除する対象の <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="comparer">値を比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAt``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>シーケンス内の指定されたインデックス位置にある要素を返します。</summary>
      <returns>
        <paramref name="source" /> 内の指定した位置にある要素。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="index">取得する要素の、0 から始まるインデックス。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> が 0 未満です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAtOrDefault``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>シーケンス内の指定されたインデックス位置にある要素を返します。インデックスが範囲外の場合は既定値を返します。</summary>
      <returns>
        <paramref name="index" /> が <paramref name="source" /> の範囲外の場合は default(<paramref name="TSource" />)。それ以外の場合は <paramref name="source" /> で指定された位置にある要素。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="index">取得する要素の、0 から始まるインデックス。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>既定の等値比較子を使用して値を比較することにより、2 つのシーケンスの差集合を生成します。</summary>
      <returns>2 つのシーケンスの差集合が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source1">
        <paramref name="source2" /> には含まれていないが、返される要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="source2">最初のシーケンスにも含まれているが、返されたシーケンスには出現しない要素を含む <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>指定された <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用して値を比較することにより、2 つのシーケンスの差集合を生成します。</summary>
      <returns>2 つのシーケンスの差集合が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source1">
        <paramref name="source2" /> には含まれていないが、返される要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="source2">最初のシーケンスにも含まれているが、返されたシーケンスには出現しない要素を含む <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="comparer">値を比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスの最初の要素を返します。</summary>
      <returns>
        <paramref name="source" /> の最初の要素。</returns>
      <param name="source">最初の要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">ソース シーケンスが空です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定された条件を満たす、シーケンスの最初の要素を返します。</summary>
      <returns>
        <paramref name="predicate" /> でテストに合格する、<paramref name="source" /> の最初の要素。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="predicate" /> の条件を満たす要素がありません。またはソース シーケンスが空です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスの最初の要素を返します。シーケンスに要素が含まれていない場合は既定値を返します。</summary>
      <returns>
        <paramref name="source" /> が空の場合は default(<paramref name="TSource" />)。それ以外の場合は <paramref name="source" /> の最初の要素。</returns>
      <param name="source">最初の要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定された条件を満たす、シーケンスの最初の要素を返します。このような要素が見つからない場合は既定値を返します。</summary>
      <returns>
        <paramref name="source" /> が空の場合または <paramref name="predicate" /> で指定されたテストに合格する要素がない場合は default(<paramref name="TSource" />)。それ以外の場合は、<paramref name="predicate" /> で指定されたテストに合格する、<paramref name="source" /> の最初の要素。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>指定されたキー セレクター関数に従ってシーケンスの要素をグループ化します。</summary>
      <returns>C# では IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt;、Visual Basic では IQueryable(Of IGrouping(Of TKey, TSource))。ここでは、各 <see cref="T:System.Linq.IGrouping`2" /> オブジェクトに、オブジェクトのシーケンス、およびキーが格納されています。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="keySelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>指定されたキー セレクター関数に従ってシーケンスの要素をグループ化し、指定された比較子を使用してキーを比較します。</summary>
      <returns>C# では IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt;、Visual Basic では IQueryable(Of IGrouping(Of TKey, TSource))。ここでは、各 <see cref="T:System.Linq.IGrouping`2" /> オブジェクトに、オブジェクトのシーケンス、およびキーが格納されています。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}})">
      <summary>指定されたキー セレクター関数に従ってシーケンスの要素をグループ化し、指定された関数を使用して各グループの要素を射影します。</summary>
      <returns>C# では IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt;、Visual Basic では IQueryable(Of IGrouping(Of TKey, TElement))。ここでは、各 <see cref="T:System.Linq.IGrouping`2" /> に、<paramref name="TElement" /> 型のオブジェクトのシーケンス、およびキーが格納されています。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <param name="elementSelector">ソースの各要素を <see cref="T:System.Linq.IGrouping`2" /> の要素に割り当てる関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <typeparam name="TElement">各 <see cref="T:System.Linq.IGrouping`2" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、または <paramref name="elementSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>指定された関数を使用して、シーケンスの要素をグループ化し、各グループの要素を射影します。キー値の比較には、指定された比較子を使用します。</summary>
      <returns>C# では IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt;、Visual Basic では IQueryable(Of IGrouping(Of TKey, TElement))。ここでは、各 <see cref="T:System.Linq.IGrouping`2" /> に、<paramref name="TElement" /> 型のオブジェクトのシーケンス、およびキーが格納されています。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <param name="elementSelector">ソースの各要素を <see cref="T:System.Linq.IGrouping`2" /> の要素に割り当てる関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <typeparam name="TElement">各 <see cref="T:System.Linq.IGrouping`2" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="elementSelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}})">
      <summary>指定されたキー セレクター関数に従ってシーケンスの要素をグループ化し、各グループとそのキーから結果値を作成します。各グループの要素は、指定された関数を使用して射影されます。</summary>
      <returns>
        <paramref name="TResult" /> の型引数を持つ T:System.Linq.IQueryable`1。各要素は、グループとそのキーの射影を表します。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <param name="elementSelector">ソースの各要素を <see cref="T:System.Linq.IGrouping`2" /> の要素に割り当てる関数。</param>
      <param name="resultSelector">各グループから結果値を作成する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <typeparam name="TElement">各 <see cref="T:System.Linq.IGrouping`2" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> によって返される結果値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="elementSelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>指定されたキー セレクター関数に従ってシーケンスの要素をグループ化し、各グループとそのキーから結果値を作成します。キーの比較には、指定された比較子を使用し、各グループの要素の射影には、指定された関数を使用します。</summary>
      <returns>
        <paramref name="TResult" /> の型引数を持つ T:System.Linq.IQueryable`1。各要素は、グループとそのキーの射影を表します。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <param name="elementSelector">ソースの各要素を <see cref="T:System.Linq.IGrouping`2" /> の要素に割り当てる関数。</param>
      <param name="resultSelector">各グループから結果値を作成する関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <typeparam name="TElement">各 <see cref="T:System.Linq.IGrouping`2" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> によって返される結果値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="elementSelector" />、<paramref name="resultSelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}})">
      <summary>指定されたキー セレクター関数に従ってシーケンスの要素をグループ化し、各グループとそのキーから結果値を作成します。</summary>
      <returns>
        <paramref name="TResult" /> の型引数を持つ T:System.Linq.IQueryable`1。各要素は、グループとそのキーの射影を表します。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <param name="resultSelector">各グループから結果値を作成する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> によって返される結果値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>指定されたキー セレクター関数に従ってシーケンスの要素をグループ化し、各グループとそのキーから結果値を作成します。キーの比較には、指定された比較子を使用します。</summary>
      <returns>
        <paramref name="TResult" /> の型引数を持つ T:System.Linq.IQueryable`1。各要素は、グループとそのキーの射影を表します。</returns>
      <param name="source">グループ化する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">各要素のキーを抽出する関数。</param>
      <param name="resultSelector">各グループから結果値を作成する関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> によって返される結果値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、<paramref name="resultSelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}})">
      <summary>キーが等しいかどうかに基づいて 2 つのシーケンスの要素を相互に関連付け、その結果をグループ化します。キーの比較には既定の等値比較子が使用されます。</summary>
      <returns>2 つのシーケンスに対してグループ化結合を実行して取得した <paramref name="TResult" /> 型の要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="outer">結合する最初のシーケンス。</param>
      <param name="inner">最初のシーケンスに結合するシーケンス。</param>
      <param name="outerKeySelector">最初のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="innerKeySelector">2 番目のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="resultSelector">最初のシーケンスの要素と、2 番目のシーケンスの一致する要素のコレクションから結果の要素を作成する関数。</param>
      <typeparam name="TOuter">最初のシーケンスの要素の型。</typeparam>
      <typeparam name="TInner">2 番目のシーケンスの要素の型。</typeparam>
      <typeparam name="TKey">キー セレクター関数によって返されるキーの型。</typeparam>
      <typeparam name="TResult">結果の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>キーが等しいかどうかに基づいて 2 つのシーケンスの要素を相互に関連付け、その結果をグループ化します。指定された <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用してキーを比較します。</summary>
      <returns>2 つのシーケンスに対してグループ化結合を実行して取得した <paramref name="TResult" /> 型の要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="outer">結合する最初のシーケンス。</param>
      <param name="inner">最初のシーケンスに結合するシーケンス。</param>
      <param name="outerKeySelector">最初のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="innerKeySelector">2 番目のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="resultSelector">最初のシーケンスの要素と、2 番目のシーケンスの一致する要素のコレクションから結果の要素を作成する関数。</param>
      <param name="comparer">キーをハッシュして比較する比較子。</param>
      <typeparam name="TOuter">最初のシーケンスの要素の型。</typeparam>
      <typeparam name="TInner">2 番目のシーケンスの要素の型。</typeparam>
      <typeparam name="TKey">キー セレクター関数によって返されるキーの型。</typeparam>
      <typeparam name="TResult">結果の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>既定の等値比較子を使用して値を比較することにより、2 つのシーケンスの積集合を生成します。</summary>
      <returns>2 つのシーケンスの積集合を含むシーケンス。</returns>
      <param name="source1">
        <paramref name="source2" /> にも含まれる、返される一意の要素を含むシーケンス。</param>
      <param name="source2">最初のシーケンスにも含まれる、返される一意の要素を含むシーケンス。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>指定された <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用して値を比較することにより、2 つのシーケンスの積集合を生成します。</summary>
      <returns>2 つのシーケンスの積集合を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source1">
        <paramref name="source2" /> にも含まれる、返される一意の要素を含む <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="source2">最初のシーケンスにも含まれる、返される一意の要素を含む <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="comparer">値を比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}})">
      <summary>一致するキーに基づいて 2 つのシーケンスの要素を相互に関連付けます。キーの比較には既定の等値比較子が使用されます。</summary>
      <returns>2 つのシーケンスに対して内部結合を実行して取得した <paramref name="TResult" /> 型の要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="outer">結合する最初のシーケンス。</param>
      <param name="inner">最初のシーケンスに結合するシーケンス。</param>
      <param name="outerKeySelector">最初のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="innerKeySelector">2 番目のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="resultSelector">一致する 2 つの要素から結果の要素を作成する関数。</param>
      <typeparam name="TOuter">最初のシーケンスの要素の型。</typeparam>
      <typeparam name="TInner">2 番目のシーケンスの要素の型。</typeparam>
      <typeparam name="TKey">キー セレクター関数によって返されるキーの型。</typeparam>
      <typeparam name="TResult">結果の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>一致するキーに基づいて 2 つのシーケンスの要素を相互に関連付けます。指定された <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用してキーを比較します。</summary>
      <returns>2 つのシーケンスに対して内部結合を実行して取得した <paramref name="TResult" /> 型の要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="outer">結合する最初のシーケンス。</param>
      <param name="inner">最初のシーケンスに結合するシーケンス。</param>
      <param name="outerKeySelector">最初のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="innerKeySelector">2 番目のシーケンスの各要素から結合キーを抽出する関数。</param>
      <param name="resultSelector">一致する 2 つの要素から結果の要素を作成する関数。</param>
      <param name="comparer">キーをハッシュして比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TOuter">最初のシーケンスの要素の型。</typeparam>
      <typeparam name="TInner">2 番目のシーケンスの要素の型。</typeparam>
      <typeparam name="TKey">キー セレクター関数によって返されるキーの型。</typeparam>
      <typeparam name="TResult">結果の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" />、<paramref name="inner" />、<paramref name="outerKeySelector" />、<paramref name="innerKeySelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスの最後の要素を返します。</summary>
      <returns>
        <paramref name="source" /> の最後の位置にある値。</returns>
      <param name="source">最後の要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">ソース シーケンスが空です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定された条件を満たす、シーケンスの最後の要素を返します。</summary>
      <returns>
        <paramref name="predicate" /> で指定されたテストに合格する、<paramref name="source" /> の最後の要素。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="predicate" /> の条件を満たす要素がありません。またはソース シーケンスが空です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスの最後の要素を返します。シーケンスに要素が含まれていない場合は既定値を返します。</summary>
      <returns>
        <paramref name="source" /> が空の場合は default (<paramref name="TSource" />)。それ以外の場合は <paramref name="source" /> の最後の要素。</returns>
      <param name="source">最後の要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>条件を満たす、シーケンスの最後の要素を返します。このような要素が見つからない場合は既定値を返します。</summary>
      <returns>
        <paramref name="source" /> が空の場合、または述語関数でテストに合格する要素がない場合は default (<paramref name="TSource" />)。それ以外の場合は、述語関数でテストに合格する、<paramref name="source" /> の最後の要素。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0})">
      <summary>シーケンス内の要素の合計数を表す <see cref="T:System.Int64" /> を返します。</summary>
      <returns>
        <paramref name="source" /> にある要素の数。</returns>
      <param name="source">カウントする要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">要素数が <see cref="F:System.Int64.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>条件を満たす、シーケンス内の要素の数を表す <see cref="T:System.Int64" /> を返します。</summary>
      <returns>述語関数の条件を満たす、<paramref name="source" /> の要素数。</returns>
      <param name="source">カウントする要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
      <exception cref="T:System.OverflowException">一致する要素数が <see cref="F:System.Int64.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``1(System.Linq.IQueryable{``0})">
      <summary>ジェネリックの <see cref="T:System.Linq.IQueryable`1" /> にある最大値を返します。</summary>
      <returns>シーケンスの最大値。</returns>
      <param name="source">最大値を確認する対象となる値のシーケンス。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>ジェネリックの <see cref="T:System.Linq.IQueryable`1" /> の各要素に対して射影関数を呼び出し、結果の最大値を返します。</summary>
      <returns>シーケンスの最大値。</returns>
      <param name="source">最大値を確認する対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> で表された関数によって返される値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``1(System.Linq.IQueryable{``0})">
      <summary>ジェネリックの <see cref="T:System.Linq.IQueryable`1" /> にある最小値を返します。</summary>
      <returns>シーケンスの最小値。</returns>
      <param name="source">最小値を確認する対象となる値のシーケンス。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>ジェネリックの <see cref="T:System.Linq.IQueryable`1" /> の各要素に対して射影関数を呼び出し、結果の最小値を返します。</summary>
      <returns>シーケンスの最小値。</returns>
      <param name="source">最小値を確認する対象となる値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> で表された関数によって返される値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OfType``1(System.Linq.IQueryable)">
      <summary>指定された型に基づいて <see cref="T:System.Linq.IQueryable" /> の要素をフィルター処理します。</summary>
      <returns>
        <paramref name="TResult" /> 型を持つ、<paramref name="source" /> の要素を含むコレクション。</returns>
      <param name="source">フィルター処理する要素を含む <see cref="T:System.Linq.IQueryable" />。</param>
      <typeparam name="TResult">シーケンスの要素をフィルター処理する型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>シーケンスの要素をキーに従って昇順に並べ替えます。</summary>
      <returns>要素がキーに従って並べ替えられている <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">順序付ける値のシーケンス。</param>
      <param name="keySelector">要素からキーを抽出する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表される関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="keySelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>指定された比較子を使用してシーケンスの要素を昇順に並べ替えます。</summary>
      <returns>要素がキーに従って並べ替えられている <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">順序付ける値のシーケンス。</param>
      <param name="keySelector">要素からキーを抽出する関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表される関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>シーケンスの要素をキーに従って降順に並べ替えます。</summary>
      <returns>要素がキーに従って降順に並べ替えられている <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">順序付ける値のシーケンス。</param>
      <param name="keySelector">要素からキーを抽出する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表される関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="keySelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>指定された比較子を使用してシーケンスの要素を降順に並べ替えます。</summary>
      <returns>要素がキーに従って降順に並べ替えられている <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">順序付ける値のシーケンス。</param>
      <param name="keySelector">要素からキーを抽出する関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表される関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Reverse``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスの要素の順序を反転させます。</summary>
      <returns>要素が入力シーケンスの要素に逆順で対応している <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">反転させる値のシーケンス。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>シーケンスの各要素を新しいフォームに射影します。</summary>
      <returns>
        <paramref name="source" /> の各要素に対して射影関数を呼び出した結果として得られる要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">射影する値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> で表された関数によって返される値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,``1}})">
      <summary>要素のインデックスを組み込むことにより、シーケンスの各要素を新しいフォームに射影します。</summary>
      <returns>
        <paramref name="source" /> の各要素に対して射影関数を呼び出した結果として得られる要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">射影する値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> で表された関数によって返される値の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>シーケンスの各要素を <see cref="T:System.Collections.Generic.IEnumerable`1" /> に射影し、その各要素で結果のセレクター関数を呼び出します。各中間シーケンスの結果として得られる値は、1 つの 1 次元シーケンスに結合され、返されます。</summary>
      <returns>
        <paramref name="source" /> の各要素で一対多の射影関数 <paramref name="collectionSelector" /> を呼び出し、こうしたシーケンスの各要素とそれに対応する <paramref name="source" /> 要素を結果の要素に割り当てた結果として得られる要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">射影する値のシーケンス。</param>
      <param name="collectionSelector">入力シーケンスの各要素に適用する射影関数。</param>
      <param name="resultSelector">各中間シーケンスの各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TCollection">
        <paramref name="collectionSelector" /> で表される関数によって収集される中間要素の型。</typeparam>
      <typeparam name="TResult">結果のシーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="collectionSelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>シーケンスの各要素を <see cref="T:System.Collections.Generic.IEnumerable`1" /> に射影し、結果のシーケンスを 1 つのシーケンスに結合します。</summary>
      <returns>入力シーケンスの各要素で一対多の射影関数を呼び出した結果として得られる要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">射影する値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> で表される関数によって返されるシーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>シーケンスの各要素を、それを生成したソース要素のインデックスを組み込む <see cref="T:System.Collections.Generic.IEnumerable`1" /> に射影します。結果のセレクター関数は、各中間シーケンスの各要素に対して呼び出されます。結果値は 1 つの 1 次元シーケンスに結合され、返されます。</summary>
      <returns>
        <paramref name="source" /> の各要素で一対多の射影関数 <paramref name="collectionSelector" /> を呼び出し、こうしたシーケンスの各要素とそれに対応する <paramref name="source" /> 要素を結果の要素に割り当てた結果として得られる要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">射影する値のシーケンス。</param>
      <param name="collectionSelector">入力シーケンスの各要素に適用する射影関数。この関数の 2 つ目のパラメーターは、ソース要素のインデックスを表します。</param>
      <param name="resultSelector">各中間シーケンスの各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TCollection">
        <paramref name="collectionSelector" /> で表される関数によって収集される中間要素の型。</typeparam>
      <typeparam name="TResult">結果のシーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="collectionSelector" />、または <paramref name="resultSelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>シーケンスの各要素を <see cref="T:System.Collections.Generic.IEnumerable`1" /> に射影し、結果のシーケンスを 1 つのシーケンスに結合します。各ソース要素のインデックスは、その要素の射影されたフォームで使用されます。</summary>
      <returns>入力シーケンスの各要素で一対多の射影関数を呼び出した結果として得られる要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">射影する値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。この関数の 2 つ目のパラメーターは、ソース要素のインデックスを表します。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TResult">
        <paramref name="selector" /> で表される関数によって返されるシーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>既定の等値比較子を使用して要素を比較することで、2 つのシーケンスが等しいかどうかを判断します。</summary>
      <returns>2 つのソース シーケンスが同じ長さで、それらに対応する要素の比較が等しい場合は true。それ以外の場合は false。</returns>
      <param name="source1">
        <paramref name="source2" /> の要素と比較する要素が含まれている <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="source2">最初のシーケンスの要素と比較する要素が含まれている <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>指定された <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用して要素を比較することで、2 つのシーケンスが等しいかどうかを判断します。</summary>
      <returns>2 つのソース シーケンスが同じ長さで、それらに対応する要素の比較が等しい場合は true。それ以外の場合は false。</returns>
      <param name="source1">
        <paramref name="source2" /> の要素と比較する要素が含まれている <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="source2">最初のシーケンスの要素と比較する要素が含まれている <see cref="T:System.Collections.Generic.IEnumerable`1" />。</param>
      <param name="comparer">要素の比較に使用する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスの唯一の要素を返します。シーケンス内の要素が 1 つだけではない場合は、例外をスローします。</summary>
      <returns>入力シーケンスの 1 つの要素。</returns>
      <param name="source">1 つの要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> には複数の要素があります。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定された条件を満たす、シーケンスの唯一の要素を返します。そのような要素が複数存在する場合は、例外をスローします。</summary>
      <returns>
        <paramref name="predicate" /> の条件を満たす、入力シーケンスの 1 つの要素。</returns>
      <param name="source">1 つの要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="predicate" /> の条件を満たす要素がありません。または<paramref name="predicate" /> の条件を満たす要素が複数あります。またはソース シーケンスが空です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>シーケンスの唯一の要素を返します。シーケンスが空の場合、既定値を返します。シーケンス内に要素が複数ある場合、このメソッドは例外をスローします。</summary>
      <returns>入力シーケンスの 1 つの要素。シーケンスに要素が含まれない場合は default (<paramref name="TSource" />)。</returns>
      <param name="source">1 つの要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> には複数の要素があります。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定された条件を満たす、シーケンスの唯一の要素を返します。そのような要素が存在しない場合、既定値を返します。複数の要素が条件を満たす場合、このメソッドは例外をスローします。</summary>
      <returns>
        <paramref name="predicate" /> の条件を満たす、入力シーケンスの 1 つの要素。そのような要素が見つからない場合は default (<paramref name="TSource" />)。</returns>
      <param name="source">1 つの要素を返す <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="predicate" /> の条件を満たす要素が複数あります。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Skip``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>シーケンス内の指定された数の要素をバイパスし、残りの要素を返します。</summary>
      <returns>入力シーケンスで指定されたインデックスの後に出現する要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="count">残りの要素を返す前にスキップする要素の数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定された条件が満たされる限り、シーケンスの要素をバイパスした後、残りの要素を返します。</summary>
      <returns>
        <paramref name="predicate" /> で指定されたテストに合格しない連続する最初の要素から <paramref name="source" /> の要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>指定された条件が満たされる限り、シーケンスの要素をバイパスした後、残りの要素を返します。要素のインデックスは、述語関数のロジックで使用されます。</summary>
      <returns>
        <paramref name="predicate" /> で指定されたテストに合格しない連続する最初の要素から <paramref name="source" /> の要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">返される要素が含まれる <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件に当てはまるかどうかをテストする関数。この関数の 2 つ目のパラメーターは、ソース要素のインデックスを表します。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Decimal})">
      <summary>
        <see cref="T:System.Decimal" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる <see cref="T:System.Decimal" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Decimal.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Double})">
      <summary>
        <see cref="T:System.Double" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる <see cref="T:System.Double" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int32})">
      <summary>
        <see cref="T:System.Int32" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる <see cref="T:System.Int32" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int32.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int64})">
      <summary>
        <see cref="T:System.Int64" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる <see cref="T:System.Int64" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int64.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>null 許容の <see cref="T:System.Decimal" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる null 許容の <see cref="T:System.Decimal" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Decimal.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>null 許容の <see cref="T:System.Double" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる null 許容の <see cref="T:System.Double" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>null 許容の <see cref="T:System.Int32" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる null 許容の <see cref="T:System.Int32" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int32.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>null 許容の <see cref="T:System.Int64" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる null 許容の <see cref="T:System.Int64" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int64.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>null 許容の <see cref="T:System.Single" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる null 許容の <see cref="T:System.Single" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Single})">
      <summary>
        <see cref="T:System.Single" /> 値のシーケンスの合計を計算します。</summary>
      <returns>シーケンスの値の合計。</returns>
      <param name="source">合計を計算する対象となる <see cref="T:System.Single" /> 値のシーケンス。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Decimal" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Decimal.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Double" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Int32" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int32.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Int64" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int64.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Decimal" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Decimal.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Double" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Int32" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int32.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Int64" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
      <exception cref="T:System.OverflowException">合計が <see cref="F:System.Int64.MaxValue" /> を超えています。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する、null 許容の <see cref="T:System.Single" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>入力シーケンスの各要素に対して射影関数を呼び出して取得する <see cref="T:System.Single" /> 値のシーケンスの合計を計算します。</summary>
      <returns>射影された値の合計。</returns>
      <param name="source">
        <paramref name="TSource" /> 型の値のシーケンス。</param>
      <param name="selector">各要素に適用する射影関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="selector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Take``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>シーケンスの先頭から、指定された数の連続する要素を返します。</summary>
      <returns>
        <paramref name="source" /> の先頭から、指定された数の要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">要素を返すシーケンス。</param>
      <param name="count">返す要素数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> は null なので、</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>指定された条件が満たされる限り、シーケンスから要素を返します。</summary>
      <returns>
        <paramref name="predicate" /> で指定されたテストに合格しなくなった要素の前に出現する、入力シーケンスの要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">要素を返すシーケンス。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>指定された条件が満たされる限り、シーケンスから要素を返します。要素のインデックスは、述語関数のロジックで使用されます。</summary>
      <returns>
        <paramref name="predicate" /> で指定されたテストに合格しなくなった要素の前に出現する、入力シーケンスの要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">要素を返すシーケンス。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。この関数の 2 つ目のパラメーターは、ソース シーケンスの要素のインデックスを表します。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>キーに従って、シーケンス内の後続の要素を昇順で配置します。</summary>
      <returns>要素がキーに従って並べ替えられている <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">並べ替える要素を格納している <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">各要素からキーを抽出する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="keySelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>指定された比較子を使用して、シーケンス内の後続の要素を昇順で配置します。</summary>
      <returns>要素がキーに従って並べ替えられている <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">並べ替える要素を格納している <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">各要素からキーを抽出する関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>キーに従って、シーケンス内の後続の要素を降順で配置します。</summary>
      <returns>要素がキーに従って降順に並べ替えられている <see cref="T:System.Linq.IOrderedQueryable`1" />。</returns>
      <param name="source">並べ替える要素を格納している <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">各要素からキーを抽出する関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> で表された関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="keySelector" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>指定された比較子を使用して、シーケンス内の後続の要素を降順で配置します。</summary>
      <returns>要素がキーに従って降順に並べ替えられているコレクション。</returns>
      <param name="source">並べ替える要素を格納している <see cref="T:System.Linq.IOrderedQueryable`1" />。</param>
      <param name="keySelector">各要素からキーを抽出する関数。</param>
      <param name="comparer">キーを比較する <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <typeparam name="TKey">
        <paramref name="keySelector" /> 関数によって返されるキーの型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" />、<paramref name="keySelector" />、または <paramref name="comparer" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>既定の等値比較子を使用して、2 つのシーケンスの和集合を生成します。</summary>
      <returns>2 つの入力シーケンスの要素 (重複する要素は除く) を格納している <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source1">和集合演算の最初のセットを形成する各要素を含むシーケンス。</param>
      <param name="source2">和集合演算の 2 番目のセットを形成する各要素を含むシーケンス。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>指定された <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> を使用して 2 つのシーケンスの和集合を生成します。</summary>
      <returns>2 つの入力シーケンスの要素 (重複する要素は除く) を格納している <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source1">和集合演算の最初のセットを形成する各要素を含むシーケンス。</param>
      <param name="source2">和集合演算の 2 番目のセットを形成する各要素を含むシーケンス。</param>
      <param name="comparer">値を比較する <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">入力シーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>述語に基づいて値のシーケンスをフィルター処理します。</summary>
      <returns>
        <paramref name="predicate" /> で指定された条件を満たす、入力シーケンスの要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">フィルター処理する <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>述語に基づいて値のシーケンスをフィルター処理します。各要素のインデックスは、述語関数のロジックで使用されます。</summary>
      <returns>
        <paramref name="predicate" /> で指定された条件を満たす、入力シーケンスの要素を含む <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">フィルター処理する <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">各要素が条件を満たしているかどうかをテストする関数。この関数の 2 つ目のパラメーターは、ソース シーケンスの要素のインデックスを表します。</param>
      <typeparam name="TSource">
        <paramref name="source" /> の要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> または <paramref name="predicate" /> が null です。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Zip``3(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>指定された述語関数を使用して 2 つのシーケンスをマージします。</summary>
      <returns>2 つの入力シーケンスのマージされた要素が格納されている <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source1">マージする 1 番目のシーケンス。</param>
      <param name="source2">マージする 2 番目のシーケンス。</param>
      <param name="resultSelector">2 つのシーケンスの要素をマージする方法を指定する関数。</param>
      <typeparam name="TFirst">1 番目の入力シーケンスの要素の型。</typeparam>
      <typeparam name="TSecond">2 番目の入力シーケンスの要素の型。</typeparam>
      <typeparam name="TResult">結果のシーケンスの要素の型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> または <paramref name="source2 " />が null です。</exception>
    </member>
  </members>
</doc>