﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Dynamic.Runtime</name>
  </assembly>
  <members>
    <member name="T:System.Dynamic.BinaryOperationBinder">
      <summary>Представляет бинарную динамическую операцию в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Dynamic.BinaryOperationBinder" />.</summary>
      <param name="operation">Вид бинарной операции.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической бинарной операции.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции.</param>
      <param name="args">Массив аргументов динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку бинарной динамической операции, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической бинарной операции.</param>
      <param name="arg">Правый операнд динамической бинарной операции.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической бинарной операции, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической бинарной операции.</param>
      <param name="arg">Правый операнд динамической бинарной операции.</param>
      <param name="errorSuggestion">Результат привязки в случае ее неудачного завершения или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.Operation">
      <summary>Вид бинарной операции.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.ExpressionType" />, который предоставляет вид бинарной операции.</returns>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.BindingRestrictions">
      <summary>Представляет набор ограничений привязки <see cref="T:System.Dynamic.DynamicMetaObject" />, при соблюдении которых динамическая привязка является допустимой.</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Combine(System.Collections.Generic.IList{System.Dynamic.DynamicMetaObject})">
      <summary>Объединяет ограничения привязки из списка экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" /> в один набор ограничений.</summary>
      <returns>Новый набор ограничений привязки.</returns>
      <param name="contributingObjects">Список экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" />, ограничения которых объединяются в общий набор.</param>
    </member>
    <member name="F:System.Dynamic.BindingRestrictions.Empty">
      <summary>Представляет пустой набор ограничений привязки.Это поле доступно только для чтения.</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetExpressionRestriction(System.Linq.Expressions.Expression)">
      <summary>Создает ограничение привязки, которое проверяет выражение на наличие неизменяемых свойств.</summary>
      <returns>Новые ограничения привязки.</returns>
      <param name="expression">Выражение, представляющее ограничения.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetInstanceRestriction(System.Linq.Expressions.Expression,System.Object)">
      <summary>Создает ограничение привязки, которое проверяет удостоверения экземпляров объектов в выражении.</summary>
      <returns>Новые ограничения привязки.</returns>
      <param name="expression">Выражение для проверки.</param>
      <param name="instance">Точный экземпляр объекта для проверки.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetTypeRestriction(System.Linq.Expressions.Expression,System.Type)">
      <summary>Создает ограничение привязки, которое проверяет удостоверение типа среды выполнения в выражении.</summary>
      <returns>Новые ограничения привязки.</returns>
      <param name="expression">Выражение для проверки.</param>
      <param name="type">Точный тип для проверки.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Merge(System.Dynamic.BindingRestrictions)">
      <summary>Объединяет набор ограничений привязки с текущими ограничениями привязки.</summary>
      <returns>Новый набор ограничений привязки.</returns>
      <param name="restrictions">Набор ограничений привязки, объединяемый с текущими ограничениями привязки.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.ToExpression">
      <summary>Создает объект <see cref="T:System.Linq.Expressions.Expression" />, представляющий ограничения привязки.</summary>
      <returns>Дерево выражения, представляющее ограничения.</returns>
    </member>
    <member name="T:System.Dynamic.CallInfo">
      <summary>Описывает аргументы в процессе динамической привязки.</summary>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Создает новый объект CallInfo, представляющий аргументы в процессе динамической привязки.</summary>
      <param name="argCount">Число аргументов.</param>
      <param name="argNames">Имена аргументов.</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.String[])">
      <summary>Создает новый объект PositionalArgumentInfo.</summary>
      <param name="argCount">Число аргументов.</param>
      <param name="argNames">Имена аргументов.</param>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentCount">
      <summary>Число аргументов.</summary>
      <returns>Число аргументов.</returns>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentNames">
      <summary>Имена аргументов.</summary>
      <returns>Доступная только для чтения коллекция имен аргументов.</returns>
    </member>
    <member name="M:System.Dynamic.CallInfo.Equals(System.Object)">
      <summary>Определяет, равен ли указанный экземпляр CallInfo текущему экземпляру.</summary>
      <returns>Значение true, если указанный экземпляр равен текущему экземпляру; в противном случае — значение false.</returns>
      <param name="obj">Экземпляр <see cref="T:System.Dynamic.CallInfo" />, сравниваемый с текущим экземпляром.</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.GetHashCode">
      <summary>Служит в качестве хэш-функции для текущего экземпляра <see cref="T:System.Dynamic.CallInfo" />.</summary>
      <returns>Хэш-код для текущего объекта <see cref="T:System.Dynamic.CallInfo" />.</returns>
    </member>
    <member name="T:System.Dynamic.ConvertBinder">
      <summary>Представляет динамическую операцию преобразования в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.#ctor(System.Type,System.Boolean)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.ConvertBinder" />.</summary>
      <param name="type">Тип, в который выполняется преобразование.</param>
      <param name="explicit">Значение true, если преобразование должно быть явным; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции преобразования.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции преобразования.</param>
      <param name="args">Массив аргументов динамической операции преобразования.</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Explicit">
      <summary>Получает значение, указывающее, должно ли преобразование быть явным.</summary>
      <returns>Значение True, если преобразование является явным; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции преобразования, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции преобразования.</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции преобразования, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции преобразования.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Type">
      <summary>Тип, в который выполняется преобразование.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип, в который выполняется преобразование.</returns>
    </member>
    <member name="T:System.Dynamic.CreateInstanceBinder">
      <summary>Представляет динамическую операцию создания в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Dynamic.CreateInstanceBinder" />.</summary>
      <param name="callInfo">Сигнатура аргументов в источнике вызова.</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции создания.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции создания.</param>
      <param name="args">Массив аргументов динамической операции создания.</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.CallInfo">
      <summary>Получает сигнатуру аргументов в источнике вызова.</summary>
      <returns>Сигнатура аргументов в источнике вызова.</returns>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции создания, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции создания.</param>
      <param name="args">Аргументы динамической операции создания.</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции создания, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции создания.</param>
      <param name="args">Аргументы динамической операции создания.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.DeleteIndexBinder">
      <summary>Представляет динамическую операцию удаления индекса в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.DeleteIndexBinder" />.</summary>
      <param name="callInfo">Сигнатура аргументов в источнике вызова.</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции удаления индекса.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции удаления индекса.</param>
      <param name="args">Массив аргументов динамической операции удаления индекса.</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.CallInfo">
      <summary>Получает сигнатуру аргументов в источнике вызова.</summary>
      <returns>Сигнатура аргументов в источнике вызова.</returns>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции удаления индекса, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции удаления индекса.</param>
      <param name="indexes">Аргументы динамической операции удаления индекса.</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции удаления индекса, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции удаления индекса.</param>
      <param name="indexes">Аргументы динамической операции удаления индекса.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.DeleteMemberBinder">
      <summary>Представляет динамическую операцию удаления члена в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.DeleteIndexBinder" />.</summary>
      <param name="name">Имя члена, который требуется удалить.</param>
      <param name="ignoreCase">Значение true, если имя должно сравниваться без учета регистра; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции удаления члена.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции удаления члена.</param>
      <param name="args">Массив аргументов динамической операции удаления члена.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции удаления члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции удаления члена.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции удаления члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции удаления члена.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.IgnoreCase">
      <summary>Получает значение, указывающее, следует ли игнорировать регистр имен членов при сравнении строк.</summary>
      <returns>Значение true, если строки должны сравниваться без учета регистра; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.Name">
      <summary>Получает имя члена, который требуется удалить.</summary>
      <returns>Имя члена, который требуется удалить.</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObject">
      <summary>Представляет динамическую привязку и логику привязки объекта, участвующего в динамической привязке.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <param name="expression">Выражение, представляющее данный объект <see cref="T:System.Dynamic.DynamicMetaObject" /> в процессе динамической привязки.</param>
      <param name="restrictions">Набор ограничений привязки, при соблюдении которых привязка является допустимой.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions,System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <param name="expression">Выражение, представляющее данный объект <see cref="T:System.Dynamic.DynamicMetaObject" /> в процессе динамической привязки.</param>
      <param name="restrictions">Набор ограничений привязки, при соблюдении которых привязка является допустимой.</param>
      <param name="value">Значение среды выполнения, представленное объектом <see cref="T:System.Dynamic.DynamicMetaObject" />.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической бинарной операции.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.BinaryOperationBinder" />, который представляет сведения о динамической операции.</param>
      <param name="arg">Экземпляр класса <see cref="T:System.Dynamic.DynamicMetaObject" />, который представляет правый операнд бинарной операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindConvert(System.Dynamic.ConvertBinder)">
      <summary>Выполняет привязку динамической операции преобразования.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр <see cref="T:System.Dynamic.ConvertBinder" />, который представляет сведения о динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindCreateInstance(System.Dynamic.CreateInstanceBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции создания экземпляра.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.CreateInstanceBinder" />, который представляет сведения о динамической операции.</param>
      <param name="args">Массив экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" />, которые являются аргументами операции создания экземпляра.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции удаления индекса.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.DeleteIndexBinder" />, который представляет сведения о динамической операции.</param>
      <param name="indexes">Массив экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" />, которые являются индексами для операции удаления индекса.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>Выполняет привязку динамической операции удаления члена.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.DeleteMemberBinder" />, который представляет сведения о динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetIndex(System.Dynamic.GetIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции получения индекса.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.GetIndexBinder" />, который представляет сведения о динамической операции.</param>
      <param name="indexes">Массив экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" />, которые являются индексами для операции получения индекса.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetMember(System.Dynamic.GetMemberBinder)">
      <summary>Выполняет привязку динамической операции получения члена.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.GetMemberBinder" />, который представляет сведения о динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvoke(System.Dynamic.InvokeBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции вызова.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.InvokeBinder" />, который представляет сведения о динамической операции.</param>
      <param name="args">Массив экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" />, которые являются аргументами для операции вызова.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvokeMember(System.Dynamic.InvokeMemberBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции вызова члена.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.InvokeMemberBinder" />, который представляет сведения о динамической операции.</param>
      <param name="args">Массив экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" />, которые являются аргументами для операции вызова члена.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetIndex(System.Dynamic.SetIndexBinder,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции задания индекса.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.SetIndexBinder" />, который представляет сведения о динамической операции.</param>
      <param name="indexes">Массив экземпляров <see cref="T:System.Dynamic.DynamicMetaObject" />, которые являются индексами для операции задания индекса.</param>
      <param name="value">Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий значение операции задания индекса.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetMember(System.Dynamic.SetMemberBinder,System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции задания члена.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр класса <see cref="T:System.Dynamic.SetMemberBinder" />, который представляет сведения о динамической операции.</param>
      <param name="value">Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий значение операции задания члена.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindUnaryOperation(System.Dynamic.UnaryOperationBinder)">
      <summary>Выполняет привязку динамической унарной операции.</summary>
      <returns>Новый объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="binder">Экземпляр <see cref="T:System.Dynamic.UnaryOperationBinder" />, который представляет сведения о динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.Create(System.Object,System.Linq.Expressions.Expression)">
      <summary>Создает метаобъект для указанного объекта.</summary>
      <returns>Если заданный объект реализует интерфейс <see cref="T:System.Dynamic.IDynamicMetaObjectProvider" /> и не является удаленным объектом из другого домена приложения, метод <see cref="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)" /> возвращает метаобъект, относящийся к конкретному объекту.В противном случае создается и возвращается новый обычный метаобъект без ограничений.</returns>
      <param name="value">Объект, для которого создается метаобъект.</param>
      <param name="expression">Выражение, представляющее данный объект <see cref="T:System.Dynamic.DynamicMetaObject" /> в процессе динамической привязки.</param>
    </member>
    <member name="F:System.Dynamic.DynamicMetaObject.EmptyMetaObjects">
      <summary>Представляет пустой массив типа <see cref="T:System.Dynamic.DynamicMetaObject" />.Это поле доступно только для чтения.</summary>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Expression">
      <summary>Выражение, представляющее объект <see cref="T:System.Dynamic.DynamicMetaObject" /> в процессе динамической привязки.</summary>
      <returns>Выражение, представляющее объект <see cref="T:System.Dynamic.DynamicMetaObject" /> в процессе динамической привязки.</returns>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.GetDynamicMemberNames">
      <summary>Возвращает перечисление имен всех динамических членов.</summary>
      <returns>Список имен динамических членов.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.HasValue">
      <summary>Получает значение, показывающее, имеет ли объект <see cref="T:System.Dynamic.DynamicMetaObject" /> значение среды выполнения.</summary>
      <returns>Значение true, если объект <see cref="T:System.Dynamic.DynamicMetaObject" /> имеет значение среды выполнения; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.LimitType">
      <summary>Получает тип ограничения <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <returns>Значение <see cref="P:System.Dynamic.DynamicMetaObject.RuntimeType" />, если значение среды выполнения доступно; в противном случае — тип свойства <see cref="P:System.Dynamic.DynamicMetaObject.Expression" />.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Restrictions">
      <summary>Набор ограничений привязки, при соблюдении которых привязка является допустимой.</summary>
      <returns>Набор ограничений привязки.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.RuntimeType">
      <summary>Получает объект <see cref="T:System.Type" /> значения среды выполнения или NULL, если с объектом <see cref="T:System.Dynamic.DynamicMetaObject" /> не связано никакое значение.</summary>
      <returns>Объект <see cref="T:System.Type" /> значения среды выполнения или NULL.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Value">
      <summary>Значение среды выполнения, представленное объектом <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <returns>Значение среды выполнения, представленное объектом <see cref="T:System.Dynamic.DynamicMetaObject" />.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObjectBinder">
      <summary>Связыватель динамического источника вызова, участвующий в протоколе привязки <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Dynamic.DynamicMetaObjectBinder" />.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>При переопределении в производном классе выполняет привязку динамической операции.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции.</param>
      <param name="args">Массив аргументов динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>Выполняет привязку динамической операции к набору аргументов в среде выполнения.</summary>
      <returns>Выражение, проверяющее аргументы динамической операции и выполняющее эту операцию, если проверки пройдены успешно.Если проверки не пройдены при последующих выполнениях динамической операции, метод Bind вызывается еще раз, чтобы создать новый объект <see cref="T:System.Linq.Expressions.Expression" /> для новых типов аргументов.</returns>
      <param name="args">Массив аргументов динамической операции.</param>
      <param name="parameters">Массив экземпляров класса <see cref="T:System.Linq.Expressions.ParameterExpression" />, представляющих параметры источника вызова в процессе привязки.</param>
      <param name="returnLabel">Элемент LabelTarget, возвращающий результат динамической привязки.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Откладывает привязку операции до вычисления значений среды выполнения всех аргументов динамической операции.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции.</param>
      <param name="args">Массив аргументов динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject[])">
      <summary>Откладывает привязку операции до вычисления значений среды выполнения всех аргументов динамической операции.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="args">Массив аргументов динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.GetUpdateExpression(System.Type)">
      <summary>Получает выражение, которое вызовет обновление привязки.Она указывает, что привязка выражения более недействительна.Обычно используется в случае изменения "версии" динамического объекта.</summary>
      <returns>Выражение обновления.</returns>
      <param name="type">Свойство <see cref="P:System.Linq.Expressions.Expression.Type" /> итогового выражения; допустим любой тип.</param>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObjectBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicObject">
      <summary>Предоставляет базовый класс для указания динамического поведения во время выполнения.Этот класс должен наследоваться; непосредственно создавать его экземпляры нельзя.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.#ctor">
      <summary>Позволяет производным типам инициализировать новый экземпляр типа <see cref="T:System.Dynamic.DynamicObject" />.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetDynamicMemberNames">
      <summary>Возвращает перечисление имен всех динамических членов. </summary>
      <returns>Последовательность, содержащая имена динамических членов.</returns>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>Предоставляет объект <see cref="T:System.Dynamic.DynamicMetaObject" />, вызывающий динамические виртуальные методы.Объект можно инкапсулировать в другой объект <see cref="T:System.Dynamic.DynamicMetaObject" />, чтобы обеспечить пользовательское поведение для отдельных действий.Данный метод поддерживает инфраструктуру среды DLR для разработчиков языков и не предназначен для непосредственного использования из кода.</summary>
      <returns>Объект типа <see cref="T:System.Dynamic.DynamicMetaObject" />.</returns>
      <param name="parameter">Выражение, представляющее объект <see cref="T:System.Dynamic.DynamicMetaObject" />, вызывающий динамические виртуальные методы.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Object,System.Object@)">
      <summary>Предоставляет реализацию для двоичных операций.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для таких операций, как сложение и умножение.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения о двоичной операции.Свойство binder.Operation возвращает объект <see cref="T:System.Linq.Expressions.ExpressionType" />.Например, для оператора sum = first + second, где first и second являются производными класса DynamicObject, binder.Operation возвращает значение ExpressionType.Add.</param>
      <param name="arg">Правый операнд для двоичной операции.Например, для оператора sum = first + second, где first и second являются производными класса DynamicObject, <paramref name="arg" /> равен second.</param>
      <param name="result">Результат двоичной операции.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
      <summary>Предоставляет реализацию для операций преобразования типа.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для операций, преобразующих объект из одного типа в другой.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об операции преобразования.Свойство binder.Type предоставляет тип, в который необходимо преобразовать объект.Например, для оператора (String)sampleObject в C# (CType(sampleObject, Type) в Visual Basic), где sampleObject — экземпляр класса, производного от класса <see cref="T:System.Dynamic.DynamicObject" />, binder.Type возвращает тип <see cref="T:System.String" />.Свойство binder.Explicit предоставляет сведения о виде преобразования, который имеет место.Для явного преобразования возвращается значение true, для неявного — значение false.</param>
      <param name="result">Результат операции преобразования типа.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryCreateInstance(System.Dynamic.CreateInstanceBinder,System.Object[],System.Object@)">
      <summary>Предоставляет реализацию для операций, инициализирующих новый экземпляр динамического объекта.Этот метод не предназначен для использования в C# или Visual Basic.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об операции инициализации.</param>
      <param name="args">Аргументы, переданные объекту во время инициализации.Например, для операции new SampleType(100), где SampleType — тип, производный от класса <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="args[0]" /> равен 100.</param>
      <param name="result">Результат инициализации.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Object[])">
      <summary>Предоставляет реализацию для операций, удаляющих объект по индексу.Этот метод не предназначен для использования в C# или Visual Basic.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об удалении.</param>
      <param name="indexes">Удаляемые индексы.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>Предоставляет реализацию для операций, удаляющих элемент объекта.Этот метод не предназначен для использования в C# или Visual Basic.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об удалении.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetIndex(System.Dynamic.GetIndexBinder,System.Object[],System.Object@)">
      <summary>Предоставляет реализацию для операций, получающих значение по индексу.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для операций индексации.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об операции. </param>
      <param name="indexes">Индексы, которые используются в операции.Например, для операции sampleObject[3] в C# (sampleObject(3) в Visual Basic), где sampleObject является производным от класса DynamicObject, <paramref name="indexes[0]" /> равно 3.</param>
      <param name="result">Результат операции индексации.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <summary>Предоставляет реализацию для операций, получающих значения членов.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для таких операций, как получение значения свойства.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об объекте, вызвавшем динамическую операцию.Свойство binder.Name предоставляет имя члена, с которым выполняется динамическая операция.Например, для оператора Console.WriteLine(sampleObject.SampleProperty), где sampleObject является экземпляром класса, производного от класса <see cref="T:System.Dynamic.DynamicObject" />, binder.Name возвращает значение SampleProperty.Свойство binder.IgnoreCase задает, учитывается ли регистр в имени члена.</param>
      <param name="result">Результат операции получения.Например, если для свойства вызывается метод, можно присвоить свойству значение <paramref name="result" />.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvoke(System.Dynamic.InvokeBinder,System.Object[],System.Object@)">
      <summary>Предоставляет реализацию для операций, вызывающих объект.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для таких операций, как вызов объекта или делегата.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об операции вызова.</param>
      <param name="args">Аргументы, переданные объекту во время операции вызова.Например, для операции sampleObject(100), где sampleObject является производным от класса <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="args[0]" /> равен 100.</param>
      <param name="result">Результат вызова объекта.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
      <summary>Предоставляет реализацию для операций, вызывающих член.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для таких операций, как вызов метода.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения о динамической операции.Свойство binder.Name предоставляет имя члена, с которым выполняется динамическая операция.Например, для оператора sampleObject.SampleMethod(100), где sampleObject является экземпляром класса, производного от класса <see cref="T:System.Dynamic.DynamicObject" />, binder.Name возвращает значение SampleMethod.Свойство binder.IgnoreCase задает, учитывается ли регистр в имени члена.</param>
      <param name="args">Аргументы, переданные члену объекта во время операции вызова.Например, для оператора sampleObject.SampleMethod(100), где sampleObject является производным от класса <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="args[0]" /> равен 100.</param>
      <param name="result">Результат вызова члена.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetIndex(System.Dynamic.SetIndexBinder,System.Object[],System.Object)">
      <summary>Предоставляет реализацию для операций, задающих значение по индексу.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для операций, осуществляющих доступ к объектам по заданному индексу.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об операции. </param>
      <param name="indexes">Индексы, которые используются в операции.Например, для операции sampleObject[3] = 10 в C# (sampleObject(3) = 10 в Visual Basic), где sampleObject является производным от класса <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="indexes[0]" /> равно 3.</param>
      <param name="value">Значение, которое необходимо задать для объекта с заданным индексом.Например, для операции sampleObject[3] = 10 в C# (sampleObject(3) = 10 в Visual Basic), где sampleObject является производным от класса <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="value" /> равно 10.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
      <summary>Предоставляет реализацию для операций, задающих значения членов.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для таких операций, как задание значения свойства.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об объекте, вызвавшем динамическую операцию.Свойство binder.Name предоставляет имя члена, которому присваивается значение.Например, для оператора sampleObject.SampleProperty = "Test", где sampleObject является экземпляром класса, производного от класса <see cref="T:System.Dynamic.DynamicObject" />, binder.Name возвращает значение SampleProperty.Свойство binder.IgnoreCase задает, учитывается ли регистр в имени члена.</param>
      <param name="value">Значение, задаваемое для члена.Например, для sampleObject.SampleProperty = "Test", где sampleObject является экземпляром класса, производного от класса <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="value" /> является Test.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryUnaryOperation(System.Dynamic.UnaryOperationBinder,System.Object@)">
      <summary>Предоставляет реализацию для унарных операций.Классы, производные от класса <see cref="T:System.Dynamic.DynamicObject" />, могут переопределять этот метод, чтобы задать динамическое поведение для таких операций, как вычитание, увеличение или уменьшение.</summary>
      <returns>true, если операция выполнена успешно, в противном случае — false.Если данный метод возвращает значение false, поведение определяется связывателем среды языка. (В большинстве случаев создается языковое исключение во время выполнения).</returns>
      <param name="binder">Предоставляет сведения об унарной операции.Свойство binder.Operation возвращает объект <see cref="T:System.Linq.Expressions.ExpressionType" />.Например, для оператора negativeNumber = -number, где number является производным от класса DynamicObject, binder.Operation возвращает значение Negate.</param>
      <param name="result">Результат унарной операции.</param>
    </member>
    <member name="T:System.Dynamic.ExpandoObject">
      <summary>Представляет объект, члены которого можно динамически добавлять и удалять во время выполнения.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.#ctor">
      <summary>Инициализирует новый ExpandoObject, не содержащий членов.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Добавляет указанное значение в <see cref="T:System.Collections.Generic.ICollection`1" /> с указанным ключом.</summary>
      <param name="item">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, представляющая ключ и значение, добавляемые в коллекцию.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Удаляет все элементы из коллекции.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Определяет, содержит ли коллекция <see cref="T:System.Collections.Generic.ICollection`1" /> указанные ключ и значение.</summary>
      <returns>Значение true, если коллекция содержит указанные ключ и значение; в противном случае — значение false.</returns>
      <param name="item">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, которую требуется найти в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
      <summary>Копирует элементы коллекции <see cref="T:System.Collections.Generic.ICollection`1" /> в массив типа <see cref="T:System.Collections.Generic.KeyValuePair`2" />, начиная с указанного индекса массива.</summary>
      <param name="array">Одномерный массив типа <see cref="T:System.Collections.Generic.KeyValuePair`2" />, в который копируются элементы <see cref="T:System.Collections.Generic.KeyValuePair`2" /> из коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.Индексация в массиве должна вестись с нуля.</param>
      <param name="arrayIndex">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, с которого начинается копирование.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Count">
      <summary>Получает количество элементов в наборе <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>Количество элементов в коллекции <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Получает значение, указывающее, является ли объект <see cref="T:System.Collections.Generic.ICollection`1" /> доступным только для чтения.</summary>
      <returns>Значение true, если <see cref="T:System.Collections.Generic.ICollection`1" /> доступна только для чтения; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Удаляет из коллекции ключ и значение.</summary>
      <returns>Значение true, если ключ и значение найдены и удалены; в противном случае — значение false.Этот метод возвращает значение false, если ключ и значение не найдены в <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Структура <see cref="T:System.Collections.Generic.KeyValuePair`2" />, представляющая ключ и значение, которые требуется удалить из коллекции.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Add(System.String,System.Object)">
      <summary>Добавляет указанные ключ и значение в словарь.</summary>
      <param name="key">Объект, который используется в качестве ключа.</param>
      <param name="value">Объект, который используется в качестве значения.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#ContainsKey(System.String)">
      <summary>Определяет, содержится ли указанный ключ в словаре.</summary>
      <returns>true, если в словаре содержится элемент с указанным ключом; в противном случае — false.</returns>
      <param name="key">Ключ, который нужно найти в словаре.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Item(System.String)">
      <summary>Получает или задает элемент, имеющий указанный ключ.</summary>
      <returns>Элемент, имеющий указанный ключ.</returns>
      <param name="key">Ключ элемента, который требуется получить или задать.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Получает <see cref="T:System.Collections.Generic.ICollection`1" />, который содержит ключи <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />, содержащая ключи объекта, который реализует <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(System.String)">
      <summary>Удаляет из <see cref="T:System.Collections.IDictionary" /> элемент, имеющий указанный ключ.</summary>
      <returns>Значение true, если элемент успешно удален, в противном случае — значение false.Этот метод также возвращает значение false, если параметр <paramref name="key" /> не найден в исходном объекте <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
      <param name="key">Ключ удаляемого элемента.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#TryGetValue(System.String,System.Object@)">
      <summary>Получает значение, связанное с указанным ключом.</summary>
      <returns>Значение true, если объект, который реализует интерфейс <see cref="T:System.Collections.Generic.IDictionary`2" />, содержит элемент, имеющий указанный ключ; в противном случае — значение false.</returns>
      <param name="key">Ключ значения, которое необходимо получить.</param>
      <param name="value">При возвращении метода содержит значение, связанное с указанном ключом, если он найден; в противном случае — значение по умолчанию для данного типа параметра <paramref name="value" />.Этот параметр передается без инициализации.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Получает <see cref="T:System.Collections.Generic.ICollection`1" />, которая содержит значения в <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />, содержащая значения в объекте, который реализует <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.Generic.IEnumerator`1" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, выполняющий перебор элементов коллекции.</summary>
      <returns>Объект <see cref="T:System.Collections.IEnumerator" />, который может использоваться для итерации элементов коллекции.</returns>
    </member>
    <member name="E:System.Dynamic.ExpandoObject.System#ComponentModel#INotifyPropertyChanged#PropertyChanged">
      <summary>Возникает при смене значения свойства.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Dynamic#IDynamicMetaObjectProvider#GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>Предоставленный объект MetaObject будет вызывать динамические виртуальные методы.Объект можно инкапсулировать внутри другого объекта MetaObject, чтобы обеспечить пользовательское поведение для отдельных действий.</summary>
      <returns>Объект типа <see cref="T:System.Dynamic.DynamicMetaObject" />.</returns>
      <param name="parameter">Выражение, представляющее объект MetaObject, который будет вызывать динамические виртуальные методы.</param>
    </member>
    <member name="T:System.Dynamic.GetIndexBinder">
      <summary>Представляет динамическую операцию получения индекса в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.GetIndexBinder" />.</summary>
      <param name="callInfo">Сигнатура аргументов в источнике вызова.</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции получения индекса.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции получения индекса.</param>
      <param name="args">Массив аргументов динамической операции получения индекса.</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.CallInfo">
      <summary>Получает сигнатуру аргументов в источнике вызова.</summary>
      <returns>Сигнатура аргументов в источнике вызова.</returns>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции получения индекса, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции получения индекса.</param>
      <param name="indexes">Аргументы динамической операции получения индекса.</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции получения индекса, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции получения индекса.</param>
      <param name="indexes">Аргументы динамической операции получения индекса.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.GetMemberBinder">
      <summary>Представляет динамическую операцию получения члена в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.GetMemberBinder" />.</summary>
      <param name="name">Имя члена, который необходимо получить.</param>
      <param name="ignoreCase">Значение true, если имя должно сравниваться без учета регистра; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции получения члена.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции получения члена.</param>
      <param name="args">Массив аргументов динамической операции получения члена.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции получения члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции получения члена.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции получения члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции получения члена.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.IgnoreCase">
      <summary>Получает значение, указывающее, следует ли игнорировать регистр имен членов при сравнении строк.</summary>
      <returns>Значение true, если регистр знаков не учитывается; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.Name">
      <summary>Получает имя члена, который требуется получить.</summary>
      <returns>Имя члена, который необходимо получить.</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.IDynamicMetaObjectProvider">
      <summary>Представляет динамический объект, операции которого могут привязываться во время выполнения.</summary>
    </member>
    <member name="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>Возвращает объект <see cref="T:System.Dynamic.DynamicMetaObject" />, который предназначен для операций привязки, выполняемых с данным объектом.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, привязываемый к данному объекту.</returns>
      <param name="parameter">Представление дерева выражений значения среды выполнения.</param>
    </member>
    <member name="T:System.Dynamic.IInvokeOnGetBinder">
      <summary>Представляет сведения о динамической операции получения члена, указывающие, должен ли метод получения члена вызывать свойства при выполнении операции получения.</summary>
    </member>
    <member name="P:System.Dynamic.IInvokeOnGetBinder.InvokeOnGet">
      <summary>Получает значение, указывающее, должна ли данная операция получения члена вызывать свойства при выполнении операции получения.Значение по умолчанию при отсутствии данного интерфейса — true.</summary>
      <returns>Значение true, если данная операция получения члена должна вызывать свойства при выполнении операции получения; в противном случае — значение false.</returns>
    </member>
    <member name="T:System.Dynamic.InvokeBinder">
      <summary>Представляет динамическую операцию вызова в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.InvokeBinder" />.</summary>
      <param name="callInfo">Сигнатура аргументов в источнике вызова.</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции вызова.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции вызова.</param>
      <param name="args">Массив аргументов динамической операции вызова.</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.CallInfo">
      <summary>Получает сигнатуру аргументов в источнике вызова.</summary>
      <returns>Сигнатура аргументов в источнике вызова.</returns>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции вызова, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции вызова.</param>
      <param name="args">Аргументы динамической операции вызова.</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции вызова, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции вызова.</param>
      <param name="args">Аргументы динамической операции вызова.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.InvokeMemberBinder">
      <summary>Представляет динамическую операцию вызова члена в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.#ctor(System.String,System.Boolean,System.Dynamic.CallInfo)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.InvokeMemberBinder" />.</summary>
      <param name="name">Имя элемента, который предполагается вызвать.</param>
      <param name="ignoreCase">Значение true, если имя должно сравниваться без учета регистра; в противном случае — значение false.</param>
      <param name="callInfo">Сигнатура аргументов в источнике вызова.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции вызова члена.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции вызова члена.</param>
      <param name="args">Массив аргументов динамической операции вызова члена.</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.CallInfo">
      <summary>Получает сигнатуру аргументов в источнике вызова.</summary>
      <returns>Сигнатура аргументов в источнике вызова.</returns>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции вызова, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции вызова.</param>
      <param name="args">Аргументы динамической операции вызова.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции вызова члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции вызова члена.</param>
      <param name="args">Аргументы динамической операции вызова члена.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции вызова члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции вызова члена.</param>
      <param name="args">Аргументы динамической операции вызова члена.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.IgnoreCase">
      <summary>Получает значение, указывающее, следует ли игнорировать регистр имен членов при сравнении строк.</summary>
      <returns>Значение true, если регистр знаков не учитывается; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.Name">
      <summary>Получает имя члена, который требуется вызвать.</summary>
      <returns>Имя элемента, который предполагается вызвать.</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.SetIndexBinder">
      <summary>Представляет динамическую операцию задания индекса в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.SetIndexBinder" />.</summary>
      <param name="callInfo">Сигнатура аргументов в источнике вызова.</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции задания индекса.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции задания индекса.</param>
      <param name="args">Массив аргументов динамической операции задания индекса.</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.CallInfo">
      <summary>Получает сигнатуру аргументов в источнике вызова.</summary>
      <returns>Сигнатура аргументов в источнике вызова.</returns>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции задания индекса, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции задания индекса.</param>
      <param name="indexes">Аргументы динамической операции задания индекса.</param>
      <param name="value">Значение, задаваемое для коллекции.</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>При переопределении в производном классе выполняет привязку динамической операции задания индекса, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции задания индекса.</param>
      <param name="indexes">Аргументы динамической операции задания индекса.</param>
      <param name="value">Значение, задаваемое для коллекции.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.SetMemberBinder">
      <summary>Представляет динамическую операцию задания члена в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>Инициализирует новый экземпляр <see cref="T:System.Dynamic.SetMemberBinder" />.</summary>
      <param name="name">Имя члена, который необходимо получить.</param>
      <param name="ignoreCase">Значение true, если имя должно сравниваться без учета регистра; в противном случае — значение false.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической операции задания члена.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции задания члена.</param>
      <param name="args">Массив аргументов динамической операции задания члена.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции задания члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции задания члена.</param>
      <param name="value">Значение, задаваемое для члена.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку динамической операции задания члена, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции задания члена.</param>
      <param name="value">Значение, задаваемое для члена.</param>
      <param name="errorSuggestion">Результат привязки, который необходимо использовать в случае ее неудачного завершения, или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.IgnoreCase">
      <summary>Получает значение, указывающее, следует ли игнорировать регистр имен членов при сравнении строк.</summary>
      <returns>Значение true, если регистр знаков не учитывается; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.Name">
      <summary>Получает имя члена, который требуется получить.</summary>
      <returns>Имя члена, который необходимо получить.</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Dynamic.UnaryOperationBinder">
      <summary>Представляет унарную динамическую операцию в источнике вызова с указанием семантики привязки и сведений об операции.</summary>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Dynamic.BinaryOperationBinder" />.</summary>
      <param name="operation">Вид унарной операции.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Выполняет привязку динамической унарной операции.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической операции.</param>
      <param name="args">Массив аргументов динамической операции.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку унарной динамической операции, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической унарной операции.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Выполняет привязку унарной динамической операции, если не удается привязать динамический целевой объект.</summary>
      <returns>Объект <see cref="T:System.Dynamic.DynamicMetaObject" />, представляющий результат привязки.</returns>
      <param name="target">Целевой объект динамической унарной операции.</param>
      <param name="errorSuggestion">Результат привязки в случае ее неудачного завершения или значение NULL.</param>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.Operation">
      <summary>Вид унарной операции.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.ExpressionType" />, который представляет вид унарной операции.</returns>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.ReturnType">
      <summary>Тип результата операции.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип результата операции.</returns>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpression">
      <summary>Представляет динамическую операцию.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>Отправляет в конкретный метод Visit для данного типа узла.Например, объект <see cref="T:System.Linq.Expressions.MethodCallExpression" /> вызывает метод <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />.</summary>
      <returns>Результат посещения этого узла.</returns>
      <param name="visitor">Посетитель, с помощью которого выполняется посещение этого узла.</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Arguments">
      <summary>Получает аргументы для динамической операции.</summary>
      <returns>Доступная только для чтения коллекция, содержащая аргументы динамической операции.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Binder">
      <summary>Получает объект <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />, который определят поведение динамического сайта во время выполнения.</summary>
      <returns>
        <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />, который определят поведение динамического сайта во время выполнения.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.DelegateType">
      <summary>Получает тип делегата, используемого объектом <see cref="T:System.Runtime.CompilerServices.CallSite" />.</summary>
      <returns>Объект <see cref="T:System.Type" />, представляющий тип делегата, используемого объектом <see cref="T:System.Runtime.CompilerServices.CallSite" />.</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" />, у которого свойство <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> равняется <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, а для свойств <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> заданы указанные значения.</returns>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="returnType">Тип результата динамического выражения.</param>
      <param name="arguments">Аргументы динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression)">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" />, у которого свойство <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> равняется <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, а для свойств <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> заданы указанные значения.</returns>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="returnType">Тип результата динамического выражения.</param>
      <param name="arg0">Первый аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" />, у которого свойство <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> равняется <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, а для свойств <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> заданы указанные значения.</returns>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="returnType">Тип результата динамического выражения.</param>
      <param name="arg0">Первый аргумент динамической операции.</param>
      <param name="arg1">Второй аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" />, у которого свойство <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> равняется <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, а для свойств <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> заданы указанные значения.</returns>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="returnType">Тип результата динамического выражения.</param>
      <param name="arg0">Первый аргумент динамической операции.</param>
      <param name="arg1">Второй аргумент динамической операции.</param>
      <param name="arg2">Третий аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" />, у которого свойство <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> равняется <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, а для свойств <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> заданы указанные значения.</returns>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="returnType">Тип результата динамического выражения.</param>
      <param name="arg0">Первый аргумент динамической операции.</param>
      <param name="arg1">Второй аргумент динамической операции.</param>
      <param name="arg2">Третий аргумент динамической операции.</param>
      <param name="arg3">Четвертый аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression[])">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" />, у которого свойство <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> равняется <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, а для свойств <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> заданы указанные значения.</returns>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="returnType">Тип результата динамического выражения.</param>
      <param name="arguments">Аргументы динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" /> со свойством <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />, равным <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, и свойствами <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />, для которых заданы указанные значения.</returns>
      <param name="delegateType">Тип делегата, используемого <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="arguments">Аргументы динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression)">
      <summary>Создает объект <see cref="T:System.Linq.Expressions.DynamicExpression" />, представляющий динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> и одним аргументом.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" /> со свойством <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />, равным <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, и свойствами <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />, для которых заданы указанные значения.</returns>
      <param name="delegateType">Тип делегата, используемого <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="arg0">Аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> и двумя аргументами.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" /> со свойством <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />, равным <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, и свойствами <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />, для которых заданы указанные значения.</returns>
      <param name="delegateType">Тип делегата, используемого <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="arg0">Первый аргумент динамической операции.</param>
      <param name="arg1">Второй аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> и тремя аргументами.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" /> со свойством <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />, равным <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, и свойствами <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />, для которых заданы указанные значения.</returns>
      <param name="delegateType">Тип делегата, используемого <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="arg0">Первый аргумент динамической операции.</param>
      <param name="arg1">Второй аргумент динамической операции.</param>
      <param name="arg2">Третий аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> и четырьмя аргументами.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" /> со свойством <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />, равным <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, и свойствами <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />, для которых заданы указанные значения.</returns>
      <param name="delegateType">Тип делегата, используемого <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="arg0">Первый аргумент динамической операции.</param>
      <param name="arg1">Второй аргумент динамической операции.</param>
      <param name="arg2">Третий аргумент динамической операции.</param>
      <param name="arg3">Четвертый аргумент динамической операции.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression[])">
      <summary>Создает выражение <see cref="T:System.Linq.Expressions.DynamicExpression" />, которое представляет динамическую операцию, привязанную с использованием указанного объекта <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.DynamicExpression" /> со свойством <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />, равным <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />, и свойствами <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> и <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />, для которых заданы указанные значения.</returns>
      <param name="delegateType">Тип делегата, используемого <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Связыватель времени выполнения для динамической операции.</param>
      <param name="arguments">Аргументы динамической операции.</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.NodeType">
      <summary>Возвращает тип узла данного выражения.При переопределении этого метода узлы расширения должны возвращать <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.ExpressionType" /> выражения.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#CreateCallSite"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#Rewrite(System.Linq.Expressions.Expression[])"></member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Type">
      <summary>Получает статичный тип выражения, представленного этим выражением <see cref="T:System.Linq.Expressions.Expression" />.</summary>
      <returns>Тип <see cref="P:System.Linq.Expressions.DynamicExpression.Type" />, представляющий этот статичный тип выражения.</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>Сравнивает значение, отправленное в параметр, arguments, со свойством Arguments текущего экземпляра DynamicExpression.Если значения параметров и свойства равны, возвращается текущий экземпляр.Если они не равны, возвращается новый экземпляр DynamicExpression, идентичный текущему экземпляру за исключением того, что свойству Arguments присвоено значение параметра arguments.</summary>
      <returns>Нужно использовать это выражение, если дочерние элементы не изменились, либо выражение с обновленными дочерними элементами.</returns>
      <param name="arguments">Свойство <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> результата.</param>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpressionVisitor">
      <summary>Представляет метод просмотра или перезаписи деревьев динамических выражений.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Linq.Expressions.DynamicExpressionVisitor" />.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.VisitDynamic(System.Linq.Expressions.DynamicExpression)">
      <summary>Просматривает дочерний элемент выражения <see cref="T:System.Linq.Expressions.DynamicExpression" />.</summary>
      <returns>Возвращает <see cref="T:System.Linq.Expressions.Expression" />, измененное выражение в случае изменения самого выражения или любого его подвыражения; в противном случае возвращается исходное выражение.</returns>
      <param name="node">Выражение, которое необходимо просмотреть.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite">
      <summary>Базовый класс динамического источника вызова.Этот тип используется в качестве типа параметра для целей динамического источника вызова.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite.Binder">
      <summary>Класс, предназначенный для привязки динамических операций к динамическому сайту.</summary>
      <returns>Объект <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />, предназначенный для привязки динамических операций.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite.Create(System.Type,System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>Создает источник вызова с данными типом делегата и построителем.</summary>
      <returns>Новый источник вызова.</returns>
      <param name="delegateType">Тип делегата источника вызова.</param>
      <param name="binder">Привязчик источника вызова.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite`1">
      <summary>Тип динамического сайта.</summary>
      <typeparam name="T">Тип делегата.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite`1.Create(System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>Создает экземпляр динамического источника вызова, инициализированного с помощью привязчика, ответственного за привязку среды выполнения динамических операций этого источника вызова.</summary>
      <returns>Новый экземпляр динамического источника вызова.</returns>
      <param name="binder">Привязчик, предназначенный для привязки среды выполнения динамических операций этого источника вызова.</param>
    </member>
    <member name="F:System.Runtime.CompilerServices.CallSite`1.Target">
      <summary>Кэш уровня 0 — специальный делегат на основе журнала сайта.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite`1.Update">
      <summary>Делегат обновления.Вызывается при отсутствии на динамическом сайте совпадений в кэше.</summary>
      <returns>Делегат обновления.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteBinder">
      <summary>Класс, предназначенный для привязки динамических операций к динамическому источнику вызова в среде выполнения.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>Выполняет привязку динамической операции к набору аргументов в среде выполнения.</summary>
      <returns>Выражение, проверяющее аргументы динамической операции и выполняющее эту операцию, если проверки пройдены успешно.Если проверки не пройдены при последующих выполнениях динамической операции, метод Bind вызывается еще раз, чтобы создать новый объект <see cref="T:System.Linq.Expressions.Expression" /> для новых типов аргументов.</returns>
      <param name="args">Массив аргументов динамической операции.</param>
      <param name="parameters">Массив экземпляров класса <see cref="T:System.Linq.Expressions.ParameterExpression" />, представляющих параметры источника вызова в процессе привязки.</param>
      <param name="returnLabel">Элемент LabelTarget, возвращающий результат динамической привязки.</param>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.BindDelegate``1(System.Runtime.CompilerServices.CallSite{``0},System.Object[])">
      <summary>Обеспечивает низкоуровневую поддержку привязки в среде выполнения.Этот метод можно переопределять в классах, предоставляя прямой делегат для реализации правила.Это позволяет сохранять правила на диск, обеспечивать специализированные правила в среде выполнения и предоставлять альтернативную политику кэширования.</summary>
      <returns>Новый делегат, заменяющий целевой элемент источника вызова.</returns>
      <param name="site">Источник вызова, для которого выполняется привязка.</param>
      <param name="args">Аргументы для привязки.</param>
      <typeparam name="T">Тип целевого элемента источника вызова.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.CacheTarget``1(``0)">
      <summary>Добавляет целевой элемент в кэш известных целевых элементов.Целевые элементы в кэше сканируются перед вызовом метода BindDelegate для получения нового правила.</summary>
      <param name="target">Целевой делегат, добавляемый в кэш.</param>
      <typeparam name="T">Тип добавляемого целевого элемента.</typeparam>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSiteBinder.UpdateLabel">
      <summary>Получает метку, с помощью которой можно обеспечить обновление привязки.Она указывает, что привязка выражения более недействительна.Обычно используется в случае изменения "версии" динамического объекта.</summary>
      <returns>Объект <see cref="T:System.Linq.Expressions.LabelTarget" />, представляющий метку, с помощью которой можно обеспечить обновление привязки.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteHelpers">
      <summary>Класс, содержащий вспомогательные методы для источников вызова динамической среды выполнения (DLR).</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteHelpers.IsInternalFrame(System.Reflection.MethodBase)">
      <summary>Проверяет, используется ли объект <see cref="T:System.Reflection.MethodBase" /> в качестве внутреннего объекта среды DLR (в этом случае он не должен отображаться в стеке кода языка).</summary>
      <returns>Значение true, если вводимый объект <see cref="T:System.Reflection.MethodBase" /> используется в качестве внутреннего объекта среды DLR (в этом случае он не должен отображаться в стеке кода языка).В противном случае — значение false.</returns>
      <param name="mb">Вводимый объект <see cref="T:System.Reflection.MethodBase" /></param>
    </member>
    <member name="T:System.Runtime.CompilerServices.DynamicAttribute">
      <summary>Означает, что использование объекта <see cref="T:System.Object" /> с элементом следует рассматривать как динамически выполняемый тип.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" />.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor(System.Boolean[])">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" />.</summary>
      <param name="transformFlags">Указывает с помощью префикса обхода конструкции типа, какие экземпляры <see cref="T:System.Object" /> следует считать динамически выполняемыми типами.</param>
    </member>
    <member name="P:System.Runtime.CompilerServices.DynamicAttribute.TransformFlags">
      <summary>Указывает с помощью префикса обхода конструкции типа, какие экземпляры <see cref="T:System.Object" /> следует считать динамически выполняемыми типами.</summary>
      <returns>Список объектов <see cref="T:System.Object" />, которые следует рассматривать как динамически выполняемые типы.</returns>
    </member>
  </members>
</doc>