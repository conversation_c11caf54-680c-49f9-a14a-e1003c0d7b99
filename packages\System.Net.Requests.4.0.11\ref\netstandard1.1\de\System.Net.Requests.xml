﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Requests</name>
  </assembly>
  <members>
    <member name="T:System.Net.HttpWebRequest">
      <summary>Stellt eine HTTP-spezifische Implementierung der <see cref="T:System.Net.WebRequest" />-Klasse bereit.</summary>
    </member>
    <member name="M:System.Net.HttpWebRequest.Abort">
      <summary>Bricht eine Anforderung an eine Internetressource ab.</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Accept">
      <summary>Ruft den Wert des Accept-HTTP-Headers ab oder legt ihn fest.</summary>
      <returns>Der Wert des Accept-HTTP-Headers.Der Standardwert ist null.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.AllowReadStreamBuffering">
      <summary>Ruft einen Wert ab, der angibt, ob die von der Internetressource empfangenen Daten gepuffert werden sollen, oder legt diesen Wert fest.</summary>
      <returns>true, um die aus der Internetressource empfangenen Daten zwischenzuspeichern, andernfalls false.true aktiviert die Zwischenspeicherung der aus der Internetressource empfangenen Daten, false deaktiviert die Zwischenspeicherung.Die Standardeinstellung ist true.</returns>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>Startet eine asynchrone Anforderung eines <see cref="T:System.IO.Stream" />-Objekts, das zum Schreiben von Daten verwendet werden soll.</summary>
      <returns>Ein <see cref="T:System.IAsyncResult" />, das auf die asynchrone Anforderung verweist.</returns>
      <param name="callback">Der <see cref="T:System.AsyncCallback" />-Delegat. </param>
      <param name="state">Das Zustandsobjekt für diese Anforderung. </param>
      <exception cref="T:System.Net.ProtocolViolationException">Die <see cref="P:System.Net.HttpWebRequest.Method" />-Eigenschaft ist GET oder HEAD.- oder -  <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> ist true, <see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> ist false, <see cref="P:System.Net.HttpWebRequest.ContentLength" /> ist -1, <see cref="P:System.Net.HttpWebRequest.SendChunked" /> ist false, und <see cref="P:System.Net.HttpWebRequest.Method" /> ist POST oder PUT. </exception>
      <exception cref="T:System.InvalidOperationException">Der Stream wird von einem vorherigen Aufruf von <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> verwendet.- oder -  <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> ist auf einen Wert festgelegt, und <see cref="P:System.Net.HttpWebRequest.SendChunked" /> ist false.- oder -  Es sind nur noch wenige Threads im Threadpool verfügbar. </exception>
      <exception cref="T:System.NotSupportedException">Die Cachebestätigung der Anforderung hat angegeben, dass die Antwort für diese Anforderung vom Cache bereitgestellt werden kann. Anforderungen, die Daten schreiben, dürfen jedoch den Cache nicht verwenden.Diese Ausnahme kann auftreten, wenn Sie eine benutzerdefinierte Cachebestätigung verwenden, die nicht ordnungsgemäß implementiert wurde.</exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> wurde bereits zuvor aufgerufen. </exception>
      <exception cref="T:System.ObjectDisposedException">In einer .NET Compact Framework-Anwendung wurde ein Anforderungsstream, dessen Inhalt die Länge 0 (null) hat, nicht korrekt abgerufen und geschlossen.Weitere Informationen über das Behandeln von Anforderungen mit einem Inhalt von der Länge 0 (null) finden Sie unter Network Programming in the .NET Compact Framework.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>Startet eine asynchrone Anforderung an eine Internetressource.</summary>
      <returns>Ein <see cref="T:System.IAsyncResult" />, das auf die asynchrone Anforderung einer Antwort verweist.</returns>
      <param name="callback">Der <see cref="T:System.AsyncCallback" />-Delegat. </param>
      <param name="state">Das Zustandsobjekt für diese Anforderung. </param>
      <exception cref="T:System.InvalidOperationException">Der Stream wird bereits von einem vorherigen Aufruf von <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> verwendet.- oder -  <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> ist auf einen Wert festgelegt, und <see cref="P:System.Net.HttpWebRequest.SendChunked" /> ist false.- oder -  Es sind nur noch wenige Threads im Threadpool verfügbar. </exception>
      <exception cref="T:System.Net.ProtocolViolationException">
        <see cref="P:System.Net.HttpWebRequest.Method" /> ist GET oder HEAD, und entweder ist <see cref="P:System.Net.HttpWebRequest.ContentLength" /> größer als 0, oder <see cref="P:System.Net.HttpWebRequest.SendChunked" /> ist true.- oder -  <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> ist true, <see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> ist false, <see cref="P:System.Net.HttpWebRequest.ContentLength" /> ist -1, <see cref="P:System.Net.HttpWebRequest.SendChunked" /> ist false, und <see cref="P:System.Net.HttpWebRequest.Method" /> ist POST oder PUT.- oder -  Der <see cref="T:System.Net.HttpWebRequest" /> hat einen Entitätstext, aber die <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />-Methode wird aufgerufen, ohne die <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />-Methode aufzurufen. - oder -  <see cref="P:System.Net.HttpWebRequest.ContentLength" /> ist größer als 0 (null), aber die Anwendung schreibt nicht alle versprochenen Daten.</exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> wurde bereits zuvor aufgerufen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContentType">
      <summary>Ruft den Wert des Content-type-HTTP-Headers ab oder legt ihn fest.</summary>
      <returns>Der Wert des Content-type-HTTP-Headers.Der Standardwert ist null.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContinueTimeout">
      <summary>Ruft eine Timeout-Zeit (in Millisekunden) ab oder legt diese fest, bis zu der auf den Serverstatus gewartet wird, nachdem "100-Continue" vom Server empfangen wurde. </summary>
      <returns>Das Timeout in Millisekunden, bis zu dem auf den Empfang von "100-Continue" gewartet wird. </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.CookieContainer">
      <summary>Ruft die der Anforderung zugeordneten Cookies ab oder legt diese fest.</summary>
      <returns>Ein <see cref="T:System.Net.CookieContainer" /> mit den dieser Anforderung zugeordneten Cookies.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Credentials">
      <summary>Ruft Authentifizierungsinformationen für die Anforderung ab oder legt diese fest.</summary>
      <returns>Ein <see cref="T:System.Net.ICredentials" />-Element mit den der Anforderung zugeordneten Anmeldeinformationen für die Authentifizierung.Die Standardeinstellung ist null.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>Beendet eine asynchrone Anforderung eines <see cref="T:System.IO.Stream" />-Objekts, das zum Schreiben von Daten verwendet werden soll.</summary>
      <returns>Ein <see cref="T:System.IO.Stream" />, der zum Schreiben von Anforderungsdaten verwendet werden soll.</returns>
      <param name="asyncResult">Die ausstehende Anforderung für einen Datenstrom. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> ist null. </exception>
      <exception cref="T:System.IO.IOException">Die Anforderung wurde nicht abgeschlossen, und es ist kein Stream verfügbar. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> wurde nicht durch die derzeitige Instanz von einem Aufruf von <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> zurückgegeben. </exception>
      <exception cref="T:System.InvalidOperationException">Diese Methode wurde zuvor unter Verwendung von <paramref name="asyncResult" /> aufgerufen. </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> wurde bereits zuvor aufgerufen.- oder -  Fehler bei der Verarbeitung der Anforderung. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>Beendet eine asynchrone Anforderung an eine Internetressource.</summary>
      <returns>Eine <see cref="T:System.Net.WebResponse" /> mit der Antwort von der Internetressource.</returns>
      <param name="asyncResult">Die ausstehende Anforderung einer Antwort. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> ist null. </exception>
      <exception cref="T:System.InvalidOperationException">Diese Methode wurde zuvor unter Verwendung von <paramref name="asyncResult." /> aufgerufen.- oder -  Die <see cref="P:System.Net.HttpWebRequest.ContentLength" />-Eigenschaft ist größer als 0, die Daten wurden jedoch nicht in den Anforderungsstream geschrieben. </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> wurde bereits zuvor aufgerufen.- oder -  Fehler bei der Verarbeitung der Anforderung. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> wurde nicht durch die derzeitige Instanz von einem Aufruf von <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> zurückgegeben. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.HaveResponse">
      <summary>Ruft einen Wert ab, der angibt, ob eine Antwort von einer Internetressource empfangen wurde.</summary>
      <returns>true, wenn eine Antwort empfangen wurde, andernfalls false.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Headers">
      <summary>Gibt eine Auflistung der Name-Wert-Paare an, aus denen sich die HTTP-Header zusammensetzen.</summary>
      <returns>Eine <see cref="T:System.Net.WebHeaderCollection" /> mit den Name-Wert-Paaren, aus denen sich die Header für die HTTP-Anforderung zusammensetzen.</returns>
      <exception cref="T:System.InvalidOperationException">Die Anforderung wurde durch Aufrufen der <see cref="M:System.Net.HttpWebRequest.GetRequestStream" />-Methode, der <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />-Methode, der <see cref="M:System.Net.HttpWebRequest.GetResponse" />-Methode oder der <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />-Methode gestartet. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Method">
      <summary>Ruft die Methode für die Anforderung ab oder legt diese fest.</summary>
      <returns>Die Anforderungsmethode zum Herstellen der Verbindung mit der Internetressource.Der Standardwert ist GET.</returns>
      <exception cref="T:System.ArgumentException">Es wurde keine Methode angegeben.- oder -  Die Zeichenfolge der Methode enthält ungültige Zeichen. </exception>
    </member>
    <member name="P:System.Net.HttpWebRequest.RequestUri">
      <summary>Ruft den ursprünglichen URI (Uniform Resource Identifier) der Anforderung ab.</summary>
      <returns>Ein <see cref="T:System.Uri" /> mit dem URI der Internetressource, der an die <see cref="M:System.Net.WebRequest.Create(System.String)" />-Methode übergeben wurde.</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.SupportsCookieContainer">
      <summary>Ruft einen Wert ab, der angibt, ob die Anforderung Unterstützung für einen <see cref="T:System.Net.CookieContainer" /> bereitstellt.</summary>
      <returns>true, wenn der Vorgang Unterstützung für einen <see cref="T:System.Net.CookieContainer" /> bietet, andernfalls false.true, wenn ein <see cref="T:System.Net.CookieContainer" /> unterstützt wird, andernfalls false. </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.UseDefaultCredentials">
      <summary>Ruft einen <see cref="T:System.Boolean" />-Wert ab, der steuert, ob mit den Anforderungen Standardanmeldeinformationen gesendet werden, oder legt diesen fest.</summary>
      <returns>true, wenn die Standardanmeldeinformationen verwendet werden, andernfalls false.Der Standardwert ist false.</returns>
      <exception cref="T:System.InvalidOperationException">Sie haben versucht, diese Eigenschaft festzulegen, nachdem die Anforderung gesendet wurde.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="USERNAME" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.HttpWebResponse">
      <summary>Stellt eine HTTP-spezifische Implementierung der <see cref="T:System.Net.WebResponse" />-Klasse bereit.</summary>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentLength">
      <summary>Ruft die Länge des von der Anforderung zurückgegebenen Inhalts ab.</summary>
      <returns>Die Anzahl von Bytes, die von der Anforderung zurückgegeben werden.Die Inhaltslänge schließt nicht die Headerinformationen ein.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentType">
      <summary>Ruft den Inhaltstyp der Antwort ab.</summary>
      <returns>Eine Zeichenfolge, die den Inhaltstyp der Antwort enthält.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Cookies">
      <summary>Ruft die dieser Antwort zugeordneten Cookies ab oder legt diese fest.</summary>
      <returns>Eine <see cref="T:System.Net.CookieCollection" /> mit den dieser Antwort zugeordneten Cookies.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="M:System.Net.HttpWebResponse.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.HttpWebResponse" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen. </param>
    </member>
    <member name="M:System.Net.HttpWebResponse.GetResponseStream">
      <summary>Ruft den Stream ab, der zum Lesen des Textkörpers der Serverantwort verwendet wird.</summary>
      <returns>Ein <see cref="T:System.IO.Stream" /> mit dem Antworttext.</returns>
      <exception cref="T:System.Net.ProtocolViolationException">Es ist kein Antwortstream vorhanden. </exception>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebResponse.Headers">
      <summary>Ruft die Header ab, die dieser Antwort vom Server zugeordnet sind.</summary>
      <returns>Eine <see cref="T:System.Net.WebHeaderCollection" /> mit den mit der Antwort zurückgegebenen Headerinformationen.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Method">
      <summary>Ruft die zum Zurückgeben der Antwort verwendete Methode ab.</summary>
      <returns>Eine Zeichenfolge mit der zum Zurückgeben der Antwort verwendeten HTTP-Methode.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ResponseUri">
      <summary>Ruft den URI der Internetressource ab, die die Anforderung beantwortet hat.</summary>
      <returns>Ein <see cref="T:System.Uri" />-Objekt, das den URI der Internetressource enthält, die die Anforderung beantwortet hat.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusCode">
      <summary>Ruft den Status der Antwort ab.</summary>
      <returns>Einer der <see cref="T:System.Net.HttpStatusCode" />-Werte.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusDescription">
      <summary>Ruft die mit der Antwort zurückgegebene Statusbeschreibung ab.</summary>
      <returns>Eine Zeichenfolge, die den Status der Antwort beschreibt.</returns>
      <exception cref="T:System.ObjectDisposedException">Die aktuelle Instanz wurde bereits verworfen. </exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.SupportsHeaders">
      <summary>Ruft einen Wert ab, der angibt, ob Header unterstützt werden.</summary>
      <returns>Gibt <see cref="T:System.Boolean" />zurück.true, wenn Header unterstützt werden, andernfalls false.</returns>
    </member>
    <member name="T:System.Net.IWebRequestCreate">
      <summary>Stellt die Basisschnittstelle zum Erstellen von <see cref="T:System.Net.WebRequest" />-Instanzen bereit.</summary>
    </member>
    <member name="M:System.Net.IWebRequestCreate.Create(System.Uri)">
      <summary>Erstellt eine <see cref="T:System.Net.WebRequest" />-Instanz.</summary>
      <returns>Eine <see cref="T:System.Net.WebRequest" />-Instanz.</returns>
      <param name="uri">Der URI (Uniform Resource Identifier) der Webressource. </param>
      <exception cref="T:System.NotSupportedException">Das in <paramref name="uri" /> angegebene Anforderungsschema wird von dieser <see cref="T:System.Net.IWebRequestCreate" />-Instanz nicht unterstützt. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> ist null. </exception>
      <exception cref="T:System.UriFormatException">Unter .NET for Windows Store apps oder in der Portable Klassenbibliothek verwenden Sie stattdessen die Basisklassenausnahme <see cref="T:System.FormatException" />.Der in <paramref name="uri" /> angegebene URI ist kein gültiger URI. </exception>
    </member>
    <member name="T:System.Net.ProtocolViolationException">
      <summary>Diese Ausnahme wird ausgelöst, wenn beim Verwenden eines Netzwerkprotokolls ein Fehler auftritt.</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.ProtocolViolationException" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.ProtocolViolationException" />-Klasse mit der angegebenen Meldung.</summary>
      <param name="message">Die Zeichenfolge der Fehlermeldung. </param>
    </member>
    <member name="T:System.Net.WebException">
      <summary>Diese Ausnahme wird ausgelöst, wenn während des Netzwerkzugriffes über ein austauschbares Protokoll ein Fehler auftritt.</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.WebException" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.WebException" />-Klasse mit der angegebenen Fehlermeldung.</summary>
      <param name="message">Der Text der Fehlermeldung. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.WebException" />-Klasse mit der angegebenen Fehlermeldung und der angegebenen geschachtelten Ausnahme.</summary>
      <param name="message">Der Text der Fehlermeldung. </param>
      <param name="innerException">Eine geschachtelte Ausnahme. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception,System.Net.WebExceptionStatus,System.Net.WebResponse)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.WebException" />-Klasse mit der angegebenen Fehlermeldung, der geschachtelten Ausnahme, dem Status und der Antwort.</summary>
      <param name="message">Der Text der Fehlermeldung. </param>
      <param name="innerException">Eine geschachtelte Ausnahme. </param>
      <param name="status">Einer der <see cref="T:System.Net.WebExceptionStatus" />-Werte. </param>
      <param name="response">Eine <see cref="T:System.Net.WebResponse" />-Instanz, die die Antwort des Remotehosts enthält. </param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Net.WebExceptionStatus)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.WebException" />-Klasse mit der angegebenen Fehlermeldung und dem angegebenen Status.</summary>
      <param name="message">Der Text der Fehlermeldung. </param>
      <param name="status">Einer der <see cref="T:System.Net.WebExceptionStatus" />-Werte. </param>
    </member>
    <member name="P:System.Net.WebException.Response">
      <summary>Ruft die vom Remotehost zurückgegebene Antwort ab.</summary>
      <returns>Wenn eine Antwort der Internetressource verfügbar ist, eine <see cref="T:System.Net.WebResponse" />-Instanz mit der Fehlerantwort einer Internetressource, andernfalls null.</returns>
    </member>
    <member name="P:System.Net.WebException.Status">
      <summary>Ruft den Status der Antwort ab.</summary>
      <returns>Einer der <see cref="T:System.Net.WebExceptionStatus" />-Werte.</returns>
    </member>
    <member name="T:System.Net.WebExceptionStatus">
      <summary>Definiert Statuscodes für die <see cref="T:System.Net.WebException" />-Klasse.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.ConnectFailure">
      <summary>Auf der Transportebene konnte keine Verbindung mit dem remoten Dienstpunkt hergestellt werden.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.MessageLengthLimitExceeded">
      <summary>Es wurde eine Meldung empfangen, bei der die festgelegte Größe für das Senden einer Anforderung bzw. das Empfangen einer Antwort vom Server überschritten wurde.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Pending">
      <summary>Eine interne asynchrone Anforderung steht aus.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.RequestCanceled">
      <summary>Die Anforderung wurde abgebrochen. Es wurde die <see cref="M:System.Net.WebRequest.Abort" />-Methode aufgerufen, oder ein nicht klassifizierbarer Fehler ist aufgetreten.Dies ist der Standardwert für <see cref="P:System.Net.WebException.Status" />.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.SendFailure">
      <summary>Es konnte keine vollständige Anforderung an den Remoteserver gesendet werden.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Success">
      <summary>Es ist kein Fehler aufgetreten.</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.UnknownError">
      <summary>Eine Ausnahme unbekannten Typs ist aufgetreten.</summary>
    </member>
    <member name="T:System.Net.WebRequest">
      <summary>Sendet eine Anforderung an einen Uniform Resource Identifier (URI).Dies ist eine abstract Klasse.</summary>
    </member>
    <member name="M:System.Net.WebRequest.#ctor">
      <summary>Initialisiert eine neue Instanz der<see cref="T:System.Net.WebRequest" />-Klasse.</summary>
    </member>
    <member name="M:System.Net.WebRequest.Abort">
      <summary>Bricht die Anforderung ab. </summary>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>Stellt beim Überschreiben in einer Nachfolgerklasse eine asynchrone Version der <see cref="M:System.Net.WebRequest.GetRequestStream" />-Methode bereit.</summary>
      <returns>Ein <see cref="T:System.IAsyncResult" />, das auf die asynchrone Anforderung verweist.</returns>
      <param name="callback">Der <see cref="T:System.AsyncCallback" />-Delegat. </param>
      <param name="state">Ein Objekt mit Zustandsinformationen für diese asynchrone Anforderung. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>Startet beim Überschreiben in einer Nachfolgerklasse eine asynchrone Anforderung einer Internetressource.</summary>
      <returns>Ein <see cref="T:System.IAsyncResult" />, das auf die asynchrone Anforderung verweist.</returns>
      <param name="callback">Der <see cref="T:System.AsyncCallback" />-Delegat. </param>
      <param name="state">Ein Objekt mit Zustandsinformationen für diese asynchrone Anforderung. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="P:System.Net.WebRequest.ContentType">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse den Inhaltstyp der zu sendenden Anforderungsdaten ab oder legt diese fest.</summary>
      <returns>Der Inhaltstyp der Anforderungsdaten.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.String)">
      <summary>Initialisiert eine neue <see cref="T:System.Net.WebRequest" />-Instanz für das angegebene URI-Schema.</summary>
      <returns>Ein <see cref="T:System.Net.WebRequest" />-Nachfolger für ein bestimmtes URI-Schema.</returns>
      <param name="requestUriString">Der URI, der die Internetressource bezeichnet. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> has not been registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.Uri)">
      <summary>Initialisiert eine neue <see cref="T:System.Net.WebRequest" />-Instanz für das angegebene URI-Schema.</summary>
      <returns>Ein <see cref="T:System.Net.WebRequest" />-Nachfolger für das angegebene URI-Schema.</returns>
      <param name="requestUri">Ein <see cref="T:System.Uri" /> mit dem URI der angeforderten Ressource. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is not registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.String)">
      <summary>Initialisiert eine neue <see cref="T:System.Net.HttpWebRequest" />-Instanz für die angegebene URI-Zeichenfolge.</summary>
      <returns>Gibt <see cref="T:System.Net.HttpWebRequest" />zurück.Eine <see cref="T:System.Net.HttpWebRequest" />-Instanz für die spezifische URI-Zeichenfolge.</returns>
      <param name="requestUriString">Eine URI-Zeichenfolge, mit der die Internetressource bezeichnet wird. </param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.Uri)">
      <summary>Initialisiert eine neue <see cref="T:System.Net.HttpWebRequest" />-Instanz für den angegebenen URI.</summary>
      <returns>Gibt <see cref="T:System.Net.HttpWebRequest" />zurück.Eine <see cref="T:System.Net.HttpWebRequest" />-Instanz für die spezifische URI-Zeichenfolge.</returns>
      <param name="requestUri">Ein URI, mit dem die Internetressource bezeichnet wird.</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUri" /> is not a valid URI. </exception>
    </member>
    <member name="P:System.Net.WebRequest.Credentials">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse die Netzwerkanmeldeinformationen, die für die Authentifizierung der Anforderung der Internetressource verwendet werden, ab oder legt diese fest.</summary>
      <returns>Ein <see cref="T:System.Net.ICredentials" />-Objekt mit den mit der Anforderung verknüpften Authentifizierungsanmeldeinformationen.Die Standardeinstellung ist null.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.DefaultWebProxy">
      <summary>Ruft den globalen HTTP-Proxy ab oder legt diesen fest.</summary>
      <returns>Ein von jedem Aufruf der Instanzen von <see cref="T:System.Net.WebRequest" /> verwendeter <see cref="T:System.Net.IWebProxy" />.</returns>
    </member>
    <member name="M:System.Net.WebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>Gibt beim Überschreiben in einer Nachfolgerklasse einen <see cref="T:System.IO.Stream" /> zum Schreiben von Daten in die Internetressource zurück.</summary>
      <returns>Ein <see cref="T:System.IO.Stream" />, in den Daten geschrieben werden können.</returns>
      <param name="asyncResult">Ein <see cref="T:System.IAsyncResult" />, das auf eine ausstehende Anforderung eines Streams verweist. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>Gibt beim Überschreiben in einer Nachfolgerklasse eine <see cref="T:System.Net.WebResponse" /> zurück.</summary>
      <returns>Eine <see cref="T:System.Net.WebResponse" /> mit einer Antwort auf die Internetanforderung.</returns>
      <param name="asyncResult">Ein <see cref="T:System.IAsyncResult" />, das auf eine ausstehende Anforderung einer Antwort verweist. </param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.GetRequestStreamAsync">
      <summary>Gibt nach dem Überschreiben in einer abgeleiteten Klasse einen <see cref="T:System.IO.Stream" /> zurück, womit Daten in einem asynchronen Vorgang in die Internetressource geschrieben werden können.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.WebRequest.GetResponseAsync">
      <summary>Gibt beim Überschreiben in einer Nachfolgerklasse in einem asynchronen Vorgang eine Antwort auf eine Internetanforderung zurück.</summary>
      <returns>Gibt <see cref="T:System.Threading.Tasks.Task`1" />zurück.Das Aufgabenobjekt, das den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="P:System.Net.WebRequest.Headers">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse eine Auflistung von Name-Wert-Paaren für Header ab, die mit der Anforderung verknüpft sind, oder legt diese fest.</summary>
      <returns>Eine <see cref="T:System.Net.WebHeaderCollection" /> mit den dieser Anforderung zugeordneten Name-Wert-Paaren für Header.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Method">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse die in dieser Anforderung zu verwendende Protokollmethode ab oder legt diese fest.</summary>
      <returns>Die in dieser Anforderung zu verwendende Protokollmethode.</returns>
      <exception cref="T:System.NotImplementedException">If the property is not overridden in a descendant class, any attempt is made to get or set the property. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Proxy">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse den beim Zugriff auf diese Internetressource verwendeten Netzwerkproxy ab oder legt diesen fest.</summary>
      <returns>Der beim Zugriff auf die Internetressource zu verwendende <see cref="T:System.Net.IWebProxy" />.</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.RegisterPrefix(System.String,System.Net.IWebRequestCreate)">
      <summary>Registriert einen <see cref="T:System.Net.WebRequest" />-Nachfolger für den angegebenen URI.</summary>
      <returns>true, wenn die Registrierung erfolgreich ist, andernfalls false.</returns>
      <param name="prefix">Der vollständige URI oder das URI-Präfix, der bzw. das der<see cref="T:System.Net.WebRequest" /> -Nachfolger bearbeitet. </param>
      <param name="creator">Die Erstellungsmethode, die die <see cref="T:System.Net.WebRequest" /> zum Erstellen des <see cref="T:System.Net.WebRequest" />-Nachfolgers aufruft. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prefix" /> is null-or- <paramref name="creator" /> is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.RequestUri">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse den mit der Anforderung verknüpften URI der Internetressource ab.</summary>
      <returns>Ein <see cref="T:System.Uri" />, der die der Anforderung zugeordnete Ressource darstellt. </returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.UseDefaultCredentials">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse einen <see cref="T:System.Boolean" />-Wert ab, der steuert, ob mit Anforderungen <see cref="P:System.Net.CredentialCache.DefaultCredentials" /> gesendet werden, oder legt einen solchen Wert fest.</summary>
      <returns>true, wenn die Standardanmeldeinformationen verwendet werden, andernfalls false.Der Standardwert ist false.</returns>
      <exception cref="T:System.InvalidOperationException">You attempted to set this property after the request was sent.</exception>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.WebResponse">
      <summary>Stellt eine Antwort eines URIs (Uniform Resource Identifier) bereit.Dies ist eine abstract Klasse.</summary>
    </member>
    <member name="M:System.Net.WebResponse.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.WebResponse" />-Klasse.</summary>
    </member>
    <member name="P:System.Net.WebResponse.ContentLength">
      <summary>Ruft beim Überschreiben in einer Nachfolgerklasse die Inhaltslänge der empfangenen Daten ab oder legt diese fest.</summary>
      <returns>Die Anzahl der von der Internetressource zurückgegebenen Bytes.</returns>
      <exception cref="T:System.NotSupportedException">Es wurde versucht, die Eigenschaft abzurufen oder festzulegen, obwohl die Eigenschaft in einer Nachfolgerklasse nicht überschrieben wurde. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ContentType">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den Inhaltstyp der empfangenen Daten ab oder legt diesen fest.</summary>
      <returns>Eine Zeichenfolge, die den Inhaltstyp der Antwort enthält.</returns>
      <exception cref="T:System.NotSupportedException">Es wurde versucht, die Eigenschaft abzurufen oder festzulegen, obwohl die Eigenschaft in einer Nachfolgerklasse nicht überschrieben wurde. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebResponse.Dispose">
      <summary>Gibt die vom <see cref="T:System.Net.WebResponse" />-Objekt verwendeten nicht verwalteten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Net.WebResponse.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.WebResponse" />-Objekt verwendeten nicht verwalteten Ressourcen und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen. </param>
    </member>
    <member name="M:System.Net.WebResponse.GetResponseStream">
      <summary>Gibt beim Überschreiben in einer Nachfolgerklasse den Datenstream von der Internetressource zurück.</summary>
      <returns>Eine Instanz der <see cref="T:System.IO.Stream" />-Klasse zum Lesen von Daten aus der Internetressource.</returns>
      <exception cref="T:System.NotSupportedException">Es wurde versucht, auf die Methode zuzugreifen, obwohl die Methode in einer Nachfolgerklasse nicht überschrieben wurde. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.Headers">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse eine Auflistung von Name-Wert-Paaren für Header ab, die dieser Anforderung zugeordnet sind.</summary>
      <returns>Eine Instanz der <see cref="T:System.Net.WebHeaderCollection" />-Klasse, die Headerwerte enthält, die dieser Antwort zugeordnet sind.</returns>
      <exception cref="T:System.NotSupportedException">Es wurde versucht, die Eigenschaft abzurufen oder festzulegen, obwohl die Eigenschaft in einer Nachfolgerklasse nicht überschrieben wurde. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ResponseUri">
      <summary>Ruft beim Überschreiben in einer abgeleiteten Klasse den URI der Internetressource ab, die letztlich auf die Anforderung geantwortet hat.</summary>
      <returns>Eine Instanz der <see cref="T:System.Uri" />-Klasse, die den URI der Internetressource enthält, die letztlich auf die Anforderung geantwortet hat.</returns>
      <exception cref="T:System.NotSupportedException">Es wurde versucht, die Eigenschaft abzurufen oder festzulegen, obwohl die Eigenschaft in einer Nachfolgerklasse nicht überschrieben wurde. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.SupportsHeaders">
      <summary>Ruft einen Wert ab, der angibt, ob Header unterstützt werden.</summary>
      <returns>Gibt <see cref="T:System.Boolean" />zurück.true, wenn Header unterstützt werden, andernfalls false.</returns>
    </member>
  </members>
</doc>