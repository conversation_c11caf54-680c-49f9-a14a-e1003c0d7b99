﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Queryable</name>
  </assembly>
  <members>
    <member name="T:System.Linq.EnumerableExecutor">
      <summary>表示一个表达式目录树，并提供在重写该表达式目录树后执行该表达式目录树的功能。</summary>
    </member>
    <member name="M:System.Linq.EnumerableExecutor.#ctor">
      <summary>初始化 <see cref="T:System.Linq.EnumerableExecutor" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Linq.EnumerableExecutor`1">
      <summary>表示一个表达式目录树，并提供在重写该表达式目录树后执行该表达式目录树的功能。</summary>
      <typeparam name="T">执行表达式目录树后所得到的值的数据类型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableExecutor`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>初始化 <see cref="T:System.Linq.EnumerableExecutor`1" /> 类的新实例。</summary>
      <param name="expression">要与新实例关联的表达式目录树。</param>
    </member>
    <member name="T:System.Linq.EnumerableQuery">
      <summary>将 <see cref="T:System.Collections.IEnumerable" /> 表示为 <see cref="T:System.Linq.EnumerableQuery" /> 数据源。</summary>
    </member>
    <member name="M:System.Linq.EnumerableQuery.#ctor">
      <summary>初始化 <see cref="T:System.Linq.EnumerableQuery" /> 类的新实例。</summary>
    </member>
    <member name="T:System.Linq.EnumerableQuery`1">
      <summary>将 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合表示为 <see cref="T:System.Linq.IQueryable`1" /> 数据源。</summary>
      <typeparam name="T">集合中数据的类型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>初始化 <see cref="T:System.Linq.EnumerableQuery`1" /> 类的新实例，并将该实例与 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合关联。</summary>
      <param name="enumerable">要与新实例关联的集合。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>初始化 <see cref="T:System.Linq.EnumerableQuery`1" /> 类的新实例，并将该实例与表达式目录树关联。</summary>
      <param name="expression">要与新实例关联的表达式目录树。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个枚举数，该枚举数可以循环访问关联的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合，如果该集合为空，则循环访问通过将关联的表达式目录树重写为 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 数据源上的查询并执行该查询而得到的集合。</summary>
      <returns>可用来循环访问关联的数据源的枚举数。</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个枚举数，该枚举数可以循环访问关联的 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 集合，如果该集合为空，则循环访问通过将关联的表达式目录树重写为 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 数据源上的查询并执行该查询而得到的集合。</summary>
      <returns>可用来循环访问关联的数据源的枚举数。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#ElementType">
      <summary>获取该实例所表示的集合中的数据的类型。</summary>
      <returns>该实例所表示的集合中的数据的类型。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Expression">
      <summary>获取与该实例关联或者表示该实例的表达式目录树。</summary>
      <returns>与该实例关联或者表示该实例的表达式目录树。</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Provider">
      <summary>获取与该实例关联的查询提供程序。</summary>
      <returns>与该实例关联的查询提供程序。</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>构造一个新的 <see cref="T:System.Linq.EnumerableQuery`1" /> 对象，并将它与表示 <see cref="T:System.Linq.IQueryable`1" /> 数据集合的指定表达式目录树关联。</summary>
      <returns>与 <paramref name="expression" /> 关联的 EnumerableQuery 对象。</returns>
      <param name="expression">要执行的表达式目录树。</param>
      <typeparam name="S">
        <paramref name="expression" /> 所表示的集合中的数据的类型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery(System.Linq.Expressions.Expression)">
      <summary>构造一个新的 <see cref="T:System.Linq.EnumerableQuery`1" /> 对象，并将它与表示 <see cref="T:System.Linq.IQueryable" /> 数据集合的指定表达式目录树关联。</summary>
      <returns>与 <paramref name="expression" /> 关联的 <see cref="T:System.Linq.EnumerableQuery`1" /> 对象。</returns>
      <param name="expression">表示 <see cref="T:System.Linq.IQueryable" /> 数据集合的表达式目录树。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute``1(System.Linq.Expressions.Expression)">
      <summary>在重写表达式后执行表达式，重写的目的是对无法通过 <see cref="T:System.Linq.Queryable" /> 方法查询的任何可枚举数据源调用 <see cref="T:System.Linq.Enumerable" /> 方法，而不是调用 <see cref="T:System.Linq.Queryable" /> 方法。</summary>
      <returns>执行 <paramref name="expression" /> 后所得到的值。</returns>
      <param name="expression">要执行的表达式目录树。</param>
      <typeparam name="S">
        <paramref name="expression" /> 所表示的集合中的数据的类型。</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute(System.Linq.Expressions.Expression)">
      <summary>在重写表达式后执行表达式，重写的目的是对无法通过 <see cref="T:System.Linq.Queryable" /> 方法查询的任何可枚举数据源调用 <see cref="T:System.Linq.Enumerable" /> 方法，而不是调用 <see cref="T:System.Linq.Queryable" /> 方法。</summary>
      <returns>执行 <paramref name="expression" /> 后所得到的值。</returns>
      <param name="expression">要执行的表达式目录树。</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.ToString">
      <summary>返回可枚举集合的文本表示形式；如果该集合为 null，则返回与此实例关联的表达式树的文本表示形式。</summary>
      <returns>可枚举集合的文本表示形式；如果该集合为 null，则为与此实例关联的表达式树的文本表示形式。</returns>
    </member>
    <member name="T:System.Linq.Queryable">
      <summary>提供一组用于查询实现 <see cref="T:System.Linq.IQueryable`1" /> 的数据结构的 static（在 Visual Basic 中为 Shared）方法。</summary>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0,``0}})">
      <summary>对序列应用累加器函数。</summary>
      <returns>累加器的最终值。</returns>
      <param name="source">要聚合的序列。</param>
      <param name="func">要应用于每个元素的累加器函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``2(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}})">
      <summary>对序列应用累加器函数。将指定的种子值用作累加器初始值。</summary>
      <returns>累加器的最终值。</returns>
      <param name="source">要聚合的序列。</param>
      <param name="seed">累加器的初始值。</param>
      <param name="func">要对每个元素调用的累加器函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TAccumulate">累加器值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``3(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,``2}})">
      <summary>对序列应用累加器函数。将指定的种子值用作累加器的初始值，并使用指定的函数选择结果值。</summary>
      <returns>已转换的累加器最终值。</returns>
      <param name="source">要聚合的序列。</param>
      <param name="seed">累加器的初始值。</param>
      <param name="func">要对每个元素调用的累加器函数。</param>
      <param name="selector">将累加器的最终值转换为结果值的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TAccumulate">累加器值的类型。</typeparam>
      <typeparam name="TResult">结果值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="func" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.All``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>确定序列中的所有元素是否都满足条件。</summary>
      <returns>如果源序列中的每个元素都通过指定谓词中的测试，或者序列为空，则为 true；否则为 false。</returns>
      <param name="source">要测试其元素是否满足条件的序列。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0})">
      <summary>确定序列是否包含任何元素。</summary>
      <returns>如果源序列包含任何元素，则为 true；否则为 false。</returns>
      <param name="source">要检查是否为空的序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>确定序列中的任何元素是否都满足条件。</summary>
      <returns>如果源序列中的任何元素都通过指定谓词中的测试，则为 true；否则为 false。</returns>
      <param name="source">要测试其元素是否满足条件的序列。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>将泛型 <see cref="T:System.Collections.Generic.IEnumerable`1" /> 转换为泛型 <see cref="T:System.Linq.IQueryable`1" />。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，表示输入序列。</returns>
      <param name="source">要转换的序列。</param>
      <typeparam name="TElement">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable(System.Collections.IEnumerable)">
      <summary>将 <see cref="T:System.Collections.IEnumerable" /> 转换为 <see cref="T:System.Linq.IQueryable" />。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable" />，表示输入序列。</returns>
      <param name="source">要转换的序列。</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> 未为某些 <paramref name="T" /> 实现 <see cref="T:System.Collections.Generic.IEnumerable`1" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Decimal})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Double})">
      <summary>计算 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int32})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int64})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 Source 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 Source 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 Source 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算平均值的、可以为 null 的 <see cref="T:System.Int32" />  值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 Source 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均值；如果 Source 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算平均值的可以为 null 的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Single})">
      <summary>计算 <see cref="T:System.Single" /> 值序列的平均值。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算平均值的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">用于计算平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>计算 <see cref="T:System.Double" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值；如果 <paramref name="source" /> 序列为空或仅包含 null 值，则为 null。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>计算 <see cref="T:System.Single" /> 值序列的平均值，该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>值序列的平均值。</returns>
      <param name="source">要计算其平均值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 中不包含任何元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Cast``1(System.Linq.IQueryable)">
      <summary>将 <see cref="T:System.Linq.IQueryable" /> 的元素转换为指定的类型。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含被转换为指定类型的源序列中的每个元素。</returns>
      <param name="source">包含要转换的元素的 <see cref="T:System.Linq.IQueryable" />。</param>
      <typeparam name="TResult">
        <paramref name="source" /> 中的元素要转换成的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidCastException">序列中的元素不能强制转换为 <paramref name="TResult" /> 类型。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Concat``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>连接两个序列。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含两个输入序列的连接元素。</returns>
      <param name="source1">要连接的第一个序列。</param>
      <param name="source2">要与第一个序列连接的序列。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0)">
      <summary>通过使用默认的相等比较器确定序列是否包含指定的元素。</summary>
      <returns>如果输入序列包含具有指定值的元素，则为 true；否则为 false。</returns>
      <param name="source">要在其中定位 <paramref name="item" /> 的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="item">要在序列中定位的对象。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 确定序列是否包含指定的元素。</summary>
      <returns>如果输入序列包含具有指定值的元素，则为 true；否则为 false。</returns>
      <param name="source">要在其中定位 <paramref name="item" /> 的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="item">要在序列中定位的对象。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0})">
      <summary>返回序列中的元素数量。</summary>
      <returns>输入序列中的元素数量。</returns>
      <param name="source">包含要计数的元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 中的元素数量大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回指定序列中满足条件的元素数量。</summary>
      <returns>序列中满足谓词函数的条件的元素数量。</returns>
      <param name="source">包含要进行计数的元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">
        <paramref name="source" /> 中的元素数量大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0})">
      <summary>返回指定序列的元素；如果序列为空，则返回单一实例集合中的类型参数的默认值。</summary>
      <returns>用于在 <paramref name="source" /> 为空的情况下包含 default(<paramref name="TSource" />) 的 <see cref="T:System.Linq.IQueryable`1" />；否则为 <paramref name="source" />。</returns>
      <param name="source">用于在序列为空的情况下返回默认值的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0},``0)">
      <summary>返回指定序列中的元素；如果序列为空，则返回单一实例集合中的指定值。</summary>
      <returns>在 <paramref name="source" /> 为空的情况下包含 <paramref name="defaultValue" /> 的 <see cref="T:System.Linq.IQueryable`1" />；否则为 <paramref name="source" />。</returns>
      <param name="source">用于在序列为空的情况下返回指定值的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="defaultValue">序列为空时要返回的值。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0})">
      <summary>通过使用默认的相等比较器对值进行比较返回序列中的非重复元素。</summary>
      <returns>包含 <paramref name="source" /> 中的非重复元素的 <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">要从中删除重复项的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对值进行比较返回序列中的非重复元素。</summary>
      <returns>包含 <paramref name="source" /> 中的非重复元素的 <see cref="T:System.Linq.IQueryable`1" />。</returns>
      <param name="source">要从中删除重复项的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAt``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>返回序列中指定索引处的元素。</summary>
      <returns>
        <paramref name="source" /> 中指定位置处的元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="index">要检索的从零开始的元素索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> 小于零。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAtOrDefault``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>返回序列中指定索引处的元素；如果索引超出范围，则返回默认值。</summary>
      <returns>如果 <paramref name="index" /> 超出 <paramref name="source" /> 的界限，则返回 default(<paramref name="TSource" />)；否则返回 <paramref name="source" /> 中指定位置处的元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="index">要检索的从零开始的元素索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器对值进行比较生成两个序列的差集。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含两个序列的差集。</returns>
      <param name="source1">一个 <see cref="T:System.Linq.IQueryable`1" />，将返回其不在 <paramref name="source2" /> 中的元素。</param>
      <param name="source2">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其也出现在第一个序列中的元素将不会出现在返回的序列中。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对值进行比较产生两个序列的差集。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含两个序列的差集。</returns>
      <param name="source1">一个 <see cref="T:System.Linq.IQueryable`1" />，将返回其不在 <paramref name="source2" /> 中的元素。</param>
      <param name="source2">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其也出现在第一个序列中的元素将不会出现在返回的序列中。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0})">
      <summary>返回序列中的第一个元素。</summary>
      <returns>
        <paramref name="source" /> 中的第一个元素。</returns>
      <param name="source">要返回其第一个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回序列中满足指定条件的第一个元素。</summary>
      <returns>通过 <paramref name="predicate" /> 中测试的 <paramref name="source" /> 中的第一个元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有元素满足 <paramref name="predicate" /> 中的条件。- 或 -源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>返回序列中的第一个元素；如果序列中不包含任何元素，则返回默认值。</summary>
      <returns>如果 <paramref name="source" /> 为空，则返回 default(<paramref name="TSource" />)；否则返回 <paramref name="source" /> 中的第一个元素。</returns>
      <param name="source">要返回其第一个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回序列中满足指定条件的第一个元素，如果未找到这样的元素，则返回默认值。</summary>
      <returns>如果 <paramref name="source" /> 为空或没有元素通过 <paramref name="predicate" /> 指定的测试，则返回 default(<paramref name="TSource" />)，否则返回 <paramref name="source" /> 中通过 <paramref name="predicate" /> 指定的测试的第一个元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组。</summary>
      <returns>在 C# 中为 IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt;，或者在 Visual Basic 中为 IQueryable(Of IGrouping(Of TKey, TSource))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 对象都包含一个对象序列和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并使用指定的比较器对键进行比较。</summary>
      <returns>在 C# 中为 IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt;，或者在 Visual Basic 中为 IQueryable(Of IGrouping(Of TKey, TSource))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 都包含一个对象序列和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且通过使用指定的函数对每个组中的元素进行投影。</summary>
      <returns>在 C# 中为 IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt;，或在 Visual Basic 中为 IQueryable(Of IGrouping(Of TKey, TElement))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 都包含一个 <paramref name="TElement" /> 类型的对象序列和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <typeparam name="TElement">每个 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>对序列中的元素进行分组并且通过使用指定的函数对每组中的元素进行投影。通过使用指定的比较器对键值进行比较。</summary>
      <returns>在 C# 中为 IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt;，或在 Visual Basic 中为 IQueryable(Of IGrouping(Of TKey, TElement))，其中每个 <see cref="T:System.Linq.IGrouping`2" /> 都包含一个 <paramref name="TElement" /> 类型的对象序列和一个键。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <typeparam name="TElement">每个 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。通过使用指定的函数对每个组的元素进行投影。</summary>
      <returns>一个 T:System.Linq.IQueryable`1，它具有 <paramref name="TResult" /> 的类型参数，并且其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <typeparam name="TElement">每个 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。通过使用指定的比较器对键进行比较，并且通过使用指定的函数对每个组的元素进行投影。</summary>
      <returns>一个 T:System.Linq.IQueryable`1，它具有 <paramref name="TResult" /> 的类型参数，并且其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="elementSelector">用于将每个源元素映射到 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <typeparam name="TElement">每个 <see cref="T:System.Linq.IGrouping`2" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="elementSelector" /> 或 <paramref name="resultSelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。</summary>
      <returns>一个 T:System.Linq.IQueryable`1，它具有 <paramref name="TResult" /> 的类型参数，并且其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>根据指定的键选择器函数对序列中的元素进行分组，并且从每个组及其键中创建结果值。通过使用指定的比较器对键进行比较。</summary>
      <returns>一个 T:System.Linq.IQueryable`1，它具有 <paramref name="TResult" /> 的类型参数，并且其中每个元素都表示对一个组及其键的投影。</returns>
      <param name="source">要对其元素进行分组的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="keySelector">用于提取每个元素的键的函数。</param>
      <param name="resultSelector">用于从每个组中创建结果值的函数。</param>
      <param name="comparer">一个用于对键进行比较的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <typeparam name="TResult">
        <paramref name="resultSelector" /> 返回的结果值的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="resultSelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}})">
      <summary>基于键相等对两个序列的元素进行关联并对结果进行分组。使用默认的相等比较器对键进行比较。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含通过对两个序列执行已分组的联接而获得的 <paramref name="TResult" /> 类型的元素。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从第一个序列的元素和第二个序列的匹配元素集合中创建结果元素的函数。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>基于键相等对两个序列的元素进行关联并对结果进行分组。使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对键进行比较。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含通过对两个序列执行已分组的联接而获得的 <paramref name="TResult" /> 类型的元素。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从第一个序列的元素和第二个序列的匹配元素集合中创建结果元素的函数。</param>
      <param name="comparer">用于对键进行哈希处理和比较的比较器。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器对值进行比较生成两个序列的交集。</summary>
      <returns>一个包含两个序列的交集的序列。</returns>
      <param name="source1">一个序列，将返回其也出现在 <paramref name="source2" /> 中的非重复元素。</param>
      <param name="source2">一个序列，将返回其也在第一个序列中出现的非重复元素。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对值进行比较以生成两个序列的交集。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，它包含两个序列的交集。</returns>
      <param name="source1">一个 <see cref="T:System.Linq.IQueryable`1" />，将返回其也出现在 <paramref name="source2" /> 中的非重复元素。</param>
      <param name="source2">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，将返回其也出现在第一个序列中的非重复元素。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}})">
      <summary>基于匹配键对两个序列的元素进行关联。使用默认的相等比较器对键进行比较。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，具有通过对两个序列执行内部联接而获得的 <paramref name="TResult" /> 类型的元素。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从两个匹配元素创建结果元素的函数。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>基于匹配键对两个序列的元素进行关联。使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 对键进行比较。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，具有通过对两个序列执行内部联接而获得的 <paramref name="TResult" /> 类型的元素。</returns>
      <param name="outer">要联接的第一个序列。</param>
      <param name="inner">要与第一个序列联接的序列。</param>
      <param name="outerKeySelector">用于从第一个序列的每个元素提取联接键的函数。</param>
      <param name="innerKeySelector">用于从第二个序列的每个元素提取联接键的函数。</param>
      <param name="resultSelector">用于从两个匹配元素创建结果元素的函数。</param>
      <param name="comparer">一个 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />，用于对键进行哈希处理和比较。</param>
      <typeparam name="TOuter">第一个序列中的元素的类型。</typeparam>
      <typeparam name="TInner">第二个序列中的元素的类型。</typeparam>
      <typeparam name="TKey">键选择器函数返回的键的类型。</typeparam>
      <typeparam name="TResult">结果元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> 或 <paramref name="inner" /> 或 <paramref name="outerKeySelector" /> 或 <paramref name="innerKeySelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0})">
      <summary>返回序列中的最后一个元素。</summary>
      <returns>位于 <paramref name="source" /> 中最后位置处的值。</returns>
      <param name="source">要返回其最后一个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回序列中满足指定条件的最后一个元素。</summary>
      <returns>
        <paramref name="source" /> 中的最后一个元素，它通过了由 <paramref name="predicate" /> 指定的测试。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有元素满足 <paramref name="predicate" /> 中的条件。- 或 -源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>返回序列中的最后一个元素，如果序列中不包含任何元素，则返回默认值。</summary>
      <returns>如果 <paramref name="source" /> 为空，则返回 default(<paramref name="TSource" />)；否则返回 <paramref name="source" /> 中的最后一个元素。</returns>
      <param name="source">要返回其最后一个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回序列中满足条件的最后一个元素；如果未找到这样的元素，则返回默认值。</summary>
      <returns>如果 <paramref name="source" /> 为空或没有元素通过谓词函数中的测试，则返回 default(<paramref name="TSource" />)；否则，返回通过谓词函数中测试的 <paramref name="source" /> 的最后一个元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0})">
      <summary>返回一个 <see cref="T:System.Int64" />，表示序列中的元素的总数量。</summary>
      <returns>
        <paramref name="source" /> 中的元素的数量。</returns>
      <param name="source">包含要进行计数的元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">元素的数量超过 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回一个 <see cref="T:System.Int64" />，它表示序列中满足条件的元素数量。</summary>
      <returns>
        <paramref name="source" /> 中满足谓词函数的条件的元素数量。</returns>
      <param name="source">包含要进行计数的元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">匹配元素的数量超过 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``1(System.Linq.IQueryable{``0})">
      <summary>返回泛型 <see cref="T:System.Linq.IQueryable`1" /> 中的最大值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定最大值的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>对泛型 <see cref="T:System.Linq.IQueryable`1" /> 的每个元素调用投影函数，并返回最大结果值。</summary>
      <returns>序列中的最大值。</returns>
      <param name="source">要确定最大值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="selector" /> 表示的函数返回的值类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``1(System.Linq.IQueryable{``0})">
      <summary>返回泛型 <see cref="T:System.Linq.IQueryable`1" /> 中的最小值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">要确定最小值的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>对泛型 <see cref="T:System.Linq.IQueryable`1" /> 的每个元素调用投影函数，并返回最小结果值。</summary>
      <returns>序列中的最小值。</returns>
      <param name="source">要确定最小值的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="selector" /> 表示的函数返回的值类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OfType``1(System.Linq.IQueryable)">
      <summary>根据指定类型筛选 <see cref="T:System.Linq.IQueryable" /> 的元素。</summary>
      <returns>一个集合，包含 <paramref name="source" /> 中的类型为 <paramref name="TResult" /> 的元素。</returns>
      <param name="source">要对其元素进行筛选的 <see cref="T:System.Linq.IQueryable" />。</param>
      <typeparam name="TResult">筛选序列元素所根据的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>根据键按升序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，根据键对其元素排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按升序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，根据键对其元素排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>根据键按降序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，将根据键按降序对其元素进行排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按降序对序列的元素排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，将根据键按降序对其元素进行排序。</returns>
      <param name="source">一个要排序的值序列。</param>
      <param name="keySelector">用于从元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Reverse``1(System.Linq.IQueryable{``0})">
      <summary>反转序列中元素的顺序。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，其元素以相反顺序对应于输入序列的元素。</returns>
      <param name="source">要反转的值序列。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>将序列中的每个元素投影到新表中。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，其元素为对 <paramref name="source" /> 的每个元素调用投影函数的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="selector" /> 表示的函数返回的值类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,``1}})">
      <summary>通过合并元素的索引将序列的每个元素投影到新表中。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，其元素为对 <paramref name="source" /> 的每个元素调用投影函数的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="selector" /> 表示的函数返回的值类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>将序列的每个元素投影到一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，并对其中的每个元素调用结果选择器函数。每个中间序列的结果值都组合为一个一维序列，并将其返回。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，其元素是通过对 <paramref name="source" /> 的每个元素调用一对多投影函数 <paramref name="collectionSelector" />，然后将这些序列元素的每一个及其对应的 <paramref name="source" /> 元素映射到结果元素中的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="collectionSelector">一个应用于输入序列的每个元素的投影函数。</param>
      <param name="resultSelector">一个应用于每个中间序列的每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TCollection">由 <paramref name="collectionSelector" /> 表示的函数收集的中间元素类型。</typeparam>
      <typeparam name="TResult">结果序列的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="collectionSelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>将序列的每个元素投影到一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，并将结果序列组合为一个序列。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，其元素是对输入序列的每个元素调用一对多投影函数的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="selector" /> 表示的函数返回的序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>将序列中的每个元素投影到一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，它合并了生成它的源元素的索引。对每个中间序列的每个元素调用结果选择器函数，并且将结果值合并为一个一维序列，并将其返回。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，其元素是通过对 <paramref name="source" /> 的每个元素调用一对多投影函数 <paramref name="collectionSelector" />，然后将这些序列元素的每一个及其对应的 <paramref name="source" /> 元素映射到结果元素中的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="collectionSelector">要应用于输入序列的每个元素的投影函数；此函数的第二个参数表示源元素的索引。</param>
      <param name="resultSelector">一个应用于每个中间序列的每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TCollection">由 <paramref name="collectionSelector" /> 表示的函数收集的中间元素类型。</typeparam>
      <typeparam name="TResult">结果序列的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="collectionSelector" /> 或 <paramref name="resultSelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>将序列的每个元素投影到一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，并将结果序列组合为一个序列。每个源元素的索引用于该元素的投影表。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，其元素是对输入序列的每个元素调用一对多投影函数的结果。</returns>
      <param name="source">一个要投影的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数；此函数的第二个参数表示源元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TResult">由 <paramref name="selector" /> 表示的函数返回的序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器比较元素以确定两个序列是否相等。</summary>
      <returns>如果两个源序列的长度相等并且它们的对应元素也相等，则为 true；否则为 false。</returns>
      <param name="source1">一个 <see cref="T:System.Linq.IQueryable`1" />，其元素用于与 <paramref name="source2" /> 中的元素进行比较。</param>
      <param name="source2">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素用于与第一个序列中的元素进行比较。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 比较元素以确定两个序列是否相等。</summary>
      <returns>如果两个源序列的长度相等并且它们的对应元素也相等，则为 true；否则为 false。</returns>
      <param name="source1">一个 <see cref="T:System.Linq.IQueryable`1" />，其元素用于与 <paramref name="source2" /> 中的元素进行比较。</param>
      <param name="source2">一个 <see cref="T:System.Collections.Generic.IEnumerable`1" />，其元素用于与第一个序列中的元素进行比较。</param>
      <param name="comparer">一个用于比较元素的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0})">
      <summary>返回序列的唯一元素；如果该序列并非恰好包含一个元素，则会引发异常。</summary>
      <returns>输入序列的单个元素。</returns>
      <param name="source">要返回其单个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 具有多个元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回序列中满足指定条件的唯一元素；如果有多个这样的元素存在，则会引发异常。</summary>
      <returns>满足 <paramref name="predicate" /> 中条件的输入序列中的单个元素。</returns>
      <param name="source">要从中返回单个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">没有元素满足 <paramref name="predicate" /> 中的条件。- 或 -多个元素满足 <paramref name="predicate" /> 中的条件。- 或 -源序列为空。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>返回序列中的唯一元素；如果该序列为空，则返回默认值；如果该序列包含多个元素，此方法将引发异常。</summary>
      <returns>返回输入序列的单个元素；如果序列不包含任何元素，则返回 default(<paramref name="TSource" />)。</returns>
      <param name="source">要返回其单个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> 具有多个元素。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>返回序列中满足指定条件的唯一元素；如果这类元素不存在，则返回默认值；如果有多个元素满足该条件，此方法将引发异常。</summary>
      <returns>返回满足 <paramref name="predicate" /> 中条件的输入序列的单个元素；如果未找到这样的元素，则返回 default(<paramref name="TSource" />)。</returns>
      <param name="source">要从中返回单个元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">多个元素满足 <paramref name="predicate" /> 中的条件。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Skip``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>跳过序列中指定数量的元素，然后返回剩余的元素。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含输入序列中指定索引后出现的元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="count">返回剩余元素前要跳过的元素数量。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>只要满足指定的条件，就跳过序列中的元素，然后返回剩余元素。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含从未通过 <paramref name="predicate" /> 指定测试的线性系列中的第一个元素开始的 <paramref name="source" /> 中的元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>只要满足指定的条件，就跳过序列中的元素，然后返回剩余元素。将在谓词函数的逻辑中使用元素的索引。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含从未通过 <paramref name="predicate" /> 指定测试的线性系列中的第一个元素开始的 <paramref name="source" /> 中的元素。</returns>
      <param name="source">要从中返回元素的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数；此函数的第二个参数表示源元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Decimal})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Double})">
      <summary>计算 <see cref="T:System.Double" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int32})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int64})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Decimal" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Double" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Int32" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Int64" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">要计算和的可以为 null 的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Single})">
      <summary>计算 <see cref="T:System.Single" /> 值序列之和。</summary>
      <returns>序列值之和。</returns>
      <param name="source">一个要计算和的 <see cref="T:System.Single" /> 值序列。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>计算 <see cref="T:System.Decimal" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>计算 <see cref="T:System.Double" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>计算 <see cref="T:System.Int32" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>计算 <see cref="T:System.Int64" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Decimal" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Decimal.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Double" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int32" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int32.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Int64" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
      <exception cref="T:System.OverflowException">和大于 <see cref="F:System.Int64.MaxValue" />。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>计算可以为 null 的 <see cref="T:System.Single" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>计算 <see cref="T:System.Single" /> 值序列之和，而该序列是通过对输入序列中的每个元素调用投影函数而获得的。</summary>
      <returns>投影值之和。</returns>
      <param name="source">一个 <paramref name="TSource" /> 类型的值序列。</param>
      <param name="selector">要应用于每个元素的投影函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="selector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Take``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>从序列的开头返回指定数量的连续元素。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含从 <paramref name="source" /> 开始处的指定数量的元素。</returns>
      <param name="source">要从其返回元素的序列。</param>
      <param name="count">要返回的元素数量。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>只要满足指定的条件，就会返回序列的元素。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含不再通过由 <paramref name="predicate" /> 指定测试的元素之前出现的输入序列中的元素。</returns>
      <param name="source">要从其返回元素的序列。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>只要满足指定的条件，就会返回序列的元素。将在谓词函数的逻辑中使用元素的索引。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含不再通过由 <paramref name="predicate" /> 指定测试的元素之前出现的输入序列中的元素。</returns>
      <param name="source">要从其返回元素的序列。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数；此函数的第二个参数表示源序列中元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>根据某个键按升序对序列中的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，根据键对其元素排序。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按升序对序列中的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，根据键对其元素排序。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>根据某个键按降序对序列中的元素执行后续排序。</summary>
      <returns>一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，将根据键按降序对其元素进行排序。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 表示的函数返回的键类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>使用指定的比较器按降序对序列中的元素执行后续排序。</summary>
      <returns>一个集合，其中的元素是根据某个键按降序进行排序的。</returns>
      <param name="source">一个 <see cref="T:System.Linq.IOrderedQueryable`1" />，包含要排序的元素。</param>
      <param name="keySelector">用于从每个元素中提取键的函数。</param>
      <param name="comparer">一个用于比较键的 <see cref="T:System.Collections.Generic.IComparer`1" />。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <typeparam name="TKey">由 <paramref name="keySelector" /> 函数返回的键的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="keySelector" /> 或 <paramref name="comparer" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>通过使用默认的相等比较器生成两个序列的并集。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含两个输入序列中的元素（重复元素除外）。</returns>
      <param name="source1">非重复元素组成合并运算的第一组的一个序列。</param>
      <param name="source2">非重复元素组成合并运算的第二组的一个序列。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>通过使用指定的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 生成两个序列的并集。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含两个输入序列中的元素（重复元素除外）。</returns>
      <param name="source1">非重复元素组成合并运算的第一组的一个序列。</param>
      <param name="source2">非重复元素组成合并运算的第二组的一个序列。</param>
      <param name="comparer">用于比较值的 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />。</param>
      <typeparam name="TSource">输入序列中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>基于谓词筛选值序列。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含满足由 <paramref name="predicate" /> 指定的条件的输入序列中的元素。</returns>
      <param name="source">要筛选的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>基于谓词筛选值序列。将在谓词函数的逻辑中使用每个元素的索引。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含满足由 <paramref name="predicate" /> 指定的条件的输入序列中的元素。</returns>
      <param name="source">要筛选的 <see cref="T:System.Linq.IQueryable`1" />。</param>
      <param name="predicate">用于测试每个元素是否满足条件的函数；此函数的第二个参数表示源序列中元素的索引。</param>
      <typeparam name="TSource">
        <paramref name="source" /> 中的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> 或 <paramref name="predicate" /> 为 null。</exception>
    </member>
    <member name="M:System.Linq.Queryable.Zip``3(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>通过使用指定的谓词函数合并两个序列。</summary>
      <returns>一个 <see cref="T:System.Linq.IQueryable`1" />，包含两个输入序列的已合并元素。</returns>
      <param name="source1">要合并的第一个序列。</param>
      <param name="source2">要合并的第二个序列。</param>
      <param name="resultSelector">用于指定如何合并这两个序列的元素的函数。</param>
      <typeparam name="TFirst">第一个输入序列中的元素的类型。</typeparam>
      <typeparam name="TSecond">第二个输入序列中的元素的类型。</typeparam>
      <typeparam name="TResult">结果序列的元素的类型。</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> 或 <paramref name="source2 " /> 为 null。</exception>
    </member>
  </members>
</doc>