﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Requests</name>
  </assembly>
  <members>
    <member name="T:System.Net.HttpWebRequest">
      <summary>
        <see cref="T:System.Net.WebRequest" /> クラスの HTTP 固有の実装を提供します。</summary>
    </member>
    <member name="M:System.Net.HttpWebRequest.Abort">
      <summary>インターネット リソースへの要求を取り消します。</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Accept">
      <summary>Accept HTTP ヘッダーの値を取得または設定します。</summary>
      <returns>Accept HTTP ヘッダーの値。既定値は null です。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.AllowReadStreamBuffering">
      <summary>インターネット リソースから受け取ったデータをバッファリングするかどうかを示す値を取得または設定します。</summary>
      <returns>trueインターネット リソースから受信されたバッファーに格納するにはそれ以外の場合、falseです。インターネット リソースから受信したデータのバッファリングを有効にする場合は true。バッファリングを無効にする場合は false。既定値は、true です。</returns>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>データを書き込むために使用する <see cref="T:System.IO.Stream" /> オブジェクトの非同期要求を開始します。</summary>
      <returns>非同期の要求を参照する <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> デリゲート。</param>
      <param name="state">この要求に対して使用する状態オブジェクト。</param>
      <exception cref="T:System.Net.ProtocolViolationException">
        <see cref="P:System.Net.HttpWebRequest.Method" /> プロパティが GET または HEAD です。または <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> は true で、<see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> は false で、<see cref="P:System.Net.HttpWebRequest.ContentLength" /> は -1 で、<see cref="P:System.Net.HttpWebRequest.SendChunked" /> は false で、<see cref="P:System.Net.HttpWebRequest.Method" /> は POST または PUT です。</exception>
      <exception cref="T:System.InvalidOperationException">ストリームが、<see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> の前回の呼び出しで使用されています。または <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> に値が設定され、<see cref="P:System.Net.HttpWebRequest.SendChunked" /> が false です。またはスレッド プールのスレッドが不足しています。</exception>
      <exception cref="T:System.NotSupportedException">要求のキャッシュ検証コントロールで、この要求の応答がキャッシュから取得できることが示されましたが、データの書き込みを行う要求でキャッシュは使用できません。この例外は、キャッシュ検証コントロールの不適切なカスタム実装を使用した場合に発生することがあります。</exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> は既に呼び出されました。</exception>
      <exception cref="T:System.ObjectDisposedException">.NET Compact Framework アプリケーションで、コンテンツ長が 0 の要求ストリームは取得されず、適切に閉じられました。コンテンツ長が 0 の要求の処理の詳細については、「Network Programming in the .NET Compact Framework」を参照してください。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>インターネット リソースへの非同期要求を開始します。</summary>
      <returns>非同期要求の応答を参照する <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> デリゲート</param>
      <param name="state">この要求に対して使用する状態オブジェクト。</param>
      <exception cref="T:System.InvalidOperationException">ストリームが、既に <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> の前回の呼び出しで使用されています。または <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> に値が設定され、<see cref="P:System.Net.HttpWebRequest.SendChunked" /> が false です。またはスレッド プールのスレッドが不足しています。 </exception>
      <exception cref="T:System.Net.ProtocolViolationException">
        <see cref="P:System.Net.HttpWebRequest.Method" /> が GET または HEAD で、<see cref="P:System.Net.HttpWebRequest.ContentLength" /> が 0 より大きいか、<see cref="P:System.Net.HttpWebRequest.SendChunked" /> が true です。または <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> は true で、<see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> は false です。また、<see cref="P:System.Net.HttpWebRequest.ContentLength" /> は -1、<see cref="P:System.Net.HttpWebRequest.SendChunked" /> は false、<see cref="P:System.Net.HttpWebRequest.Method" /> は POST または PUT です。または<see cref="T:System.Net.HttpWebRequest" /> にはエンティティ本体がありますが、<see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> メソッドを呼び出さずに、<see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> メソッドが呼び出されます。または<see cref="P:System.Net.HttpWebRequest.ContentLength" /> が 0 より大きいが、アプリケーションが約束されたすべてのデータを書き込むようになっていません。</exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> は既に呼び出されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContentType">
      <summary>Content-type HTTP ヘッダーの値を取得または設定します。</summary>
      <returns>Content-type HTTP ヘッダーの値。既定値は null です。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContinueTimeout">
      <summary>100 回の続行まで待機するミリ秒単位のタイムアウト値をサーバーから取得または設定します。</summary>
      <returns>100 回の続行まで待機するミリ秒単位のタイムアウト値。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.CookieContainer">
      <summary>要求に関連付けられているクッキーを取得または設定します。</summary>
      <returns>この要求に関連付けられているクッキーを格納している <see cref="T:System.Net.CookieContainer" />。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Credentials">
      <summary>要求に対して使用する認証情報を取得または設定します。</summary>
      <returns>要求と関連付けられた認証資格情報を格納する <see cref="T:System.Net.ICredentials" />。既定値は、null です。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>データを書き込むために使用する <see cref="T:System.IO.Stream" /> オブジェクトの非同期要求を終了します。</summary>
      <returns>要求データを書き込むために使用する <see cref="T:System.IO.Stream" />。</returns>
      <param name="asyncResult">ストリームの保留中の要求。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> は null です。</exception>
      <exception cref="T:System.IO.IOException">要求が完了しませんでした。また、ストリームは使用できません。 </exception>
      <exception cref="T:System.ArgumentException">現在のインスタンスによって、<see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> への呼び出しから <paramref name="asyncResult" /> が返されませんでした。</exception>
      <exception cref="T:System.InvalidOperationException">このメソッドは、<paramref name="asyncResult" /> を使用して既に呼び出されています。 </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> は既に呼び出されました。または要求の処理中にエラーが発生しました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>インターネット リソースへの非同期要求を終了します。</summary>
      <returns>インターネット リソースからの応答を格納している <see cref="T:System.Net.WebResponse" />。</returns>
      <param name="asyncResult">応答の保留中の要求。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> は null です。</exception>
      <exception cref="T:System.InvalidOperationException">このメソッドは、<paramref name="asyncResult." /> を使用して既に呼び出されています。または<see cref="P:System.Net.HttpWebRequest.ContentLength" /> プロパティが 0 を超えていますが、データが要求ストリームに書き込まれていません。 </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> は既に呼び出されました。または要求の処理中にエラーが発生しました。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by the current instance from a call to <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.HaveResponse">
      <summary>インターネット リソースから応答が受信されたかどうかを示す値を取得します。</summary>
      <returns>応答を受信した場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Headers">
      <summary>HTTP ヘッダーを構成する名前と値のペアのコレクションを指定します。</summary>
      <returns>HTTP 要求のヘッダーを構成する名前と値のペアを格納している <see cref="T:System.Net.WebHeaderCollection" />。</returns>
      <exception cref="T:System.InvalidOperationException">要求が <see cref="M:System.Net.HttpWebRequest.GetRequestStream" />、<see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />、<see cref="M:System.Net.HttpWebRequest.GetResponse" />、または <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> の各メソッドの呼び出しによって開始されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Method">
      <summary>要求に対して使用するメソッドを取得または設定します。</summary>
      <returns>インターネット リソースと通信するために使用する要求メソッド。既定値は GET です。</returns>
      <exception cref="T:System.ArgumentException">メソッドが指定されていません。またはメソッドの文字列に無効な文字が含まれています。</exception>
    </member>
    <member name="P:System.Net.HttpWebRequest.RequestUri">
      <summary>要求の元の URI (Uniform Resource Identifier) を取得します。</summary>
      <returns>
        <see cref="M:System.Net.WebRequest.Create(System.String)" /> メソッドに渡されたインターネット リソースの URI を格納している <see cref="T:System.Uri" />。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.SupportsCookieContainer">
      <summary>要求が <see cref="T:System.Net.CookieContainer" /> をサポートするかどうかを示す値を取得します。</summary>
      <returns>true要求のサポートを提供する場合、<see cref="T:System.Net.CookieContainer" />です。それ以外の場合、falseです。true if a <see cref="T:System.Net.CookieContainer" /> is supported; otherwise, false. </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.UseDefaultCredentials">
      <summary>既定の資格情報が要求と共に送信されるかどうかを制御する <see cref="T:System.Boolean" /> 値を取得または設定します。</summary>
      <returns>既定の資格情報を使用する場合は true。それ以外の場合は false。既定値は false です。</returns>
      <exception cref="T:System.InvalidOperationException">要求が送信された後で、このプロパティを設定しようとしました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="USERNAME" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.HttpWebResponse">
      <summary>
        <see cref="T:System.Net.WebResponse" /> クラスの HTTP 固有の実装を提供します。</summary>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentLength">
      <summary>要求で返されるコンテンツ長を取得します。</summary>
      <returns>要求で返されるバイト数。コンテンツ長には、ヘッダー情報は含まれません。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentType">
      <summary>応答のコンテンツ タイプを取得します。</summary>
      <returns>応答のコンテンツ タイプを格納する文字列。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Cookies">
      <summary>この応答に関連付けられているクッキーを取得または設定します。</summary>
      <returns>この要求に関連付けられているクッキーを格納する <see cref="T:System.Net.CookieCollection" />。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="M:System.Net.HttpWebResponse.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.HttpWebResponse" /> が使用しているアンマネージ リソースを解放します。オプションでマネージ リソースも破棄します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.HttpWebResponse.GetResponseStream">
      <summary>サーバーから応答の本文を読み取るために使用するストリームを取得します。</summary>
      <returns>応答の本文を格納している <see cref="T:System.IO.Stream" />。</returns>
      <exception cref="T:System.Net.ProtocolViolationException">応答ストリームがありません。</exception>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebResponse.Headers">
      <summary>応答に関連付けられているヘッダーをサーバーから取得します。</summary>
      <returns>応答で返されるヘッダー情報を格納する <see cref="T:System.Net.WebHeaderCollection" />。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Method">
      <summary>応答を返すために使用するメソッドを取得します。</summary>
      <returns>応答を返すために使用する HTTP メソッドを格納する文字列。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ResponseUri">
      <summary>要求に応答したインターネット リソースの URI を取得します。</summary>
      <returns>要求に応答したインターネット リソースの URI を格納する <see cref="T:System.Uri" />。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusCode">
      <summary>応答のステータスを取得します。</summary>
      <returns>
        <see cref="T:System.Net.HttpStatusCode" /> 値の 1 つ。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusDescription">
      <summary>応答で返されるステータス記述を取得します。</summary>
      <returns>応答のステータスを記述する文字列。</returns>
      <exception cref="T:System.ObjectDisposedException">現在のインスタンスは破棄されています。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.SupportsHeaders">
      <summary>ヘッダーがサポートされているかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。ヘッダーがサポートされる場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="T:System.Net.IWebRequestCreate">
      <summary>
        <see cref="T:System.Net.WebRequest" /> インスタンスを作成するための基本インターフェイスを提供します。</summary>
    </member>
    <member name="M:System.Net.IWebRequestCreate.Create(System.Uri)">
      <summary>
        <see cref="T:System.Net.WebRequest" /> インスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Net.WebRequest" /> のインスタンス。</returns>
      <param name="uri">Web リソースの URI。</param>
      <exception cref="T:System.NotSupportedException">
        <paramref name="uri" /> で指定された要求スキームは、この <see cref="T:System.Net.IWebRequestCreate" /> インスタンスではサポートされません。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> は null なので、</exception>
      <exception cref="T:System.UriFormatException">Windows ストア アプリのための .NET または汎用性のあるクラス ライブラリで、基本クラスの例外 <see cref="T:System.FormatException" /> を代わりにキャッチします。<paramref name="uri" /> で指定された URI が有効な URI ではありません。</exception>
    </member>
    <member name="T:System.Net.ProtocolViolationException">
      <summary>ネットワーク プロトコルの使用中にエラーが発生した場合にスローされる例外。</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor">
      <summary>
        <see cref="T:System.Net.ProtocolViolationException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor(System.String)">
      <summary>指定したメッセージを使用して、<see cref="T:System.Net.ProtocolViolationException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラー メッセージ文字列。</param>
    </member>
    <member name="T:System.Net.WebException">
      <summary>プラグ可能プロトコルによるネットワークへのアクセスでエラーが発生した場合にスローされる例外。</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor">
      <summary>
        <see cref="T:System.Net.WebException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String)">
      <summary>
        <see cref="T:System.Net.WebException" /> クラスの新しいインスタンスを、指定したエラー メッセージを使用して初期化します。</summary>
      <param name="message">エラー メッセージのテキスト。</param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception)">
      <summary>
        <see cref="T:System.Net.WebException" /> クラスの新しいインスタンスを、指定したエラー メッセージと入れ子になった例外を使用して初期化します。</summary>
      <param name="message">エラー メッセージのテキスト。</param>
      <param name="innerException">入れ子になった例外。</param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception,System.Net.WebExceptionStatus,System.Net.WebResponse)">
      <summary>
        <see cref="T:System.Net.WebException" /> クラスの新しいインスタンスを、指定したエラー メッセージ、入れ子になった例外、ステータス、および応答を使用して初期化します。</summary>
      <param name="message">エラー メッセージのテキスト。</param>
      <param name="innerException">入れ子になった例外。</param>
      <param name="status">
        <see cref="T:System.Net.WebExceptionStatus" /> 値の 1 つ。</param>
      <param name="response">リモート ホストからの応答を格納する <see cref="T:System.Net.WebResponse" /> インスタンス。</param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Net.WebExceptionStatus)">
      <summary>
        <see cref="T:System.Net.WebException" /> クラスの新しいインスタンスを、指定したエラー メッセージとステータスを使用して初期化します。</summary>
      <param name="message">エラー メッセージのテキスト。</param>
      <param name="status">
        <see cref="T:System.Net.WebExceptionStatus" /> 値の 1 つ。</param>
    </member>
    <member name="P:System.Net.WebException.Response">
      <summary>リモート ホストが返す応答を取得します。</summary>
      <returns>インターネット リソースから応答がある場合は、インターネット リソースからのエラー応答を格納した <see cref="T:System.Net.WebResponse" /> インスタンス。それ以外の場合は null。</returns>
    </member>
    <member name="P:System.Net.WebException.Status">
      <summary>応答のステータスを取得します。</summary>
      <returns>
        <see cref="T:System.Net.WebExceptionStatus" /> 値の 1 つ。</returns>
    </member>
    <member name="T:System.Net.WebExceptionStatus">
      <summary>
        <see cref="T:System.Net.WebException" /> クラスのステータス コードを定義します。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.ConnectFailure">
      <summary>トランスポート レベルで、リモート サービス ポイントと通信できませんでした。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.MessageLengthLimitExceeded">
      <summary>サーバーに要求を送信、またはサーバーからの応答を受信しているときに、制限長を超えるメッセージが渡されました。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Pending">
      <summary>内部非同期要求が保留中です。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.RequestCanceled">
      <summary>要求が取り消されたか、<see cref="M:System.Net.WebRequest.Abort" /> メソッドが呼び出されたか、または分類できないエラーが発生しました。これは、<see cref="P:System.Net.WebException.Status" /> の既定値です。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.SendFailure">
      <summary>完全な要求をリモート サーバーに送信できませんでした。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Success">
      <summary>エラーは発生しませんでした。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.UnknownError">
      <summary>未知の種類の例外が発生しました。</summary>
    </member>
    <member name="T:System.Net.WebRequest">
      <summary>Uniform Resource Identifier (URI) に対する要求を実行します。これは abstract クラスです。</summary>
    </member>
    <member name="M:System.Net.WebRequest.#ctor">
      <summary>
        <see cref="T:System.Net.WebRequest" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Net.WebRequest.Abort">
      <summary>要求を中止します。 </summary>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>派生クラスでオーバーライドされると、<see cref="M:System.Net.WebRequest.GetRequestStream" /> メソッドの非同期バージョンを提供します。</summary>
      <returns>非同期の要求を参照する <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> デリゲート。</param>
      <param name="state">非同期要求の状態情報を格納するオブジェクト。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>派生クラスでオーバーライドされると、インターネット リソースの非同期要求を開始します。</summary>
      <returns>非同期の要求を参照する <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> デリゲート。</param>
      <param name="state">非同期要求の状態情報を格納するオブジェクト。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="P:System.Net.WebRequest.ContentType">
      <summary>派生クラスでオーバーライドされると、送信している要求データのコンテンツ タイプを取得または設定します。</summary>
      <returns>要求データのコンテンツ タイプ。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.String)">
      <summary>指定した URI スキーム用に新しい <see cref="T:System.Net.WebRequest" /> のインスタンスを初期化します。</summary>
      <returns>特定の URI スキーム用の <see cref="T:System.Net.WebRequest" /> 派生クラス。</returns>
      <param name="requestUriString">インターネット リソースを識別する URI。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> has not been registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.Uri)">
      <summary>指定した URI スキーム用に新しい <see cref="T:System.Net.WebRequest" /> のインスタンスを初期化します。</summary>
      <returns>指定した URI スキーム用の <see cref="T:System.Net.WebRequest" /> 派生クラス。</returns>
      <param name="requestUri">要求されたリソースの URI を格納する <see cref="T:System.Uri" />。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is not registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.String)">
      <summary>指定した URI 文字列用に新しい <see cref="T:System.Net.HttpWebRequest" /> インスタンスを初期化します。</summary>
      <returns>
        <see cref="T:System.Net.HttpWebRequest" /> を返します。指定した URI 文字列の <see cref="T:System.Net.HttpWebRequest" /> インスタンス。</returns>
      <param name="requestUriString">インターネット リソースを識別する URI 文字列。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.Uri)">
      <summary>指定した URI 用に新しい <see cref="T:System.Net.HttpWebRequest" /> インスタンスを初期化します。</summary>
      <returns>
        <see cref="T:System.Net.HttpWebRequest" /> を返します。指定した URI 文字列の <see cref="T:System.Net.HttpWebRequest" /> インスタンス。</returns>
      <param name="requestUri">インターネット リソースを識別する URI。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUri" /> is not a valid URI. </exception>
    </member>
    <member name="P:System.Net.WebRequest.Credentials">
      <summary>派生クラスでオーバーライドされると、インターネット リソースを使用して要求を認証するために使用されるネットワーク資格情報を取得または設定します。</summary>
      <returns>要求に関連付けられた認証資格情報を格納する <see cref="T:System.Net.ICredentials" />。既定値は、null です。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.DefaultWebProxy">
      <summary>グローバル HTTP プロキシを取得または設定します。</summary>
      <returns>
        <see cref="T:System.Net.WebRequest" /> のインスタンスへのすべての呼び出しで使用される <see cref="T:System.Net.IWebProxy" />。</returns>
    </member>
    <member name="M:System.Net.WebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>派生クラスでオーバーライドされると、インターネット リソースにデータを書き込むための <see cref="T:System.IO.Stream" /> を返します。</summary>
      <returns>データを書き込む <see cref="T:System.IO.Stream" />。</returns>
      <param name="asyncResult">ストリームの保留中の要求を参照する <see cref="T:System.IAsyncResult" />。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>派生クラスでオーバーライドされると、<see cref="T:System.Net.WebResponse" /> を返します。</summary>
      <returns>インターネット要求への応答を格納する <see cref="T:System.Net.WebResponse" />。</returns>
      <param name="asyncResult">応答に対する保留中の要求を参照する <see cref="T:System.IAsyncResult" />。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.GetRequestStreamAsync">
      <summary>派生クラスでオーバーライドされると、インターネット リソースへのデータ書き込みの <see cref="T:System.IO.Stream" /> を非同期操作として返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="M:System.Net.WebRequest.GetResponseAsync">
      <summary>派生クラスでオーバーライドされると、インターネット要求への応答を非同期操作として返します。</summary>
      <returns>
        <see cref="T:System.Threading.Tasks.Task`1" /> を返します。非同期操作を表すタスク オブジェクト。</returns>
    </member>
    <member name="P:System.Net.WebRequest.Headers">
      <summary>派生クラスでオーバーライドされると、要求に関連付けられたヘッダーの名前/値ペアのコレクションを取得または設定します。</summary>
      <returns>要求に関連付けられたヘッダーの名前/値ペアを格納する <see cref="T:System.Net.WebHeaderCollection" />。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Method">
      <summary>派生クラスでオーバーライドされると、要求で使用するプロトコル メソッドを取得または設定します。</summary>
      <returns>要求で使用するプロトコル メソッド。</returns>
      <exception cref="T:System.NotImplementedException">If the property is not overridden in a descendant class, any attempt is made to get or set the property. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Proxy">
      <summary>派生クラスでオーバーライドされると、インターネット リソースにアクセスするために使用するネットワーク プロキシを取得または設定します。</summary>
      <returns>インターネット リソースにアクセスするために使用する <see cref="T:System.Net.IWebProxy" />。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.RegisterPrefix(System.String,System.Net.IWebRequestCreate)">
      <summary>指定した URI 用の <see cref="T:System.Net.WebRequest" /> 派生クラスを登録します。</summary>
      <returns>登録が成功した場合は true。それ以外の場合は false。</returns>
      <param name="prefix">
        <see cref="T:System.Net.WebRequest" /> 派生クラスが処理する完全な URI または URI プレフィックス。</param>
      <param name="creator">
        <see cref="T:System.Net.WebRequest" /> が <see cref="T:System.Net.WebRequest" /> 派生クラスを作成するために呼び出す作成メソッド。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prefix" /> is null-or- <paramref name="creator" /> is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.RequestUri">
      <summary>派生クラスでオーバーライドされると、要求に関連付けられたインターネット リソースの URI を取得します。</summary>
      <returns>要求に関連付けられているリソースを表す <see cref="T:System.Uri" />。 </returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.UseDefaultCredentials">
      <summary>派生クラスでオーバーライドされる場合、<see cref="T:System.Boolean" /> が要求と共に送信されるかどうかを制御する <see cref="P:System.Net.CredentialCache.DefaultCredentials" /> 値を取得または設定します。</summary>
      <returns>既定の資格情報を使用する場合は true。それ以外の場合は false。既定値は false です。</returns>
      <exception cref="T:System.InvalidOperationException">You attempted to set this property after the request was sent.</exception>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.WebResponse">
      <summary>URI (Uniform Resource Identifier) からの応答を利用できるようにします。これは abstract クラスです。</summary>
    </member>
    <member name="M:System.Net.WebResponse.#ctor">
      <summary>
        <see cref="T:System.Net.WebResponse" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Net.WebResponse.ContentLength">
      <summary>派生クラスでオーバーライドされると、受信しているデータのコンテンツ長を取得または設定します。</summary>
      <returns>インターネット リソースから返されるバイト数。</returns>
      <exception cref="T:System.NotSupportedException">プロパティが派生クラスでオーバーライドされていないのに、そのプロパティの取得または設定が試行されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ContentType">
      <summary>派生クラスでオーバーライドされると、受信しているデータのコンテンツ タイプを取得または設定します。</summary>
      <returns>応答のコンテンツ タイプを格納する文字列。</returns>
      <exception cref="T:System.NotSupportedException">プロパティが派生クラスでオーバーライドされていないのに、そのプロパティの取得または設定が試行されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebResponse.Dispose">
      <summary>
        <see cref="T:System.Net.WebResponse" /> オブジェクトによって使用されているアンマネージ リソースを解放します。</summary>
    </member>
    <member name="M:System.Net.WebResponse.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.WebResponse" /> オブジェクトによって使用されているアンマネージ リソースを解放します。オプションとして、マネージ リソースを破棄することもできます。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースの両方を解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="M:System.Net.WebResponse.GetResponseStream">
      <summary>派生クラスでオーバーライドされると、インターネット リソースからデータ ストリームを返します。</summary>
      <returns>インターネット リソースからデータを読み取るための <see cref="T:System.IO.Stream" /> クラスのインスタンス。</returns>
      <exception cref="T:System.NotSupportedException">メソッドが派生クラスでオーバーライドされていないのに、そのメソッドへのアクセスが試行されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.Headers">
      <summary>派生クラスでオーバーライドされると、この要求に関連付けられたヘッダーの名前と値のペアのコレクションを取得します。</summary>
      <returns>この応答に関連付けられているヘッダーの値を格納している <see cref="T:System.Net.WebHeaderCollection" /> クラスのインスタンス。</returns>
      <exception cref="T:System.NotSupportedException">プロパティが派生クラスでオーバーライドされていないのに、そのプロパティの取得または設定が試行されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ResponseUri">
      <summary>派生クラスでオーバーライドされると、要求に実際に応答したインターネット リソースの URI を取得します。</summary>
      <returns>要求に実際に応答したインターネット リソースの URI を格納する <see cref="T:System.Uri" /> クラスのインスタンス。</returns>
      <exception cref="T:System.NotSupportedException">プロパティが派生クラスでオーバーライドされていないのに、そのプロパティの取得または設定が試行されました。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.SupportsHeaders">
      <summary>ヘッダーがサポートされているかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Boolean" /> を返します。ヘッダーがサポートされる場合は true。それ以外の場合は false。</returns>
    </member>
  </members>
</doc>