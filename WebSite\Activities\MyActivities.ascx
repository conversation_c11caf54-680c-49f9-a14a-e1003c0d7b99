<%@ Control CodeBehind="MyActivities.ascx.cs" Language="c#" AutoEventWireup="false" Inherits="SplendidCRM.Activities.MyActivities" %>
<%
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005-2008 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
%>
<div id="divActivitiesMyActivities">
	<asp:Table Width="100%" BorderWidth="0" CellSpacing="0" CellPadding="0" CssClass="h3Row" runat="server">
		<asp:TableRow>
			<asp:TableCell Wrap="false">
				<h3><asp:Image SkinID="h3Arrow" Runat="server" />&nbsp;<%= L10n.Term("Activities.LBL_UPCOMING") %></h3>
			</asp:TableCell>
			<asp:TableCell Wrap="false">
				&nbsp;&nbsp;
				<%= L10n.Term("Activities.LBL_TODAY") %><asp:DropDownList ID="lstTHROUGH" DataValueField="NAME" DataTextField="DISPLAY_NAME" SelectedIndexChanged="Page_Command" AutoPostBack="true" Runat="server" />
				<asp:Label ID="txtTHROUGH" Runat="server" />
			</asp:TableCell>
			<asp:TableCell width="50%"><asp:Image SkinID="blank" runat="server" /></asp:TableCell>
		</asp:TableRow>
	</asp:Table>
	
	<asp:Panel CssClass="button-panel" Visible="<%# !PrintView %>" runat="server">
		<asp:Label ID="lblError" CssClass="error" EnableViewState="false" Runat="server" />
	</asp:Panel>
	
	<SplendidCRM:SplendidGrid id="grdMain" SkinID="grdListView" AllowPaging="<%# !PrintView %>" EnableViewState="true" runat="server">
		<Columns>
			<asp:TemplateColumn HeaderText="" ItemStyle-Width="1%" ItemStyle-HorizontalAlign="Center">
				<ItemTemplate>
					<SplendidCRM:DynamicImage ImageSkinID='<%# DataBinder.Eval(Container.DataItem, "ACTIVITY_TYPE") %>' runat="server" />
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn HeaderText="Activities.LBL_LIST_CLOSE" ItemStyle-Width="1%" ItemStyle-HorizontalAlign="Center">
				<ItemTemplate>
					<asp:HyperLink Visible='<%# SplendidCRM.Security.GetUserAccess(Sql.ToString(DataBinder.Eval(Container.DataItem, "ACTIVITY_TYPE")), "edit", Sql.ToGuid(DataBinder.Eval(Container.DataItem, "ASSIGNED_USER_ID"))) >= 0 %>' NavigateUrl='<%# "~/" + DataBinder.Eval(Container.DataItem, "ACTIVITY_TYPE") + "/edit.aspx?id=" + DataBinder.Eval(Container.DataItem, "ID") + "&Status=Close" %>' Runat="server">
						<asp:Image SkinID="close_inline" AlternateText='<%# L10n.Term("Activities.LBL_LIST_CLOSE") %>' Runat="server" />
					</asp:HyperLink>
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn  HeaderText="Activities.LBL_LIST_DATE" SortExpression="DATE_START" ItemStyle-Width="10%" ItemStyle-Wrap="false">
				<ItemTemplate>
					<font Class="<%# (Sql.ToDateTime(DataBinder.Eval(Container.DataItem, "DATE_START")) < DateTime.Now) ? "overdueTask" : "futureTask" %>"><%# Sql.ToDateString(T10n.FromServerTime(Sql.ToDateTime(DataBinder.Eval(Container.DataItem, "DATE_START")))) %></font>
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn HeaderText="Activities.LBL_ACCEPT_THIS" ItemStyle-Width="10%" ItemStyle-Wrap="false">
				<ItemTemplate>
					<div style="DISPLAY: <%# String.Compare((DataBinder.Eval(Container.DataItem, "ACCEPT_STATUS") as string), "none", true) == 0 ? "inline" : "none" %>">
						<asp:ImageButton Visible='<%# SplendidCRM.Security.GetUserAccess(Sql.ToString(DataBinder.Eval(Container.DataItem, "ACTIVITY_TYPE")), "edit", Sql.ToGuid(DataBinder.Eval(Container.DataItem, "ASSIGNED_USER_ID"))) >= 0 %>' CommandName="Activity.Accept"    CommandArgument='<%# DataBinder.Eval(Container.DataItem, "ID") %>' OnCommand="Page_Command" AlternateText='<%# L10n.Term(".dom_meeting_accept_options.accept"   ) %>' SkinID="edit_inline"    Runat="server" />
						<asp:ImageButton Visible='<%# SplendidCRM.Security.GetUserAccess(Sql.ToString(DataBinder.Eval(Container.DataItem, "ACTIVITY_TYPE")), "edit", Sql.ToGuid(DataBinder.Eval(Container.DataItem, "ASSIGNED_USER_ID"))) >= 0 %>' CommandName="Activity.Tentative" CommandArgument='<%# DataBinder.Eval(Container.DataItem, "ID") %>' OnCommand="Page_Command" AlternateText='<%# L10n.Term(".dom_meeting_accept_options.tentative") %>' SkinID="tentative_inline" Runat="server" />
						<asp:ImageButton Visible='<%# SplendidCRM.Security.GetUserAccess(Sql.ToString(DataBinder.Eval(Container.DataItem, "ACTIVITY_TYPE")), "edit", Sql.ToGuid(DataBinder.Eval(Container.DataItem, "ASSIGNED_USER_ID"))) >= 0 %>' CommandName="Activity.Decline"   CommandArgument='<%# DataBinder.Eval(Container.DataItem, "ID") %>' OnCommand="Page_Command" AlternateText='<%# L10n.Term(".dom_meeting_accept_options.decline"  ) %>' SkinID="decline_inline"   Runat="server" />
					</div>
					<div style="DISPLAY: <%# String.Compare((DataBinder.Eval(Container.DataItem, "ACCEPT_STATUS") as string), "none", true) != 0 ? "inline" : "none" %>">
						<%# L10n.Term(".dom_meeting_accept_status." + DataBinder.Eval(Container.DataItem, "ACCEPT_STATUS")) %>
					</div>
				</ItemTemplate>
			</asp:TemplateColumn>
		</Columns>
	</SplendidCRM:SplendidGrid>

</div>
