﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Dynamic.Runtime</name>
  </assembly>
  <members>
    <member name="T:System.Dynamic.BinaryOperationBinder">
      <summary>表示调用站点上的二元动态运算，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>初始化 <see cref="T:System.Dynamic.BinaryOperationBinder" /> 类的新实例。</summary>
      <param name="operation">二元运算类型。</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态二元运算的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态操作的目标。</param>
      <param name="args">动态操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行二元动态运算的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态二元运算的目标。</param>
      <param name="arg">动态二元运算的右操作数。</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行二元动态运算的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态二元运算的目标。</param>
      <param name="arg">动态二元运算的右操作数。</param>
      <param name="errorSuggestion">绑定失败时的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.Operation">
      <summary>二元运算类型。</summary>
      <returns>表示二元运算的类型的 <see cref="T:System.Linq.Expressions.ExpressionType" /> 对象。</returns>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.BindingRestrictions">
      <summary>表示应用于 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的一组绑定限制，只有满足这些限制的动态绑定才是有效的。</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Combine(System.Collections.Generic.IList{System.Dynamic.DynamicMetaObject})">
      <summary>将 <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例列表中的绑定限制合并到一个限制集中。</summary>
      <returns>新的绑定限制集。</returns>
      <param name="contributingObjects">要合并其中的限制的 <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例列表。</param>
    </member>
    <member name="F:System.Dynamic.BindingRestrictions.Empty">
      <summary>表示一个空的绑定限制集。此字段为只读。</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetExpressionRestriction(System.Linq.Expressions.Expression)">
      <summary>创建用来检查表达式中的任意不可变属性的绑定限制。</summary>
      <returns>新绑定限制。</returns>
      <param name="expression">表示限制的表达式。</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetInstanceRestriction(System.Linq.Expressions.Expression,System.Object)">
      <summary>创建用来检查表达式中的对象实例标识的绑定限制。</summary>
      <returns>新绑定限制。</returns>
      <param name="expression">要测试的表达式。</param>
      <param name="instance">要测试的恰当对象实例。</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetTypeRestriction(System.Linq.Expressions.Expression,System.Type)">
      <summary>创建用来检查表达式中的运行时类型标识的绑定限制。</summary>
      <returns>新绑定限制。</returns>
      <param name="expression">要测试的表达式。</param>
      <param name="type">要测试的恰当类型。</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Merge(System.Dynamic.BindingRestrictions)">
      <summary>将绑定限制集与当前绑定限制合并。</summary>
      <returns>新的绑定限制集。</returns>
      <param name="restrictions">用于与当前绑定限制合并的限制集。</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.ToExpression">
      <summary>创建表示绑定限制的 <see cref="T:System.Linq.Expressions.Expression" />。</summary>
      <returns>表示限制的表达式树。</returns>
    </member>
    <member name="T:System.Dynamic.CallInfo">
      <summary>描述动态绑定进程中的参数。</summary>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.Collections.Generic.IEnumerable{System.String})">
      <summary>创建一个新的 CallInfo，它表示动态绑定进程中的参数。</summary>
      <param name="argCount">参数的数量。</param>
      <param name="argNames">参数名称。</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.String[])">
      <summary>创建一个新的 PositionalArgumentInfo。</summary>
      <param name="argCount">参数的数量。</param>
      <param name="argNames">参数名称。</param>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentCount">
      <summary>参数的数量。</summary>
      <returns>参数的数量。</returns>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentNames">
      <summary>参数名称。</summary>
      <returns>参数名称的只读集合。</returns>
    </member>
    <member name="M:System.Dynamic.CallInfo.Equals(System.Object)">
      <summary>确定是否将指定的 CallInfo 实例视为等于当前实例。</summary>
      <returns>如果指定的实例等于当前实例，则为 true；否则为 false。</returns>
      <param name="obj">要与当前实例进行比较的 <see cref="T:System.Dynamic.CallInfo" /> 实例。</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.GetHashCode">
      <summary>用作当前 <see cref="T:System.Dynamic.CallInfo" /> 的哈希函数。</summary>
      <returns>当前 <see cref="T:System.Dynamic.CallInfo" /> 的哈希代码。</returns>
    </member>
    <member name="T:System.Dynamic.ConvertBinder">
      <summary>表示调用站点上的转换动态操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.#ctor(System.Type,System.Boolean)">
      <summary>初始化 <see cref="T:System.Dynamic.ConvertBinder" /> 的新实例。</summary>
      <param name="type">要转换到的类型。</param>
      <param name="explicit">如果应将转换视为显式转换，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态转换操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态转换操作的目标。</param>
      <param name="args">动态转换操作的参数数组。</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Explicit">
      <summary>获取一个值，该值指示是否应将转换视为显式转换。</summary>
      <returns>如果存在显式转换，则为 True；否则为 false。</returns>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行动态转换操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态转换操作的目标。</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态转换操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态转换操作的目标。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Type">
      <summary>要转换到的类型。</summary>
      <returns>表示要转换到的类型的 <see cref="T:System.Type" /> 对象。</returns>
    </member>
    <member name="T:System.Dynamic.CreateInstanceBinder">
      <summary>表示调用站点上的调用创建操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>初始化 <see cref="T:System.Dynamic.CreateInstanceBinder" /> 的新实例。</summary>
      <param name="callInfo">调用站点上的参数的签名。</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态创建操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态创建操作的目标。</param>
      <param name="args">动态创建操作的参数数组。</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.CallInfo">
      <summary>获取调用站点上的参数的签名。</summary>
      <returns>调用站点上的参数的签名。</returns>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>如果无法绑定目标动态对象，则执行动态创建操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态创建操作的目标。</param>
      <param name="args">动态创建操作的参数。</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态创建操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态创建操作的目标。</param>
      <param name="args">动态创建操作的参数。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.DeleteIndexBinder">
      <summary>表示调用站点上的动态删除索引操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>初始化 <see cref="T:System.Dynamic.DeleteIndexBinder" /> 的新实例。</summary>
      <param name="callInfo">调用站点上的参数的签名。</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态删除索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态删除索引操作的目标。</param>
      <param name="args">动态删除索引操作的参数数组。</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.CallInfo">
      <summary>获取调用站点上的参数的签名。</summary>
      <returns>调用站点上的参数的签名。</returns>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>如果无法绑定目标动态对象，则执行动态删除索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态删除索引操作的目标。</param>
      <param name="indexes">动态删除索引操作的参数。</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态删除索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态删除索引操作的目标。</param>
      <param name="indexes">动态删除索引操作的参数。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.DeleteMemberBinder">
      <summary>表示调用站点上的动态删除成员操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>初始化 <see cref="T:System.Dynamic.DeleteIndexBinder" /> 的新实例。</summary>
      <param name="name">要删除的成员的名称。</param>
      <param name="ignoreCase">如果在匹配名称时应忽略大小写，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态删除成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态删除成员操作的目标。</param>
      <param name="args">动态删除成员操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行动态删除成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态删除成员操作的目标。</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态删除成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态删除成员操作的目标。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.IgnoreCase">
      <summary>获取指示字符串比较是否应忽略成员名称大小写的值。</summary>
      <returns>如果字符串比较应忽略大小写，则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.Name">
      <summary>获取要删除的成员的名称。</summary>
      <returns>要删除的成员的名称。</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObject">
      <summary>表示动态绑定和参与动态绑定的对象的绑定逻辑。</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions)">
      <summary>初始化 <see cref="T:System.Dynamic.DynamicMetaObject" /> 类的新实例。</summary>
      <param name="expression">在动态绑定过程中表示此 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的表达式。</param>
      <param name="restrictions">用于确定绑定是否有效的绑定限制集。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions,System.Object)">
      <summary>初始化 <see cref="T:System.Dynamic.DynamicMetaObject" /> 类的新实例。</summary>
      <param name="expression">在动态绑定过程中表示此 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的表达式。</param>
      <param name="restrictions">用于确定绑定是否有效的绑定限制集。</param>
      <param name="value">由 <see cref="T:System.Dynamic.DynamicMetaObject" /> 表示的运行时值。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Dynamic.DynamicMetaObject)">
      <summary>执行动态二元运算的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.BinaryOperationBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="arg">一个 <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例，表示二元运算的右侧。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindConvert(System.Dynamic.ConvertBinder)">
      <summary>执行动态转换操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.ConvertBinder" /> 实例，表示动态操作的详细信息。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindCreateInstance(System.Dynamic.CreateInstanceBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态创建实例操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.CreateInstanceBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="args">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例（即，创建实例操作的参数）的数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态删除索引操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.DeleteIndexBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="indexes">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例（即，删除索引操作的索引）的数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>执行动态删除成员操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.DeleteMemberBinder" /> 实例，表示动态操作的详细信息。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetIndex(System.Dynamic.GetIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态获取索引操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.GetIndexBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="indexes">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例（即，获取索引操作的索引）的数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetMember(System.Dynamic.GetMemberBinder)">
      <summary>执行动态获取成员操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.GetMemberBinder" /> 实例，表示动态操作的详细信息。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvoke(System.Dynamic.InvokeBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态调用操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.InvokeBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="args">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例（即，调用操作的参数）的数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvokeMember(System.Dynamic.InvokeMemberBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态调用成员操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.InvokeMemberBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="args">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例（即调用成员操作的参数）的数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetIndex(System.Dynamic.SetIndexBinder,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>执行动态设置索引操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.SetIndexBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="indexes">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 实例（即，设置索引操作的索引）的数组。</param>
      <param name="value">表示设置索引操作的值的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetMember(System.Dynamic.SetMemberBinder,System.Dynamic.DynamicMetaObject)">
      <summary>执行动态设置成员操作的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.SetMemberBinder" /> 实例，表示动态操作的详细信息。</param>
      <param name="value">表示设置成员操作的值的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindUnaryOperation(System.Dynamic.UnaryOperationBinder)">
      <summary>执行动态一元运算的绑定。</summary>
      <returns>表示绑定结果的新 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="binder">一个 <see cref="T:System.Dynamic.UnaryOperationBinder" /> 实例，表示动态操作的详细信息。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.Create(System.Object,System.Linq.Expressions.Expression)">
      <summary>为指定对象创建一个元对象。</summary>
      <returns>如果给定对象实现 <see cref="T:System.Dynamic.IDynamicMetaObjectProvider" />，并且它不是来自当前 AppDomain 的外部的远程对象，则返回由 <see cref="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)" /> 返回的此对象的特定元对象。否则，将创建并返回无限制的新的纯元对象。</returns>
      <param name="value">要为其获取元对象的对象。</param>
      <param name="expression">在动态绑定过程中表示此 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的表达式。</param>
    </member>
    <member name="F:System.Dynamic.DynamicMetaObject.EmptyMetaObjects">
      <summary>表示 <see cref="T:System.Dynamic.DynamicMetaObject" /> 类型的空数组。此字段为只读。</summary>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Expression">
      <summary>在动态绑定过程中表示 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的表达式。</summary>
      <returns>在动态绑定过程中表示 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的表达式。</returns>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.GetDynamicMemberNames">
      <summary>返回所有动态成员名称的枚举。</summary>
      <returns>动态成员名称的列表。</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.HasValue">
      <summary>获取一个值，该值指示 <see cref="T:System.Dynamic.DynamicMetaObject" /> 是否具有运行时值。</summary>
      <returns>如果 <see cref="T:System.Dynamic.DynamicMetaObject" /> 具有运行时值，则为 True；否则为 False。</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.LimitType">
      <summary>获取 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的限制类型。</summary>
      <returns>如果运行时值可用，则为 <see cref="P:System.Dynamic.DynamicMetaObject.RuntimeType" />；否则为 <see cref="P:System.Dynamic.DynamicMetaObject.Expression" /> 的类型。</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Restrictions">
      <summary>用于确定绑定是否有效的绑定限制集。</summary>
      <returns>绑定限制集。</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.RuntimeType">
      <summary>获取运行时值的 <see cref="T:System.Type" />；如果 <see cref="T:System.Dynamic.DynamicMetaObject" /> 没有与其关联的值，则为 null。</summary>
      <returns>运行时值的 <see cref="T:System.Type" />，或为 null。</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Value">
      <summary>由此 <see cref="T:System.Dynamic.DynamicMetaObject" /> 表示的运行时值。</summary>
      <returns>由此 <see cref="T:System.Dynamic.DynamicMetaObject" /> 表示的运行时值。</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObjectBinder">
      <summary>参与 <see cref="T:System.Dynamic.DynamicMetaObject" /> 绑定协议的动态调用站点联编程序。</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.#ctor">
      <summary>初始化 <see cref="T:System.Dynamic.DynamicMetaObjectBinder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>在派生类中重写时，执行动态操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态操作的目标。</param>
      <param name="args">动态操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>对一组参数执行动态操作的运行时绑定。</summary>
      <returns>一个表达式，对动态操作参数执行测试；如果测试有效，则执行动态操作。如果测试在动态操作的后续匹配项上失败，将再次调用 Bind 为新参数类型生成新的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
      <param name="args">动态操作的参数数组。</param>
      <param name="parameters">表示绑定进程中调用站点的参数的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 实例数组。</param>
      <param name="returnLabel">用于返回动态绑定的结果的 LabelTarget。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>延迟操作绑定，直至已计算出所有动态操作参数的运行时值。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态操作的目标。</param>
      <param name="args">动态操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject[])">
      <summary>延迟操作绑定，直至已计算出所有动态操作参数的运行时值。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="args">动态操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.GetUpdateExpression(System.Type)">
      <summary>获取一个表达式，该表达式将使绑定得到更新。它指示表达式的绑定不再有效。通常在动态对象的“版本”发生更改时使用它。</summary>
      <returns>更新表达式。</returns>
      <param name="type">结果表达式的 <see cref="P:System.Linq.Expressions.Expression.Type" /> 属性；允许任何类型。</param>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObjectBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.DynamicObject">
      <summary>提供用于指定运行时的动态行为的基类。必须继承此类；不能直接实例化此类。</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.#ctor">
      <summary>使派生的类型可以初始化 <see cref="T:System.Dynamic.DynamicObject" /> 类型的新实例。</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetDynamicMemberNames">
      <summary>返回所有动态成员名称的枚举。</summary>
      <returns>一个包含动态成员名称的序列。</returns>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>提供调度到动态虚方法的 <see cref="T:System.Dynamic.DynamicMetaObject" />。可以将该对象封装到另一个 <see cref="T:System.Dynamic.DynamicMetaObject" /> 中，以便为各个不同操作提供自定义行为。此方法支持语言实现器的动态语言运行时基础结构，不应从代码直接使用。</summary>
      <returns>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 类型的对象。</returns>
      <param name="parameter">表示要调度到动态虚方法的 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的表达式。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Object,System.Object@)">
      <summary>提供二元运算的实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为诸如加法和乘法这样的运算指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关二元运算的信息。binder.Operation 属性返回一个 <see cref="T:System.Linq.Expressions.ExpressionType" /> 对象。例如，对于 sum = first + second 语句（其中 first 和 second 派生自 DynamicObject 类），binder.Operation 将返回 ExpressionType.Add。</param>
      <param name="arg">二元运算的右操作数。例如，对于 sum = first + second 语句（其中 first 和 second 派生自 DynamicObject 类），<paramref name="arg" /> 等于 second。</param>
      <param name="result">二元运算的结果。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
      <summary>提供类型转换运算的实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为将某个对象从一种类型转换为另一种类型的运算指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关转换运算的信息。binder.Type 属性提供必须将对象转换为的类型。例如，对于 C# 中的语句 (String)sampleObject（Visual Basic 中为 CType(sampleObject, Type)）（其中 sampleObject 是派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类的类的一个实例），binder.Type 将返回 <see cref="T:System.String" /> 类型。binder.Explicit 属性提供有关所发生转换的类型的信息。对于显式转换，它返回 true；对于隐式转换，它返回 false。</param>
      <param name="result">类型转换运算的结果。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryCreateInstance(System.Dynamic.CreateInstanceBinder,System.Object[],System.Object@)">
      <summary>为初始化动态对象的新实例的操作提供实现。不应将此方法用于 C# 或 Visual Basic。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关初始化操作的信息。</param>
      <param name="args">初始化期间传递给对象的参数。例如，对于 new SampleType(100) 操作（其中 SampleType 是派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类的类型），<paramref name="args[0]" /> 等于 100。</param>
      <param name="result">初始化的结果。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Object[])">
      <summary>为按索引删除对象的操作提供实现。不应将此方法用于 C# 或 Visual Basic。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关删除的信息。</param>
      <param name="indexes">要删除的索引。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>为删除对象成员的操作提供实现。不应将此方法用于 C# 或 Visual Basic。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关删除的信息。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetIndex(System.Dynamic.GetIndexBinder,System.Object[],System.Object@)">
      <summary>为按索引获取值的操作提供实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为索引操作指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发运行时异常。）</returns>
      <param name="binder">提供有关该操作的信息。</param>
      <param name="indexes">该操作中使用的索引。例如，对于 C# 中的 sampleObject[3] 操作（Visual Basic 中为 sampleObject(3)）（其中 sampleObject 派生自 DynamicObject 类），<paramref name="indexes[0]" /> 等于 3。</param>
      <param name="result">索引操作的结果。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <summary>为获取成员值的操作提供实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为诸如获取属性值这样的操作指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发运行时异常。）</returns>
      <param name="binder">提供有关调用了动态操作的对象的信息。binder.Name 属性提供针对其执行动态操作的成员的名称。例如，对于 Console.WriteLine(sampleObject.SampleProperty) 语句（其中 sampleObject 是派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类的类的一个实例），binder.Name 将返回“SampleProperty”。binder.IgnoreCase 属性指定成员名称是否区分大小写。</param>
      <param name="result">获取操作的结果。例如，如果为某个属性调用该方法，则可以为 <paramref name="result" /> 指派该属性值。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvoke(System.Dynamic.InvokeBinder,System.Object[],System.Object@)">
      <summary>为调用对象的操作提供实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为诸如调用对象或委托这样的操作指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关调用操作的信息。</param>
      <param name="args">调用操作期间传递给对象的参数。例如，对于 sampleObject(100) 操作（其中 sampleObject 派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类），<paramref name="args[0]" /> 等于 100。</param>
      <param name="result">对象调用的结果。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
      <summary>为调用成员的操作提供实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为诸如调用方法这样的操作指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关动态操作的信息。binder.Name 属性提供针对其执行动态操作的成员的名称。例如，对于语句 sampleObject.SampleMethod(100)（其中 sampleObject 是派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类的类的一个实例），binder.Name 将返回“SampleMethod”。binder.IgnoreCase 属性指定成员名称是否区分大小写。</param>
      <param name="args">调用操作期间传递给对象成员的参数。例如，对于语句 sampleObject.SampleMethod(100)（其中 sampleObject 派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类），<paramref name="args[0]" /> 等于 100。</param>
      <param name="result">成员调用的结果。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetIndex(System.Dynamic.SetIndexBinder,System.Object[],System.Object)">
      <summary>为按索引设置值的操作提供实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为按指定索引访问对象的操作指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关该操作的信息。</param>
      <param name="indexes">该操作中使用的索引。例如，对于 C# 中的 sampleObject[3] = 10 操作（Visual Basic 中为 sampleObject(3) = 10）（其中 sampleObject 派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类），<paramref name="indexes[0]" /> 等于 3。</param>
      <param name="value">要为具有指定索引的对象设置的值。例如，对于 C# 中的 sampleObject[3] = 10 操作（Visual Basic 中为 sampleObject(3) = 10）（其中 sampleObject 派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类），<paramref name="value" /> 等于 10。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
      <summary>为设置成员值的操作提供实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为诸如设置属性值这样的操作指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关调用了动态操作的对象的信息。binder.Name 属性提供将该值分配到的成员的名称。例如，对于语句 sampleObject.SampleProperty = "Test"（其中 sampleObject 是派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类的类的一个实例），binder.Name 将返回“SampleProperty”。binder.IgnoreCase 属性指定成员名称是否区分大小写。</param>
      <param name="value">要为成员设置的值。例如，对于 sampleObject.SampleProperty = "Test"（其中 sampleObject 是派生自 <see cref="T:System.Dynamic.DynamicObject" /> 类的类的一个实例），<paramref name="value" /> 为“Test”。</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryUnaryOperation(System.Dynamic.UnaryOperationBinder,System.Object@)">
      <summary>提供一元运算的实现。从 <see cref="T:System.Dynamic.DynamicObject" /> 类派生的类可以重写此方法，以便为诸如求反、递增、递减这样的运算指定动态行为。</summary>
      <returns>如果此运算成功，则为 true；否则为 false。如果此方法返回 false，则该语言的运行时联编程序将决定行为。（大多数情况下，将引发语言特定的运行时异常。）</returns>
      <param name="binder">提供有关一元运算的信息。binder.Operation 属性返回一个 <see cref="T:System.Linq.Expressions.ExpressionType" /> 对象。例如，对于 negativeNumber = -number 语句（其中 number 派生自 DynamicObject 类），binder.Operation 将返回“Negate”。</param>
      <param name="result">一元运算的结果。</param>
    </member>
    <member name="T:System.Dynamic.ExpandoObject">
      <summary>表示一个对象，该对象包含可在运行时动态添加和移除的成员。</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.#ctor">
      <summary>初始化不包含任何成员的新 ExpandoObject。</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>将指定值添加到具有指定键的 <see cref="T:System.Collections.Generic.ICollection`1" /> 中。</summary>
      <param name="item">表示要添加到集合中的键和值的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Clear">
      <summary>从集合中移除所有项。</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>确定 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否包含特定的键和值。</summary>
      <returns>如果集合包含特定的键和值，则为 true；否则为 false。</returns>
      <param name="item">要在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中查找的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
      <summary>从指定的数组索引开始，将 <see cref="T:System.Collections.Generic.ICollection`1" /> 的元素复制到类型 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 的数组中。</summary>
      <param name="array">类型 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 的一维数组，是从 <see cref="T:System.Collections.Generic.ICollection`1" /> 复制的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 元素的目标数组。该数组的索引必须从零开始。</param>
      <param name="arrayIndex">
        <paramref name="array" /> 中开始复制位置的从零开始的索引。</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Count">
      <summary>获取 <see cref="T:System.Collections.Generic.ICollection`1" /> 中元素的数目。</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 中元素的数目。</returns>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>获取一个值，该值指示 <see cref="T:System.Collections.Generic.ICollection`1" /> 是否为只读。</summary>
      <returns>如果 <see cref="T:System.Collections.Generic.ICollection`1" /> 为只读，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>从集合中移除键和值。</summary>
      <returns>如果成功找到并移除了键和值，则为 true；否则为 false。如果在 <see cref="T:System.Collections.Generic.ICollection`1" /> 中未找到键和值，则此方法将返回 false。</returns>
      <param name="item">表示要从集合中移除的键和值的 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 结构。</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Add(System.String,System.Object)">
      <summary>将指定的键和值添加到字典中。</summary>
      <param name="key">要用作键的对象。</param>
      <param name="value">要用作值的对象。</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#ContainsKey(System.String)">
      <summary>确定字典是否包含指定键。</summary>
      <returns>如果词典包含具有指定键的元素，则为 true；否则为 false。</returns>
      <param name="key">要在字典中定位的键。</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Item(System.String)">
      <summary>获取或设置具有指定键的元素。</summary>
      <returns>具有指定键的元素。</returns>
      <param name="key">要获取或设置的元素的键。</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>获取包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的键的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.ICollection`1" />，其中包含实现 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的对象的键。</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(System.String)">
      <summary>从 <see cref="T:System.Collections.IDictionary" /> 中移除具有指定键的元素。</summary>
      <returns>如果该元素已成功移除，则为 true；否则为 false。如果在原始 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中没有找到 <paramref name="key" />，此方法也会返回 false。</returns>
      <param name="key">要移除的元素的键。</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#TryGetValue(System.String,System.Object@)">
      <summary>获取与指定的键相关联的值。</summary>
      <returns>如果实现 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的对象包含具有指定键的元素，则为 true；否则为 false。</returns>
      <param name="key">要获取的值的键。</param>
      <param name="value">当此方法返回时，如果找到该键，则包含与指定的键相关联的值；否则将包含 <paramref name="value" /> 参数的类型默认值。该参数未经初始化即被传递。</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>获取包含 <see cref="T:System.Collections.Generic.IDictionary`2" /> 中的值的 <see cref="T:System.Collections.Generic.ICollection`1" />。</summary>
      <returns>一个 <see cref="T:System.Collections.Generic.ICollection`1" />，其中包含实现 <see cref="T:System.Collections.Generic.IDictionary`2" /> 的对象中的值。</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.Generic.IEnumerator`1" /> 对象。</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
    </member>
    <member name="E:System.Dynamic.ExpandoObject.System#ComponentModel#INotifyPropertyChanged#PropertyChanged">
      <summary>在属性值更改时发生。</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Dynamic#IDynamicMetaObjectProvider#GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>提供的 MetaObject 将调度到 Dynamic 虚方法。可以将该对象封装到另一个 MetaObject 中，以便为单个操作提供自定义行为。</summary>
      <returns>类型为 <see cref="T:System.Dynamic.DynamicMetaObject" /> 的对象。</returns>
      <param name="parameter">表示要调度到动态虚方法的 MetaObject 的表达式。</param>
    </member>
    <member name="T:System.Dynamic.GetIndexBinder">
      <summary>表示调用站点上的动态获取索引操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>初始化 <see cref="T:System.Dynamic.GetIndexBinder" /> 的新实例。</summary>
      <param name="callInfo">调用站点上的参数的签名。</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态获取索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态获取索引操作的目标。</param>
      <param name="args">动态获取索引操作的参数数组。</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.CallInfo">
      <summary>获取调用站点上的参数的签名。</summary>
      <returns>调用站点上的参数的签名。</returns>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>如果无法绑定目标动态对象，则执行动态获取索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态获取索引操作的目标。</param>
      <param name="indexes">动态获取索引操作的参数。</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态获取索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态获取索引操作的目标。</param>
      <param name="indexes">动态获取索引操作的参数。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.GetMemberBinder">
      <summary>表示调用站点上的动态获取成员操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>初始化 <see cref="T:System.Dynamic.GetMemberBinder" /> 的新实例。</summary>
      <param name="name">要获取的成员的名称。</param>
      <param name="ignoreCase">如果在匹配名称时应忽略大小写，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态获取成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态获取成员操作的目标。</param>
      <param name="args">动态获取成员操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行动态获取成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态获取成员操作的目标。</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态获取成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态获取成员操作的目标。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.IgnoreCase">
      <summary>获取指示字符串比较是否应忽略成员名称大小写的值。</summary>
      <returns>如果忽略大小写，则为 true，否则为 false。</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.Name">
      <summary>获取要获取的成员的名称。</summary>
      <returns>要获取的成员的名称。</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.IDynamicMetaObjectProvider">
      <summary>表示一个动态对象，在运行时可将该对象的操作进行绑定。</summary>
    </member>
    <member name="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>返回 <see cref="T:System.Dynamic.DynamicMetaObject" />，它负责绑定对此对象执行的操作。</summary>
      <returns>要绑定此对象的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="parameter">运行时值的表达式树表示形式。</param>
    </member>
    <member name="T:System.Dynamic.IInvokeOnGetBinder">
      <summary>表示有关某个动态获取成员操作的信息，该信息指示此获取成员在执行获取操作时是否应该调用属性。</summary>
    </member>
    <member name="P:System.Dynamic.IInvokeOnGetBinder.InvokeOnGet">
      <summary>获取一个值，该值指示此获取成员在执行获取操作时是否应该调用属性。此接口不存在时，默认值为 true。</summary>
      <returns>如果此获取成员在执行获取操作时应该调用属性，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Dynamic.InvokeBinder">
      <summary>表示调用站点上的调用动态操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>初始化 <see cref="T:System.Dynamic.InvokeBinder" /> 的新实例。</summary>
      <param name="callInfo">调用站点上的参数的签名。</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态调用操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态调用操作的目标。</param>
      <param name="args">动态调用操作的参数数组。</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.CallInfo">
      <summary>获取调用站点上的参数的签名。</summary>
      <returns>调用站点上的参数的签名。</returns>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>如果无法绑定目标动态对象，则执行动态调用操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态调用操作的目标。</param>
      <param name="args">动态调用操作的参数。</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行动态调用操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态调用操作的目标。</param>
      <param name="args">动态调用操作的参数。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.InvokeMemberBinder">
      <summary>表示调用站点上的调用成员动态操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.#ctor(System.String,System.Boolean,System.Dynamic.CallInfo)">
      <summary>初始化 <see cref="T:System.Dynamic.InvokeMemberBinder" /> 的新实例。</summary>
      <param name="name">要调用的成员名。</param>
      <param name="ignoreCase">如果在匹配名称时应忽略大小写，则为 true；否则为 false。</param>
      <param name="callInfo">调用站点上的参数的签名。</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态调用成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态调用成员操作的目标。</param>
      <param name="args">动态调用成员操作的参数数组。</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.CallInfo">
      <summary>获取调用站点上的参数的签名。</summary>
      <returns>调用站点上的参数的签名。</returns>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态调用操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态调用操作的目标。</param>
      <param name="args">动态调用操作的参数。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>如果无法绑定目标动态对象，则执行动态调用成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态调用成员操作的目标。</param>
      <param name="args">动态调用成员操作的参数。</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态调用成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态调用成员操作的目标。</param>
      <param name="args">动态调用成员操作的参数。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.IgnoreCase">
      <summary>获取指示字符串比较是否应忽略成员名称大小写的值。</summary>
      <returns>如果忽略大小写，则为 true，否则为 false。</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.Name">
      <summary>获取要调用的成员的名称。</summary>
      <returns>要调用的成员名。</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.SetIndexBinder">
      <summary>表示调用站点上的动态设置索引操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>初始化 <see cref="T:System.Dynamic.SetIndexBinder" /> 的新实例。</summary>
      <param name="callInfo">调用站点上的参数的签名。</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态设置索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态设置索引操作的目标。</param>
      <param name="args">动态设置索引操作的参数数组。</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.CallInfo">
      <summary>获取调用站点上的参数的签名。</summary>
      <returns>调用站点上的参数的签名。</returns>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行动态设置索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态设置索引操作的目标。</param>
      <param name="indexes">动态设置索引操作的参数。</param>
      <param name="value">要设置为集合的值。</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>在派生类中重写时，如果无法绑定目标动态对象，则执行动态设置索引操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态设置索引操作的目标。</param>
      <param name="indexes">动态设置索引操作的参数。</param>
      <param name="value">要设置为集合的值。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.SetMemberBinder">
      <summary>表示调用站点上的动态设置成员操作，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>初始化 <see cref="T:System.Dynamic.SetMemberBinder" /> 的新实例。</summary>
      <param name="name">要获取的成员的名称。</param>
      <param name="ignoreCase">如果在匹配名称时应忽略大小写，则为 true；否则为 false。</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态设置成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态设置成员操作的目标。</param>
      <param name="args">动态设置成员操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行动态集成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态设置成员操作的目标。</param>
      <param name="value">要为成员设置的值。</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行动态集成员操作的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态设置成员操作的目标。</param>
      <param name="value">要为成员设置的值。</param>
      <param name="errorSuggestion">绑定失败时要使用的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.IgnoreCase">
      <summary>获取指示字符串比较是否应忽略成员名称大小写的值。</summary>
      <returns>如果忽略大小写，则为 true，否则为 false。</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.Name">
      <summary>获取要获取的成员的名称。</summary>
      <returns>要获取的成员的名称。</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Dynamic.UnaryOperationBinder">
      <summary>表示调用站点上的一元动态运算，它提供绑定语义和有关操作的细节。</summary>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>初始化 <see cref="T:System.Dynamic.BinaryOperationBinder" /> 类的新实例。</summary>
      <param name="operation">一元运算类型。</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>执行动态一元运算的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态操作的目标。</param>
      <param name="args">动态操作的参数数组。</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行一元动态运算的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态一元运算的目标。</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>如果无法绑定目标动态对象，则执行一元动态运算的绑定。</summary>
      <returns>表示绑定结果的 <see cref="T:System.Dynamic.DynamicMetaObject" />。</returns>
      <param name="target">动态一元运算的目标。</param>
      <param name="errorSuggestion">绑定失败时的绑定结果，或为 null。</param>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.Operation">
      <summary>一元运算类型。</summary>
      <returns>表示一元运算类型的 <see cref="T:System.Linq.Expressions.ExpressionType" /> 的对象。</returns>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.ReturnType">
      <summary>操作的结果类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示操作的结果类型。</returns>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpression">
      <summary>表示动态操作。</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>调度到此节点类型的特定 Visit 方法。例如，<see cref="T:System.Linq.Expressions.MethodCallExpression" /> 调用 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />。</summary>
      <returns>对此节点进行访问的结果。</returns>
      <param name="visitor">对此节点进行访问的访问者。</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Arguments">
      <summary>获取动态操作的参数。</summary>
      <returns>包含动态操作的参数的只读集合。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Binder">
      <summary>获取 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />，它确定动态站点的运行时行为。</summary>
      <returns>
        <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />，它确定动态站点的运行时行为。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.DelegateType">
      <summary>获取由 <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</summary>
      <returns>
        <see cref="T:System.Type" /> 对象，表示由 <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="returnType">动态表达式的结果类型。</param>
      <param name="arguments">动态操作的参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="returnType">动态表达式的结果类型。</param>
      <param name="arg0">动态操作的第一个参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="returnType">动态表达式的结果类型。</param>
      <param name="arg0">动态操作的第一个参数。</param>
      <param name="arg1">动态操作的第二个参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="returnType">动态表达式的结果类型。</param>
      <param name="arg0">动态操作的第一个参数。</param>
      <param name="arg1">动态操作的第二个参数。</param>
      <param name="arg2">动态操作的第三个参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="returnType">动态表达式的结果类型。</param>
      <param name="arg0">动态操作的第一个参数。</param>
      <param name="arg1">动态操作的第二个参数。</param>
      <param name="arg2">动态操作的第三个参数。</param>
      <param name="arg3">动态操作的第四个参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="returnType">动态表达式的结果类型。</param>
      <param name="arguments">动态操作的参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />、<see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</param>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="arguments">动态操作的参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 和一个参数绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />、<see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</param>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="arg0">动态操作的参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 和两个参数绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />、<see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</param>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="arg0">动态操作的第一个参数。</param>
      <param name="arg1">动态操作的第二个参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 和三个参数绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />、<see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</param>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="arg0">动态操作的第一个参数。</param>
      <param name="arg1">动态操作的第二个参数。</param>
      <param name="arg2">动态操作的第三个参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 和四个参数绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />、<see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</param>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="arg0">动态操作的第一个参数。</param>
      <param name="arg1">动态操作的第二个参数。</param>
      <param name="arg2">动态操作的第三个参数。</param>
      <param name="arg3">动态操作的第四个参数。</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression[])">
      <summary>创建一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，它表示由提供的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 绑定的动态操作。</summary>
      <returns>一个 <see cref="T:System.Linq.Expressions.DynamicExpression" />，其 <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> 等于 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />，并且其 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />、<see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 和 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 均设置为指定的值。</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" /> 使用的委托的类型。</param>
      <param name="binder">动态操作的运行时联编程序。</param>
      <param name="arguments">动态操作的参数。</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.NodeType">
      <summary>返回此表达式的节点类型。扩展节点应在重写此方法时返回 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />。</summary>
      <returns>该表达式的 <see cref="T:System.Linq.Expressions.ExpressionType" />。</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#CreateCallSite"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#Rewrite(System.Linq.Expressions.Expression[])"></member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Type">
      <summary>获取此 <see cref="T:System.Linq.Expressions.Expression" /> 所表示的表达式的静态类型。</summary>
      <returns>表示表达式的静态类型的 <see cref="P:System.Linq.Expressions.DynamicExpression.Type" />。</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>将发送至这个参数的数值，arguments 与 DynamicExpression 的当前实例的 Arguments 属性进行比较。                                        如果参数和属性的值相同，则返回当前实例。如果它们不是等效的，则返回一个新的 DynamicExpression 实例，该实例的 Arguments 被设置为参数 arguments 的值，实例其它部分与当前实例完全相同。</summary>
      <returns>此表达式（如果未更改任何子级），或带有更新的子级的表达式。</returns>
      <param name="arguments">结果的 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 属性。</param>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpressionVisitor">
      <summary>表示动态表达式树的访问者或重写者。</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.#ctor">
      <summary>初始化 <see cref="T:System.Linq.Expressions.DynamicExpressionVisitor" /> 的新实例。</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.VisitDynamic(System.Linq.Expressions.DynamicExpression)">
      <summary>访问 <see cref="T:System.Linq.Expressions.DynamicExpression" /> 的子级。</summary>
      <returns>如果修改了该表达式或任何子表达式，则返回 <see cref="T:System.Linq.Expressions.Expression" />；否则返回原始表达式。</returns>
      <param name="node">要访问的表达式。</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite">
      <summary>动态调用站点的基类。此类型用作动态站点目标的参数类型。</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite.Binder">
      <summary>负责在动态站点上绑定动态操作的类。</summary>
      <returns>负责绑定动态操作的 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 对象。</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite.Create(System.Type,System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>使用给定的委托类型和联编程序创建一个调用站点。</summary>
      <returns>新的调用站点。</returns>
      <param name="delegateType">调用站点的委托类型。</param>
      <param name="binder">调用站点的联编程序。</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite`1">
      <summary>动态站点类型。</summary>
      <typeparam name="T">委托类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite`1.Create(System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>创建动态调用站点的实例，使用负责此调用站点上动态操作的运行时绑定的联编程序进行初始化。</summary>
      <returns>动态调用站点的新实例。</returns>
      <param name="binder">负责此调用站点上动态操作的运行时绑定的联编程序。</param>
    </member>
    <member name="F:System.Runtime.CompilerServices.CallSite`1.Target">
      <summary>0 级缓存 - 基于站点历史记录专用的委托。</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite`1.Update">
      <summary>更新委托。在动态站点遇到缓存未命中时调用。</summary>
      <returns>更新委托。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteBinder">
      <summary>负责动态调用站点上动态操作的运行时绑定的类。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>对一组参数执行动态操作的运行时绑定。</summary>
      <returns>一个表达式，对动态操作参数执行测试；如果测试有效，则执行动态操作。如果测试在动态操作的后续匹配项上失败，将再次调用 Bind 为新参数类型生成新的 <see cref="T:System.Linq.Expressions.Expression" />。</returns>
      <param name="args">动态操作的参数数组。</param>
      <param name="parameters">表示绑定进程中调用站点的参数的 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 实例数组。</param>
      <param name="returnLabel">用于返回动态绑定的结果的 LabelTarget。</param>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.BindDelegate``1(System.Runtime.CompilerServices.CallSite{``0},System.Object[])">
      <summary>提供低级别的运行时绑定支持。类可以对此进行重写，并为规则的实现提供直接委托。这样，就可以将规则保存到磁盘，使专用规则在运行时可用，或则提供不同的缓存策略。</summary>
      <returns>替换调用站点目标的新委托。</returns>
      <param name="site">为其执行绑定的调用站点。</param>
      <param name="args">联编程序的参数。</param>
      <typeparam name="T">调用站点的目标类型。</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.CacheTarget``1(``0)">
      <summary>将一个目标添加到已知目标的缓存。在调用 BindDelegate 生成新规则之前，将扫描已缓存的目标。</summary>
      <param name="target">要添加到缓存的目标委托。</param>
      <typeparam name="T">要添加的目标的类型。</typeparam>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSiteBinder.UpdateLabel">
      <summary>获取一个可用于导致绑定被更新的标签。它指示表达式的绑定不再有效。通常在动态对象的“版本”发生更改时使用它。</summary>
      <returns>
        <see cref="T:System.Linq.Expressions.LabelTarget" /> 对象，表示可用于触发绑定更新的标签。</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteHelpers">
      <summary>包含 DLR 调用站点的帮助器方法的类。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteHelpers.IsInternalFrame(System.Reflection.MethodBase)">
      <summary>检查 <see cref="T:System.Reflection.MethodBase" /> 是否由 DLR 在内部使用且不应显示在语言代码的堆栈上。</summary>
      <returns>如果输入 <see cref="T:System.Reflection.MethodBase" /> 由 DLR 在内部使用且不应显示在语言代码的堆栈上，则为 True。否则为 false。</returns>
      <param name="mb">输入 <see cref="T:System.Reflection.MethodBase" /></param>
    </member>
    <member name="T:System.Runtime.CompilerServices.DynamicAttribute">
      <summary>指示在成员上使用 <see cref="T:System.Object" /> 应被视为动态调度的类型。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor">
      <summary>初始化 <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor(System.Boolean[])">
      <summary>初始化 <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" /> 类的新实例。</summary>
      <param name="transformFlags">指定在类型结构的前缀遍历中哪些 <see cref="T:System.Object" /> 匹配项应被视为动态调度的类型。</param>
    </member>
    <member name="P:System.Runtime.CompilerServices.DynamicAttribute.TransformFlags">
      <summary>指定在类型结构的前缀遍历中哪些 <see cref="T:System.Object" /> 匹配项应被视为动态调度的类型。</summary>
      <returns>应被视为动态调度的类型的 <see cref="T:System.Object" /> 匹配项的列表。</returns>
    </member>
  </members>
</doc>