﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Dynamic.Runtime</name>
  </assembly>
  <members>
    <member name="T:System.Dynamic.BinaryOperationBinder">
      <summary>Rappresenta l'operazione binaria dinamica nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Dynamic.BinaryOperationBinder" />.</summary>
      <param name="operation">Tipo di operazione binaria.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione binaria dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione binaria dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione binaria dinamica.</param>
      <param name="arg">Operando destro dell'operazione binaria dinamica.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione binaria dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione binaria dinamica.</param>
      <param name="arg">Operando destro dell'operazione binaria dinamica.</param>
      <param name="errorSuggestion">Risultato dell'associazione se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.Operation">
      <summary>Tipo di operazione binaria.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.ExpressionType" /> che rappresenta il tipo di operazione binaria.</returns>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.BindingRestrictions">
      <summary>Rappresenta un set di restrizioni relative all'associazione per l'oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che determinano la validità dell'associazione dinamica.</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Combine(System.Collections.Generic.IList{System.Dynamic.DynamicMetaObject})">
      <summary>Combina le restrizioni relative all'associazione dall'elenco di istanze di <see cref="T:System.Dynamic.DynamicMetaObject" /> in un unico set di restrizioni.</summary>
      <returns>Nuovo set di restrizioni relative all'associazione.</returns>
      <param name="contributingObjects">Elenco di istanze di <see cref="T:System.Dynamic.DynamicMetaObject" /> da cui combinare le restrizioni.</param>
    </member>
    <member name="F:System.Dynamic.BindingRestrictions.Empty">
      <summary>Rappresenta un set vuoto di restrizioni relative all'associazione.Questo campo è di sola lettura.</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetExpressionRestriction(System.Linq.Expressions.Expression)">
      <summary>Crea la restrizione relativa all'associazione che controlla l'eventuale presenza di proprietà non modificabili arbitrarie nell'espressione.</summary>
      <returns>Nuove restrizioni relative all'associazione.</returns>
      <param name="expression">Espressione che rappresenta le restrizioni.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetInstanceRestriction(System.Linq.Expressions.Expression,System.Object)">
      <summary>Crea la restrizione relativa all'associazione che controlla l'identità dell'istanza dell'oggetto nell'espressione.</summary>
      <returns>Nuove restrizioni relative all'associazione.</returns>
      <param name="expression">Espressione da verificare.</param>
      <param name="instance">Istanza esatta dell'oggetto da testare.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetTypeRestriction(System.Linq.Expressions.Expression,System.Type)">
      <summary>Crea la restrizione relativa all'associazione che controlla l'identità del tipo di runtime nell'espressione.</summary>
      <returns>Nuove restrizioni relative all'associazione.</returns>
      <param name="expression">Espressione da verificare.</param>
      <param name="type">Tipo esatto da testare.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Merge(System.Dynamic.BindingRestrictions)">
      <summary>Unisce il set di restrizioni relative all'associazione alle restrizioni correnti.</summary>
      <returns>Nuovo set di restrizioni relative all'associazione.</returns>
      <param name="restrictions">Set di restrizioni a cui unire le restrizioni relative all'associazione correnti.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.ToExpression">
      <summary>Crea l'oggetto <see cref="T:System.Linq.Expressions.Expression" /> che rappresenta le restrizioni relative all'associazione.</summary>
      <returns>Struttura ad albero dell'espressione che rappresenta le restrizioni.</returns>
    </member>
    <member name="T:System.Dynamic.CallInfo">
      <summary>Descrive gli argomenti nel processo di associazione dinamico.</summary>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.Collections.Generic.IEnumerable{System.String})">
      <summary>Crea un nuovo CallInfo che rappresenta gli argomenti nel processo di associazione dinamico.</summary>
      <param name="argCount">Numero di argomenti.</param>
      <param name="argNames">Nomi degli argomenti.</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.String[])">
      <summary>Crea un nuovo oggetto PositionalArgumentInfo.</summary>
      <param name="argCount">Numero di argomenti.</param>
      <param name="argNames">Nomi degli argomenti.</param>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentCount">
      <summary>Numero di argomenti.</summary>
      <returns>Numero di argomenti.</returns>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentNames">
      <summary>Nomi degli argomenti.</summary>
      <returns>Raccolta di sola lettura di nomi degli argomenti.</returns>
    </member>
    <member name="M:System.Dynamic.CallInfo.Equals(System.Object)">
      <summary>Determina se l'istanza di CallInfo specificata è considerata uguale a quella corrente.</summary>
      <returns>Restituisce true se l'istanza specificata è uguale a quella corrente; in caso contrario, false.</returns>
      <param name="obj">Istanza di <see cref="T:System.Dynamic.CallInfo" /> da confrontare con l'istanza corrente.</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.GetHashCode">
      <summary>Funge da funzione hash per l'oggetto <see cref="T:System.Dynamic.CallInfo" /> corrente.</summary>
      <returns>Codice hash per l'oggetto <see cref="T:System.Dynamic.CallInfo" /> corrente.</returns>
    </member>
    <member name="T:System.Dynamic.ConvertBinder">
      <summary>Rappresenta l'operazione di conversione dinamica nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.#ctor(System.Type,System.Boolean)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.ConvertBinder" />.</summary>
      <param name="type">Tipo in cui eseguire la conversione.</param>
      <param name="explicit">Restituisce true se la conversione deve considerare le conversioni esplicite; in caso contrario, false.</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione di conversione dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione di conversione dinamica.</param>
      <param name="args">Matrice di argomenti dell'operazione di conversione dinamica.</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Explicit">
      <summary>Ottiene il valore che indica se la conversione deve considerare le conversioni esplicite.</summary>
      <returns>True se è presente una conversione esplicita; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione di conversione dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione di conversione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione di conversione dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione di conversione dinamica.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Type">
      <summary>Tipo in cui eseguire la conversione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo in cui eseguire la conversione.</returns>
    </member>
    <member name="T:System.Dynamic.CreateInstanceBinder">
      <summary>Rappresenta l'operazione di creazione dinamica nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.CreateInstanceBinder" />.</summary>
      <param name="callInfo">Firma degli argomenti nel sito di chiamata.</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione di creazione dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione di creazione dinamica.</param>
      <param name="args">Matrice di argomenti dell'operazione di creazione dinamica.</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.CallInfo">
      <summary>Ottiene la firma degli argomenti nel sito di chiamata.</summary>
      <returns>Firma degli argomenti nel sito di chiamata.</returns>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione di creazione dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione di creazione dinamica.</param>
      <param name="args">Argomenti dell'operazione di creazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione di creazione dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione di creazione dinamica.</param>
      <param name="args">Argomenti dell'operazione di creazione dinamica.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.DeleteIndexBinder">
      <summary>Rappresenta l'operazione dinamica di eliminazione dell'indice nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.DeleteIndexBinder" />.</summary>
      <param name="callInfo">Firma degli argomenti nel sito di chiamata.</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione dinamica di eliminazione dell'indice.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di eliminazione dell'indice.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica di eliminazione dell'indice.</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.CallInfo">
      <summary>Ottiene la firma degli argomenti nel sito di chiamata.</summary>
      <returns>Firma degli argomenti nel sito di chiamata.</returns>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione dinamica di eliminazione dell'indice se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di eliminazione dell'indice.</param>
      <param name="indexes">Argomenti dell'operazione dinamica di eliminazione dell'indice.</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione dinamica di eliminazione dell'indice se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di eliminazione dell'indice.</param>
      <param name="indexes">Argomenti dell'operazione dinamica di eliminazione dell'indice.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.DeleteMemberBinder">
      <summary>Rappresenta l'operazione dinamica di eliminazione del membro nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.DeleteIndexBinder" />.</summary>
      <param name="name">Nome del membro da eliminare.</param>
      <param name="ignoreCase">Restituisce true se nella corrispondenza del nome deve essere ignorata la distinzione tra maiuscole e minuscole; in caso contrario, false.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione dinamica di eliminazione del membro.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di eliminazione del membro.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica di eliminazione del membro.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione dinamica di eliminazione del membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di eliminazione del membro.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione dinamica di eliminazione del membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di eliminazione del membro.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.IgnoreCase">
      <summary>Ottiene il valore che indica se nel confronto tra stringhe deve essere ignorata la distinzione tra maiuscole e minuscole per il nome del membro.</summary>
      <returns>Restituisce true se nel confronto tra stringhe deve essere ignorata la distinzione tra maiuscole e minuscole; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.Name">
      <summary>Ottiene il nome del membro da eliminare.</summary>
      <returns>Nome del membro da eliminare.</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObject">
      <summary>Rappresenta l'associazione dinamica e un'associazione logica di un oggetto che partecipa all'associazione dinamica.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <param name="expression">Espressione che rappresenta <see cref="T:System.Dynamic.DynamicMetaObject" /> durante il processo di associazione dinamica.</param>
      <param name="restrictions">Set di restrizioni relative all'associazione che determinano la validità dell'associazione.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions,System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <param name="expression">Espressione che rappresenta <see cref="T:System.Dynamic.DynamicMetaObject" /> durante il processo di associazione dinamica.</param>
      <param name="restrictions">Set di restrizioni relative all'associazione che determinano la validità dell'associazione.</param>
      <param name="value">Valore di runtime rappresentato da <see cref="T:System.Dynamic.DynamicMetaObject" />.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione binaria dinamica.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.BinaryOperationBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="arg">Istanza di <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il lato destro dell'operazione binaria.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindConvert(System.Dynamic.ConvertBinder)">
      <summary>Esegue l'associazione dell'operazione di conversione dinamica.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.ConvertBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindCreateInstance(System.Dynamic.CreateInstanceBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione dinamica di creazione dell'istanza.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.CreateInstanceBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="args">Matrice di istanze o argomenti di <see cref="T:System.Dynamic.DynamicMetaObject" /> per l'operazione di creazione istanza.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione dinamica di eliminazione dell'indice.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.DeleteIndexBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="indexes">Una matrice di istanze o indici di <see cref="T:System.Dynamic.DynamicMetaObject" /> per l'operazione di eliminazione dell'indice.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>Esegue l'associazione dell'operazione dinamica di eliminazione del membro.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.DeleteMemberBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetIndex(System.Dynamic.GetIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione get dinamica sull'indice.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.GetIndexBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="indexes">Matrice di istanze o indici di <see cref="T:System.Dynamic.DynamicMetaObject" /> per l'operazione get sull'indice.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetMember(System.Dynamic.GetMemberBinder)">
      <summary>Esegue l'associazione dell'operazione get dinamica sul membro.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.GetMemberBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvoke(System.Dynamic.InvokeBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione invoke dinamica.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.InvokeBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="args">Matrice di istanze o argomenti di <see cref="T:System.Dynamic.DynamicMetaObject" /> per l'operazione invoke.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvokeMember(System.Dynamic.InvokeMemberBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione invoke dinamica sul membro.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.InvokeMemberBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="args">Matrice di istanze o argomenti di <see cref="T:System.Dynamic.DynamicMetaObject" /> per l'operazione invoke sul membro.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetIndex(System.Dynamic.SetIndexBinder,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione dinamica di impostazione dell'indice.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.SetIndexBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="indexes">Matrice di istanze o indici di <see cref="T:System.Dynamic.DynamicMetaObject" /> per l'operazione di impostazione dell'indice.</param>
      <param name="value">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il valore per l'operazione di impostazione dell'indice.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetMember(System.Dynamic.SetMemberBinder,System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione dinamica di impostazione del membro.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.SetMemberBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
      <param name="value">
        <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il valore per l'operazione di impostazione del membro.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindUnaryOperation(System.Dynamic.UnaryOperationBinder)">
      <summary>Esegue l'associazione dell'operazione unaria dinamica.</summary>
      <returns>Nuovo oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="binder">Istanza di <see cref="T:System.Dynamic.UnaryOperationBinder" /> che rappresenta i dettagli dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.Create(System.Object,System.Linq.Expressions.Expression)">
      <summary>Crea un metaoggetto per l'oggetto specificato.</summary>
      <returns>Se l'oggetto specificato implementa <see cref="T:System.Dynamic.IDynamicMetaObjectProvider" /> e non è un oggetto remoto esterno all'AppDomain corrente, restituisce il metaoggetto specifico dell'oggetto restituito da <see cref="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)" />.In caso contrario, viene creato e restituito un nuovo metaoggetto semplice senza restrizioni.</returns>
      <param name="value">Oggetto per cui ottenere un metaoggetto.</param>
      <param name="expression">Espressione che rappresenta <see cref="T:System.Dynamic.DynamicMetaObject" /> durante il processo di associazione dinamica.</param>
    </member>
    <member name="F:System.Dynamic.DynamicMetaObject.EmptyMetaObjects">
      <summary>Rappresenta una matrice vuota di tipo <see cref="T:System.Dynamic.DynamicMetaObject" />.Questo campo è di sola lettura.</summary>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Expression">
      <summary>Espressione che rappresenta <see cref="T:System.Dynamic.DynamicMetaObject" /> durante il processo di associazione dinamica.</summary>
      <returns>Espressione che rappresenta <see cref="T:System.Dynamic.DynamicMetaObject" /> durante il processo di associazione dinamica.</returns>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.GetDynamicMemberNames">
      <summary>Restituisce l'enumerazione di tutti i nomi di membro dinamici.</summary>
      <returns>Elenco dei nomi di membro dinamici.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.HasValue">
      <summary>Ottiene un valore che indica se <see cref="T:System.Dynamic.DynamicMetaObject" /> include il valore di runtime.</summary>
      <returns>Restituisce true se <see cref="T:System.Dynamic.DynamicMetaObject" /> include il valore di runtime; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.LimitType">
      <summary>Ottiene il tipo di limite di <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <returns>
        <see cref="P:System.Dynamic.DynamicMetaObject.RuntimeType" /> se il valore di runtime è disponibile; in caso contrario, un tipo di <see cref="P:System.Dynamic.DynamicMetaObject.Expression" />.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Restrictions">
      <summary>Set di restrizioni relative all'associazione che determinano la validità dell'associazione.</summary>
      <returns>Set di restrizioni relative all'associazione.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.RuntimeType">
      <summary>Ottiene il tipo (<see cref="T:System.Type" />) del valore di runtime oppure restituisce Null se a <see cref="T:System.Dynamic.DynamicMetaObject" /> non è associato alcun valore.</summary>
      <returns>Tipo (<see cref="T:System.Type" />) del valore di runtime oppure Null.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Value">
      <summary>Valore di runtime rappresentato da <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
      <returns>Valore di runtime rappresentato da <see cref="T:System.Dynamic.DynamicMetaObject" />.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObjectBinder">
      <summary>Gestore di associazione del sito di chiamata dinamico che fa parte del protocollo di associazione di <see cref="T:System.Dynamic.DynamicMetaObject" />.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Dynamic.DynamicMetaObjectBinder" />.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>Esegue l'associazione di runtime dell'operazione dinamica su un set di argomenti.</summary>
      <returns>Espressione che esegue test sugli argomenti dell'operazione dinamica ed esegue l'operazione dinamica se i test sono validi.Se i test non riescono su occorrenze successive dell'operazione dinamica, Bind sarà chiamato nuovamente per produrre un nuovo <see cref="T:System.Linq.Expressions.Expression" /> per i nuovi tipi di argomento.</returns>
      <param name="args">Matrice di argomenti per l'operazione dinamica.</param>
      <param name="parameters">Matrice di istanze <see cref="T:System.Linq.Expressions.ParameterExpression" /> che rappresentano i parametri del sito di chiamata nel processo di associazione.</param>
      <param name="returnLabel">LabelTarget utilizzato per restituire il risultato dell'associazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Rinvia l'associazione dell'operazione a un momento successivo, al calcolo dei valori di runtime di tutti gli argomenti dell'operazione dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject[])">
      <summary>Rinvia l'associazione dell'operazione a un momento successivo, al calcolo dei valori di runtime di tutti gli argomenti dell'operazione dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="args">Matrice di argomenti dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.GetUpdateExpression(System.Type)">
      <summary>Ottiene un'espressione che provocherà l'aggiornamento dell'associazione.Indica che l'associazione dell'espressione non è più valida.Viene in genere utilizzato quando la "versione" di un oggetto dinamico è stata modificata.</summary>
      <returns>Espressione di aggiornamento.</returns>
      <param name="type">Proprietà <see cref="P:System.Linq.Expressions.Expression.Type" /> dell'espressione risultante. È consentito qualsiasi tipo.</param>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObjectBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicObject">
      <summary>Fornisce una classe di base per specificare il comportamento dinamico in runtime.La classe deve essere ereditata e non è possibile crearne direttamente un'istanza.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.#ctor">
      <summary>Consente ai tipi derivati di creare una nuova istanza del tipo <see cref="T:System.Dynamic.DynamicObject" />.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetDynamicMemberNames">
      <summary>Restituisce l'enumerazione di tutti i nomi di membro dinamici. </summary>
      <returns>Sequenza che contiene nomi dei membri dinamici.</returns>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>Fornisce un oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che invia ai metodi virtuali dinamici.L'oggetto può essere incapsulato all'interno di un altro oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> per fornire il comportamento personalizzato per singole azioni.Questo metodo supporta l'infrastruttura DLR (Dynamic Language Runtime) per gli implementatori del linguaggio e non è destinato all'utilizzo direttamente dal codice.</summary>
      <returns>Oggetto di tipo <see cref="T:System.Dynamic.DynamicMetaObject" />.</returns>
      <param name="parameter">Espressione che rappresenta l'oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> da inviare ai metodi virtuali dinamici.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Object,System.Object@)">
      <summary>Fornisce l'implementazione per le operazioni binarie.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni quali l'aggiunta e la moltiplicazione.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione binaria.La proprietà binder.Operation restituisce un oggetto <see cref="T:System.Linq.Expressions.ExpressionType" />.Ad esempio, per l'istruzione sum = first + second, dove first e second sono derivate dalla classe DynamicObject, binder.Operation restituisce ExpressionType.Add..</param>
      <param name="arg">Operando destro per l'operazione binaria.Ad esempio, per l'istruzione sum = first + second, dove first e second sono derivate dalla classe DynamicObject, <paramref name="arg" /> è uguale a second.</param>
      <param name="result">Risultato dell'operazione binaria.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
      <summary>Fornisce l'implementazione per le operazioni di conversione dei tipi.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni che eseguono la conversione di un oggetto da un tipo a un altro.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione di conversione.La proprietà binder.Type fornisce il tipo in cui deve essere convertito l'oggetto.Ad esempio, per l'istruzione (String)sampleObject in C# (CType(sampleObject, Type) in Visual Basic), dove sampleObject è un'istanza della classe derivata dalla classe <see cref="T:System.Dynamic.DynamicObject" />, binder.Type restituisce il tipo <see cref="T:System.String" />.La proprietà binder.Explicit fornisce informazioni sul tipo di conversione effettuato.Restituisce true per la conversione esplicita e false per la conversione implicita.</param>
      <param name="result">Tipo di risultato dell'operazione di conversione dei tipi.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryCreateInstance(System.Dynamic.CreateInstanceBinder,System.Object[],System.Object@)">
      <summary>Fornisce l'implementazione per le operazioni che creano una nuova istanza di un oggetto dinamico.Questo metodo non può essere utilizzato in C# o in Visual Basic.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione di inizializzazione.</param>
      <param name="args">Argomenti passati all'oggetto durante l'inizializzazione.Ad esempio, per l'operazione new SampleType(100), dove SampleType è il tipo derivato dalla classe <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="args[0]" /> è uguale a 100.</param>
      <param name="result">Risultato dell'inizializzazione.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Object[])">
      <summary>Fornisce l'implementazione per operazioni che eliminano un oggetto in base all'indice.Questo metodo non può essere utilizzato in C# o in Visual Basic.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione di eliminazione.</param>
      <param name="indexes">Indici da eliminare.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>Fornisce l'implementazione per operazioni che eliminano un membro di un oggetto.Questo metodo non può essere utilizzato in C# o in Visual Basic.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione di eliminazione.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetIndex(System.Dynamic.GetIndexBinder,System.Object[],System.Object@)">
      <summary>Fornisce l'implementazione per operazioni che ottengono un valore in base all'indice.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni di indicizzazione.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata eccezione di runtime.</returns>
      <param name="binder">Fornisce informazioni sull'operazione. </param>
      <param name="indexes">Indici utilizzati nell'operazione.Ad esempio, per l'operazione sampleObject[3] in C# (sampleObject(3) in Visual Basic), dove sampleObject è derivato dalla classe DynamicObject, <paramref name="indexes[0]" /> è uguale a 3.</param>
      <param name="result">Risultato dell'operazione di indicizzazione.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <summary>Fornisce l'implementazione per operazioni che ottengono valori dei membri.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni quale l'acquisizione di un valore per una proprietà.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata eccezione di runtime.</returns>
      <param name="binder">Fornisce informazioni sull'oggetto che ha chiamato l'operazione dinamica.La proprietà binder.Name fornisce il nome del membro di dati su cui viene eseguita l'operazione dinamica.Ad esempio, per l'istruzione Console.WriteLine(sampleObject.SampleProperty), dove sampleObject è un'istanza della classe derivata dalla classe <see cref="T:System.Dynamic.DynamicObject" />, binder.Name restituisce "SampleProperty".La proprietà binder.IgnoreCase specifica se per il nome del membro viene applicata la distinzione tra maiuscole e minuscole.</param>
      <param name="result">Risultato dell'operazione get.Ad esempio, se il metodo viene chiamato per una proprietà, è possibile assegnare il valore della proprietà a <paramref name="result" />.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvoke(System.Dynamic.InvokeBinder,System.Object[],System.Object@)">
      <summary>Fornisce l'implementazione per operazioni che richiamano un oggetto.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni quale il richiamo di un oggetto o un delegato.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione invoke.</param>
      <param name="args">Argomenti passati all'oggetto durante l'operazione invoke.Ad esempio, per l'operazione sampleObject(100), dove sampleObject è derivato dalla classe <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="args[0]" /> è uguale a 100.</param>
      <param name="result">Risultato della chiamata all'oggetto.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
      <summary>Fornisce l'implementazione per operazioni che richiamano un membro.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni quale la chiamata a un metodo.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione dinamica.La proprietà binder.Name fornisce il nome del membro di dati su cui viene eseguita l'operazione dinamica.Ad esempio, per l'istruzione sampleObject.SampleMethod(100), dove sampleObject è un'istanza della classe derivata dalla classe <see cref="T:System.Dynamic.DynamicObject" />, binder.Name restituisce "SampleMethod".La proprietà binder.IgnoreCase specifica se per il nome del membro viene applicata la distinzione tra maiuscole e minuscole.</param>
      <param name="args">Argomenti passati al membro dell'oggetto durante l'operazione invoke.Ad esempio, per l'istruzione sampleObject.SampleMethod(100), dove sampleObject è derivato dalla classe <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="args[0]" /> è uguale a 100.</param>
      <param name="result">Risultato della chiamata al membro.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetIndex(System.Dynamic.SetIndexBinder,System.Object[],System.Object)">
      <summary>Fornisce l'implementazione per operazioni che impostano un valore in base all'indice.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni che accedono a oggetti in base a un indice specificato.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione. </param>
      <param name="indexes">Indici utilizzati nell'operazione.Ad esempio, per l'operazione sampleObject[3] = 10 in C# (sampleObject(3) = 10 in Visual Basic), dove sampleObject è derivato dalla classe <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="indexes[0]" /> è uguale a 3.</param>
      <param name="value">Valore da impostare per l'oggetto con l'indice specificato.Ad esempio, per l'operazione sampleObject[3] = 10 in C# (sampleObject(3) = 10 in Visual Basic), dove sampleObject è derivato dalla classe <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="value" /> è uguale a 10.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
      <summary>Fornisce l'implementazione per operazioni che impostano valori dei membri.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni quale l'impostazione di un valore per una proprietà.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'oggetto che ha chiamato l'operazione dinamica.La proprietà binder.Name fornisce il nome del membro a cui viene assegnato il valore.Ad esempio, per l'istruzione sampleObject.SampleProperty = "Test", dove sampleObject è un'istanza della classe derivata dalla classe <see cref="T:System.Dynamic.DynamicObject" />, binder.Name restituisce "SampleProperty".La proprietà binder.IgnoreCase specifica se per il nome del membro viene applicata la distinzione tra maiuscole e minuscole.</param>
      <param name="value">Valore su cui impostare il membro.Ad esempio, per l'istruzione sampleObject.SampleProperty = "Test", dove sampleObject è un'istanza della classe derivata dalla classe <see cref="T:System.Dynamic.DynamicObject" />, <paramref name="value" /> è "Test".</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryUnaryOperation(System.Dynamic.UnaryOperationBinder,System.Object@)">
      <summary>Fornisce l'implementazione per le operazioni unarie.Le classi derivate dalla classe <see cref="T:System.Dynamic.DynamicObject" /> possono eseguire l'override di questo metodo per specificare il comportamento dinamico per operazioni quale negazione, incremento o decremento.</summary>
      <returns>true se l'operazione ha esito positivo; in caso contrario, false.Se questo metodo restituisce false, il comportamento viene determinato dal gestore di associazione di runtime del linguaggio. Nella maggior parte dei casi viene generata un'eccezione di runtime specifica del linguaggio.</returns>
      <param name="binder">Fornisce informazioni sull'operazione unaria.La proprietà binder.Operation restituisce un oggetto <see cref="T:System.Linq.Expressions.ExpressionType" />.Ad esempio, per l'istruzione negativeNumber = -number, dove number e derivato dalla classe DynamicObject, binder.Operation restituisce "Negate".</param>
      <param name="result">Risultato dell'operazione unaria.</param>
    </member>
    <member name="T:System.Dynamic.ExpandoObject">
      <summary>Rappresenta un oggetto i cui membri possono essere aggiunti e rimossi in modo dinamico in fase di esecuzione.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.#ctor">
      <summary>Crea un nuovo oggetto ExpandoObject che non ha membri.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Aggiunge il valore specificato all'oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> con la chiave specificata.</summary>
      <param name="item">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> che rappresenta la chiave e il valore da aggiungere alla raccolta.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Rimuove tutti gli elementi dalla raccolta.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Stabilisce se l'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> contiene una coppia chiave/valore specifica.</summary>
      <returns>È true se la raccolta contiene una chiave e un valore specifici. In caso contrario, false.</returns>
      <param name="item">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> da individuare nell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" />.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.Generic.ICollection`1" /> in una matrice di tipo <see cref="T:System.Collections.Generic.KeyValuePair`2" />, iniziando dall'indice di matrice specificato.</summary>
      <param name="array">Matrice unidimensionale di tipo<see cref="T:System.Collections.Generic.KeyValuePair`2" /> che costituisce la destinazione degli elementi <see cref="T:System.Collections.Generic.KeyValuePair`2" /> copiati dall'oggetto <see cref="T:System.Collections.Generic.ICollection`1" />.L'indicizzazione della matrice deve essere in base zero.</param>
      <param name="arrayIndex">Indice in base zero in <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Count">
      <summary>Ottiene il numero di elementi in <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>Numero di elementi nella <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ottiene un valore che indica se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura.</summary>
      <returns>true se <see cref="T:System.Collections.Generic.ICollection`1" /> è di sola lettura. In caso contrario, false.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>Rimuove una chiave e un valore dalla raccolta.</summary>
      <returns>true se la chiave e il valore vengono trovati e rimossi. In caso contrario, false.Questo metodo restituisce false se la chiave e il valore non vengono trovati in <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="item">Struttura <see cref="T:System.Collections.Generic.KeyValuePair`2" /> che rappresenta la chiave e il valore da rimuovere dalla raccolta.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Add(System.String,System.Object)">
      <summary>Aggiunge la chiave e il valore specificati al dizionario.</summary>
      <param name="key">Oggetto da utilizzare come chiave.</param>
      <param name="value">Oggetto da utilizzare come valore.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#ContainsKey(System.String)">
      <summary>Determina se il dizionario contiene la chiave specificata.</summary>
      <returns>true se il dizionario contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave da individuare nel dizionario.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Item(System.String)">
      <summary>Ottiene o imposta l'elemento che dispone della chiave specificata.</summary>
      <returns>Elemento che dispone della chiave specificata.</returns>
      <param name="key">Chiave dell'elemento da ottenere o impostare.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Ottiene un oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> che contiene le chiavi di <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> che contiene le chiavi dell'oggetto che implementa <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(System.String)">
      <summary>Rimuove l'elemento con la chiave specificata dall'oggetto <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>true se l'elemento viene rimosso; in caso contrario, false.Questo metodo restituisce anche false se <paramref name="key" /> non è stato trovato nell'interfaccia <see cref="T:System.Collections.Generic.IDictionary`2" /> originale.</returns>
      <param name="key">Chiave dell'elemento da rimuovere.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#TryGetValue(System.String,System.Object@)">
      <summary>Ottiene il valore associato alla chiave specificata.</summary>
      <returns>true se l'oggetto che implementa <see cref="T:System.Collections.Generic.IDictionary`2" /> contiene un elemento con la chiave specificata; in caso contrario, false.</returns>
      <param name="key">Chiave del valore da ottenere.</param>
      <param name="value">Quando termina, questo metodo restituisce il valore associato alla chiave specificata nel caso in cui la chiave venga trovata. In caso contrario, restituisce il valore predefinito per il tipo del parametro <paramref name="value" />.Questo parametro viene passato non inizializzato.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Ottiene un oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> che contiene i valori di <see cref="T:System.Collections.Generic.IDictionary`2" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.ICollection`1" /> che contiene i valori dell'oggetto che implementa <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.Generic.IEnumerator`1" /> che può essere utilizzato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere la raccolta.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzata per scorrere la raccolta.</returns>
    </member>
    <member name="E:System.Dynamic.ExpandoObject.System#ComponentModel#INotifyPropertyChanged#PropertyChanged">
      <summary>Generato quando il valore di una proprietà cambia.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Dynamic#IDynamicMetaObjectProvider#GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>Il MetaObject fornito verrà inviato ai metodi virtuali dinamici.L'oggetto può essere incapsulato all'interno di un altro MetaObject per fornire comportamento personalizzato per singole azioni.</summary>
      <returns>Oggetto del tipo <see cref="T:System.Dynamic.DynamicMetaObject" />.</returns>
      <param name="parameter">Espressione che rappresenta il MetaObject da inviare ai metodi virtuali dinamici.</param>
    </member>
    <member name="T:System.Dynamic.GetIndexBinder">
      <summary>Rappresenta l'operazione get dinamica sull'indice nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.GetIndexBinder" />.</summary>
      <param name="callInfo">Firma degli argomenti nel sito di chiamata.</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione get dinamica sull'indice.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione get dinamica sull'indice.</param>
      <param name="args">Matrice di argomenti dell'operazione get dinamica sull'indice.</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.CallInfo">
      <summary>Ottiene la firma degli argomenti nel sito di chiamata.</summary>
      <returns>Firma degli argomenti nel sito di chiamata.</returns>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione get dinamica sull'indice se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione get dinamica sull'indice.</param>
      <param name="indexes">Argomenti dell'operazione get dinamica sull'indice.</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione get dinamica sull'indice se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione get dinamica sull'indice.</param>
      <param name="indexes">Argomenti dell'operazione get dinamica sull'indice.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.GetMemberBinder">
      <summary>Rappresenta l'operazione get dinamica sul membro nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.GetMemberBinder" />.</summary>
      <param name="name">Nome del membro da ottenere.</param>
      <param name="ignoreCase">Restituisce true se nella corrispondenza del nome deve essere ignorata la distinzione tra maiuscole e minuscole; in caso contrario, false.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione get dinamica sul membro.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione get dinamica sul membro.</param>
      <param name="args">Matrice di argomenti dell'operazione get dinamica sul membro.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione get dinamica sul membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione get dinamica sul membro.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione get dinamica sul membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione get dinamica sul membro.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.IgnoreCase">
      <summary>Ottiene il valore che indica se nel confronto tra stringhe deve essere ignorata la distinzione tra maiuscole e minuscole per il nome del membro.</summary>
      <returns>Restituisce true la distinzione tra maiuscole e minuscole viene ignorata; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.Name">
      <summary>Ottiene il nome del membro da ottenere.</summary>
      <returns>Nome del membro da ottenere.</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.IDynamicMetaObjectProvider">
      <summary>Rappresenta un oggetto dinamico le cui operazioni possono essere associate in fase di esecuzione.</summary>
    </member>
    <member name="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>Restituisce l'oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> responsabile delle operazioni di associazione eseguite sull'oggetto corrente.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> da associare all'oggetto corrente.</returns>
      <param name="parameter">Rappresentazione della struttura ad albero dell'espressione del valore di runtime.</param>
    </member>
    <member name="T:System.Dynamic.IInvokeOnGetBinder">
      <summary>Rappresenta le informazioni su un'operazione get dinamica sul membro che indica se devono essere richiamate le proprietà quando si esegue l'operazione get.</summary>
    </member>
    <member name="P:System.Dynamic.IInvokeOnGetBinder.InvokeOnGet">
      <summary>Ottiene il valore che indica se l'operazione get sul membro deve richiamare le proprietà quando eseguono l'operazione get.Il valore predefinito quando l'interfaccia non è presente è true.</summary>
      <returns>True se l'operazione get sul membro deve richiamare le proprietà quando eseguono l'operazione get; in caso contrario, false.</returns>
    </member>
    <member name="T:System.Dynamic.InvokeBinder">
      <summary>Rappresenta l'operazione invoke dinamica nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.InvokeBinder" />.</summary>
      <param name="callInfo">Firma degli argomenti nel sito di chiamata.</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione invoke dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione invoke dinamica.</param>
      <param name="args">Matrice di argomenti dell'operazione invoke dinamica.</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.CallInfo">
      <summary>Ottiene la firma degli argomenti nel sito di chiamata.</summary>
      <returns>Firma degli argomenti nel sito di chiamata.</returns>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione invoke dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione invoke dinamica.</param>
      <param name="args">Argomenti dell'operazione invoke dinamica.</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione invoke dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione invoke dinamica.</param>
      <param name="args">Argomenti dell'operazione invoke dinamica.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.InvokeMemberBinder">
      <summary>Rappresenta l'operazione invoke dinamica sul membro nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.#ctor(System.String,System.Boolean,System.Dynamic.CallInfo)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.InvokeMemberBinder" />.</summary>
      <param name="name">Nome del membro da richiamare,</param>
      <param name="ignoreCase">Restituisce true se nella corrispondenza del nome deve essere ignorata la distinzione tra maiuscole e minuscole; in caso contrario, false.</param>
      <param name="callInfo">Firma degli argomenti nel sito di chiamata.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione invoke dinamica sul membro.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione invoke dinamica sul membro.</param>
      <param name="args">Matrice di argomenti dell'operazione invoke dinamica sul membro.</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.CallInfo">
      <summary>Ottiene la firma degli argomenti nel sito di chiamata.</summary>
      <returns>Firma degli argomenti nel sito di chiamata.</returns>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione invoke dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione invoke dinamica.</param>
      <param name="args">Argomenti dell'operazione invoke dinamica.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione invoke dinamica sul membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione invoke dinamica sul membro.</param>
      <param name="args">Argomenti dell'operazione invoke dinamica sul membro.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione invoke dinamica sul membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione invoke dinamica sul membro.</param>
      <param name="args">Argomenti dell'operazione invoke dinamica sul membro.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.IgnoreCase">
      <summary>Ottiene il valore che indica se nel confronto tra stringhe deve essere ignorata la distinzione tra maiuscole e minuscole per il nome del membro.</summary>
      <returns>Restituisce true la distinzione tra maiuscole e minuscole viene ignorata; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.Name">
      <summary>Ottiene il nome del membro da richiamare.</summary>
      <returns>Nome del membro da richiamare,</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.SetIndexBinder">
      <summary>Rappresenta l'operazione dinamica di impostazione dell'indice nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.SetIndexBinder" />.</summary>
      <param name="callInfo">Firma degli argomenti nel sito di chiamata.</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione dinamica di impostazione dell'indice.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di impostazione dell'indice.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica di impostazione dell'indice.</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.CallInfo">
      <summary>Ottiene la firma degli argomenti nel sito di chiamata.</summary>
      <returns>Firma degli argomenti nel sito di chiamata.</returns>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione dinamica di impostazione dell'indice se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di impostazione dell'indice.</param>
      <param name="indexes">Argomenti dell'operazione dinamica di impostazione dell'indice.</param>
      <param name="value">Valore su cui impostare la raccolta.</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Se sottoposto a override nella classe derivata, esegue l'associazione dell'operazione dinamica di impostazione dell'indice se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di impostazione dell'indice.</param>
      <param name="indexes">Argomenti dell'operazione dinamica di impostazione dell'indice.</param>
      <param name="value">Valore su cui impostare la raccolta.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.SetMemberBinder">
      <summary>Rappresenta l'operazione dinamica di impostazione del membro nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Dynamic.SetMemberBinder" />.</summary>
      <param name="name">Nome del membro da ottenere.</param>
      <param name="ignoreCase">Restituisce true se nella corrispondenza del nome deve essere ignorata la distinzione tra maiuscole e minuscole; in caso contrario, false.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione dinamica di impostazione del membro.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di impostazione del membro.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica di impostazione del membro.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione dinamica di impostazione del membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di impostazione del membro.</param>
      <param name="value">Valore su cui impostare il membro.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione dinamica di impostazione del membro se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica di impostazione del membro.</param>
      <param name="value">Valore su cui impostare il membro.</param>
      <param name="errorSuggestion">Risultato dell'associazione da utilizzare se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.IgnoreCase">
      <summary>Ottiene il valore che indica se nel confronto tra stringhe deve essere ignorata la distinzione tra maiuscole e minuscole per il nome del membro.</summary>
      <returns>Restituisce true la distinzione tra maiuscole e minuscole viene ignorata; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.Name">
      <summary>Ottiene il nome del membro da ottenere.</summary>
      <returns>Nome del membro da ottenere.</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Dynamic.UnaryOperationBinder">
      <summary>Rappresenta l'operazione dinamica unaria nel sito di chiamata, fornendo la semantica di associazione e i dettagli sull'operazione.</summary>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Dynamic.BinaryOperationBinder" />.</summary>
      <param name="operation">Tipo di operazione unaria.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>Esegue l'associazione dell'operazione unaria dinamica.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione dinamica.</param>
      <param name="args">Matrice di argomenti dell'operazione dinamica.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione unaria dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione unaria dinamica.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>Esegue l'associazione dell'operazione unaria dinamica se l'oggetto dinamico di destinazione non può eseguire l'associazione.</summary>
      <returns>Oggetto <see cref="T:System.Dynamic.DynamicMetaObject" /> che rappresenta il risultato dell'associazione.</returns>
      <param name="target">Destinazione dell'operazione unaria dinamica.</param>
      <param name="errorSuggestion">Risultato dell'associazione se l'associazione non riesce, oppure null.</param>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.Operation">
      <summary>Tipo di operazione unaria.</summary>
      <returns>Oggetto di <see cref="T:System.Linq.Expressions.ExpressionType" /> che rappresenta il tipo di operazione unaria.</returns>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.ReturnType">
      <summary>Tipo di risultato dell'operazione.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di risultato dell'operazione.</returns>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpression">
      <summary>Rappresenta un'operazione dinamica.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>Invia al metodo Visit specifico per questo tipo di nodo.Ad esempio, <see cref="T:System.Linq.Expressions.MethodCallExpression" /> chiama <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />.</summary>
      <returns>Risultato della visita di questo nodo.</returns>
      <param name="visitor">Visitatore con cui visitare questo nodo.</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Arguments">
      <summary>Porta gli argomenti all'operazione dinamica.</summary>
      <returns>Le raccolte di sola lettura che contengono gli argomenti all'operazione dinamica.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Binder">
      <summary>Ottiene <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> che determina il comportamento in fase di esecuzione del sito dinamico.</summary>
      <returns>
        <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />che determina il comportamento di runtime del sito dinamico.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.DelegateType">
      <summary>Ottiene il tipo di delegato usato dal <see cref="T:System.Runtime.CompilerServices.CallSite" />.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che rappresenta il tipo di delegato usato dal  <see cref="T:System.Runtime.CompilerServices.CallSite" />.</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> con la proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> impostate sui valori specificati.</returns>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="returnType">Tipo di risultato dell'espressione dinamica.</param>
      <param name="arguments">Argomenti per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> con la proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> impostate sui valori specificati.</returns>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="returnType">Tipo di risultato dell'espressione dinamica.</param>
      <param name="arg0">Primo argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> con la proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="returnType">Tipo di risultato dell'espressione dinamica.</param>
      <param name="arg0">Primo argomento per l'operazione dinamica.</param>
      <param name="arg1">Secondo argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> con la proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="returnType">Tipo di risultato dell'espressione dinamica.</param>
      <param name="arg0">Primo argomento per l'operazione dinamica.</param>
      <param name="arg1">Secondo argomento per l'operazione dinamica.</param>
      <param name="arg2">Terzo argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> con la proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="returnType">Tipo di risultato dell'espressione dinamica.</param>
      <param name="arg0">Primo argomento per l'operazione dinamica.</param>
      <param name="arg1">Secondo argomento per l'operazione dinamica.</param>
      <param name="arg2">Terzo argomento per l'operazione dinamica.</param>
      <param name="arg3">Quarto argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression[])">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> con la proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="returnType">Tipo di risultato dell'espressione dinamica.</param>
      <param name="arguments">Argomenti per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> la cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> è uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="delegateType">Tipo del delegato utilizzato dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="arguments">Argomenti per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato e da un argomento.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> la cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> è uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="delegateType">Tipo del delegato utilizzato dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="arg0">Argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato e da due argomenti.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> la cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> è uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="delegateType">Tipo del delegato utilizzato dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="arg0">Primo argomento per l'operazione dinamica.</param>
      <param name="arg1">Secondo argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato e da tre argomenti.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> la cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> è uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="delegateType">Tipo del delegato utilizzato dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="arg0">Primo argomento per l'operazione dinamica.</param>
      <param name="arg1">Secondo argomento per l'operazione dinamica.</param>
      <param name="arg2">Terzo argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato e da quattro argomenti.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> la cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> è uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="delegateType">Tipo del delegato utilizzato dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="arg0">Primo argomento per l'operazione dinamica.</param>
      <param name="arg1">Secondo argomento per l'operazione dinamica.</param>
      <param name="arg2">Terzo argomento per l'operazione dinamica.</param>
      <param name="arg3">Quarto argomento per l'operazione dinamica.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression[])">
      <summary>Crea un oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> che rappresenta un'operazione dinamica associata dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> specificato.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.DynamicExpression" /> la cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" /> è uguale a <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" /> e le cui proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> e <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> sono impostate sui valori specificati.</returns>
      <param name="delegateType">Tipo del delegato utilizzato dall'oggetto <see cref="T:System.Runtime.CompilerServices.CallSite" />.</param>
      <param name="binder">Gestore di associazione di runtime per l'operazione dinamica.</param>
      <param name="arguments">Argomenti per l'operazione dinamica.</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.NodeType">
      <summary>Restituisce il tipo di nodo di questa espressione.I nodi di estensione devono restituire <see cref="F:System.Linq.Expressions.ExpressionType.Extension" /> quando si esegue l'override di questo metodo.</summary>
      <returns>Classe <see cref="T:System.Linq.Expressions.ExpressionType" /> dell'espressione.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#CreateCallSite"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#Rewrite(System.Linq.Expressions.Expression[])"></member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Type">
      <summary>Ottiene il tipo statico dell'espressione rappresentata da <see cref="T:System.Linq.Expressions.Expression" />.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.Type" /> che rappresenta il tipo statico dell'espressione.</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>Confronta il valore inviato al parametro arguments, alla proprietà Arguments dell'istanza corrente di DynamicExpression.Se i valori di parametro e della proprietà sono uguali, viene restituita l'istanza corrente.Se non sono uguali, viene restituita una nuova istanza di DynamicExpression identica all'istanza corrente con la differenza che la proprietà Arguments è impostata sul valore del parametro arguments.</summary>
      <returns>Espressione corrente se non viene modificato alcun elemento figlio o espressione con gli elementi figlio aggiornati.</returns>
      <param name="arguments">Proprietà <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> del risultato.</param>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpressionVisitor">
      <summary>Rappresenta un visitatore o un rewriter per gli alberi delle espressioni dinamici.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.#ctor">
      <summary>Inizializza una nuova istanza di <see cref="T:System.Linq.Expressions.DynamicExpressionVisitor" />.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.VisitDynamic(System.Linq.Expressions.DynamicExpression)">
      <summary>Visita gli elementi figlio di <see cref="T:System.Linq.Expressions.DynamicExpression" />.</summary>
      <returns>Restituisce <see cref="T:System.Linq.Expressions.Expression" />, l'espressione modificata, se l'espressione stessa o una delle relative sottoespressioni è stata modificata; in caso contrario, restituisce l'espressione originale.</returns>
      <param name="node">Espressione da visitare.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite">
      <summary>Una classe di base del sito di chiamata dinamica.Questo tipo viene utilizzato come tipo di parametro ai siti dinamici di destinazione.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite.Binder">
      <summary>Classe responsabile per l'associazione di operazioni dinamiche sul sito dinamico.</summary>
      <returns>Oggetto <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> responsabile per l'associazione di operazioni dinamiche.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite.Create(System.Type,System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>Crea un sito di chiamata con il tipo delegato specificato e il gestore di associazione.</summary>
      <returns>Nuovo sito di chiamata.</returns>
      <param name="delegateType">Tipo delegato del sito di chiamata.</param>
      <param name="binder">Gestore di associazione del sito di chiamata.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite`1">
      <summary>Tipo di sito dinamico.</summary>
      <typeparam name="T">Tipo di delegato.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite`1.Create(System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>Crea un'istanza del sito di chiamata dinamico, inizializzata con il gestore di associazione responsabile per l'associazione di runtime delle operazioni dinamiche in questo sito di chiamata.</summary>
      <returns>Nuova istanza del sito di chiamata dinamico.</returns>
      <param name="binder">Gestore di associazione responsabile per l'associazione di runtime delle operazioni dinamiche in questo sito di chiamata.</param>
    </member>
    <member name="F:System.Runtime.CompilerServices.CallSite`1.Target">
      <summary>Cache di livello 0 - delegato specializzato basato sulla cronologia del sito.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite`1.Update">
      <summary>Delegato dell'aggiornamento.Chiamato quando il sito dinamico sperimenta una riga di accesso alla cache non eseguita.</summary>
      <returns>Delegato dell'aggiornamento.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteBinder">
      <summary>Classe responsabile per l'associazione di runtime delle operazioni dinamiche sul sito dinamico di chiamata.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>Esegue l'associazione di runtime dell'operazione dinamica su un set di argomenti.</summary>
      <returns>Espressione che esegue test sugli argomenti dell'operazione dinamica ed esegue l'operazione dinamica se i test sono validi.Se i test non riescono su occorrenze successive dell'operazione dinamica, Bind sarà chiamato nuovamente per produrre un nuovo <see cref="T:System.Linq.Expressions.Expression" /> per i nuovi tipi di argomento.</returns>
      <param name="args">Matrice di argomenti per l'operazione dinamica.</param>
      <param name="parameters">Matrice di istanze <see cref="T:System.Linq.Expressions.ParameterExpression" /> che rappresentano i parametri del sito di chiamata nel processo di associazione.</param>
      <param name="returnLabel">LabelTarget utilizzato per restituire il risultato dell'associazione dinamica.</param>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.BindDelegate``1(System.Runtime.CompilerServices.CallSite{``0},System.Object[])">
      <summary>Fornisce supporto dell'associazione di runtime di basso livello.Le classi possono eseguire l'override di questo e fornire un delegato diretto per l'implementazione di regola.Può attivare regole del salvataggio su disco, disponendo di regole specializzate in fase di esecuzione o fornendo diversi criteri di memorizzazione nella cache.</summary>
      <returns>Nuovo delegato che sostituisce la destinazione di CallSite.</returns>
      <param name="site">CallSite per il quale viene eseguita l'associazione.</param>
      <param name="args">Argomenti per il gestore di associazione.</param>
      <typeparam name="T">Tipo destinazione di CallSite.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.CacheTarget``1(``0)">
      <summary>Aggiunge una destinazione alla cache di destinazioni note.Le destinazioni memorizzate nella cache saranno analizzate prima di chiamare BindDelegate per produrre la nuova regola.</summary>
      <param name="target">Delegato destinazione da aggiungere alla cache.</param>
      <typeparam name="T">Tipo di destinazione da aggiungere.</typeparam>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSiteBinder.UpdateLabel">
      <summary>Ottiene un'etichetta che può essere utilizzata per determinare l'aggiornamento dell'associazione.Indica che l'associazione dell'espressione non è più valida.Viene in genere utilizzato quando la "versione" di un oggetto dinamico è stata modificata.</summary>
      <returns>Oggetto <see cref="T:System.Linq.Expressions.LabelTarget" /> che rappresenta un'etichetta che può essere utilizzata per lanciare l'aggiornamento dell'associazione.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteHelpers">
      <summary>Classe che contiene metodi di supporto per DLR CallSites.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteHelpers.IsInternalFrame(System.Reflection.MethodBase)">
      <summary>Controlla se un oggetto <see cref="T:System.Reflection.MethodBase" /> è utilizzato internamente da DLR e non deve essere visualizzato sullo stack del codice del linguaggio.</summary>
      <returns>True se un oggetto <see cref="T:System.Reflection.MethodBase" /> di input è utilizzato internamente da DLR e non deve essere visualizzato sullo stack del codice del linguaggio.In caso contrario, false.</returns>
      <param name="mb">
        <see cref="T:System.Reflection.MethodBase" /> di input</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.DynamicAttribute">
      <summary>Indica che è l'utilizzo di <see cref="T:System.Object" /> su un membro va trattato come tipo inviato dinamicamente.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" />.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor(System.Boolean[])">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" />.</summary>
      <param name="transformFlags">Specifica, in un prefisso traversal della costruzione di un tipo, quali occorrenze <see cref="T:System.Object" /> vanno trattate come tipo dinamicamente inviato.</param>
    </member>
    <member name="P:System.Runtime.CompilerServices.DynamicAttribute.TransformFlags">
      <summary>Specifica, in un prefisso traversal della costruzione di un tipo, quali occorrenze <see cref="T:System.Object" /> vanno trattate come tipo dinamicamente inviato.</summary>
      <returns>Elenco di occorrenze di <see cref="T:System.Object" /> da trattare come tipo inviato dinamicamente.</returns>
    </member>
  </members>
</doc>