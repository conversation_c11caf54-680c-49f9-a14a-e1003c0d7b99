﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Web;
using Wuqi.Webdiyer;
using SplendidCRM.TGSAP;
using SplendidCRM._code;
using SplendidCRM._code.TGSAP;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;

namespace SplendidCRM.UtilityBillPay.Config
{
    public class DictType : SplendidControl
    {
        protected void Page_Command(object sender, CommandEventArgs e)
        {
            try
            {

            }
            catch (Exception ex)
            {
                SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
            }
        }

        private void Page_Load(object sender, System.EventArgs e)
        {
          
        }

        #region Web Form Designer generated code
        override protected void OnInit(EventArgs e)
        {
            //
            // CODEGEN: This call is required by the ASP.NET Web Form Designer.
            //
            InitializeComponent();
            base.OnInit(e);
        }

        private void InitializeComponent()
        {
            this.Load += new System.EventHandler(this.Page_Load);
            m_sMODULE = "Config";
            SetMenu(m_sMODULE);
        }
        #endregion
    }
}








                        
        
        