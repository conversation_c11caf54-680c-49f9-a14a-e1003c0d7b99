﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>Fornisce i dati per l'evento NomeMetodoCompleted.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />. </summary>
      <param name="error">Qualsiasi errore verificatosi durante l'esecuzione dell'operazione asincrona.</param>
      <param name="cancelled">Valore che indica se l'operazione asincrona è stata annullata.</param>
      <param name="userState">Oggetto di stato facoltativo fornito dall'utente passato al metodo <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />.</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>Ottiene un valore che indica se un'operazione asincrona è stata annullata.</summary>
      <returns>true se l'operazione in background è stata annullata; in caso contrario, falseIl valore predefinito è false.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>Ottiene un valore che indica l'errore verificatosi durante un'operazione asincrona.</summary>
      <returns>Un'istanza dell'oggetto <see cref="T:System.Exception" />, se si è verificato un errore durante un'operazione asincrona; in caso contrario null.</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>Genera un'eccezione fornita dall'utente in caso di errore in un'operazione asincrona.</summary>
      <exception cref="T:System.InvalidOperationException">La proprietà <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> è true. </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">La proprietà <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> è stata impostata dall'operazione asincrona.La proprietà <see cref="P:System.Exception.InnerException" /> contiene un riferimento a <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>Ottiene l’identificatore univoco per l'attività asincrona.</summary>
      <returns>Un riferimento a un oggetto che identifica in modo univoco l'attività asincrona. In caso contrario, null se non è stato impostato alcun valore.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>Rappresenta il metodo che gestirà l'evento NomeMetodoCompleted di un'operazione asincrona.</summary>
      <param name="sender">Origine dell’evento. </param>
      <param name="e">Oggetto <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> che contiene i dati dell'evento. </param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>Rileva la durata di un'operazione asincrona.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>Finalizza l'operazione asincrona.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>Pone fine alla durata di un'operazione asincrona.</summary>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> è già stato chiamato per l'attività. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Richiama un delegato nel thread o contesto adeguato al modello di applicazione.</summary>
      <param name="d">Oggetto <see cref="T:System.Threading.SendOrPostCallback" /> che esegue il wrapping del delegato da chiamare al completamento dell'operazione. </param>
      <param name="arg">Argomento per il delegato contenuto nel parametro <paramref name="d" />. </param>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> è già stato chiamato per l'attività. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> è null. </exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Pone fine alla durata di un'operazione asincrona.</summary>
      <param name="d">Oggetto <see cref="T:System.Threading.SendOrPostCallback" /> che esegue il wrapping del delegato da chiamare al completamento dell'operazione. </param>
      <param name="arg">Argomento per il delegato contenuto nel parametro <paramref name="d" />. </param>
      <exception cref="T:System.InvalidOperationException">Il metodo <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> è già stato chiamato per l'attività. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> è null. </exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>Ottiene l'oggetto <see cref="T:System.Threading.SynchronizationContext" /> passato al costruttore.</summary>
      <returns>Oggetto <see cref="T:System.Threading.SynchronizationContext" /> passato al costruttore.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>Ottiene o imposta un oggetto usato per identificare in modo univoco un'operazione asincrona.</summary>
      <returns>Oggetto di stato passato alla chiamata di metodo asincrona.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>Fornisce gestione della concorrenza per le classi che supportano le chiamate asincrone.La classe non può essere ereditata.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>Restituisce un oggetto <see cref="T:System.ComponentModel.AsyncOperation" /> per rilevare la durata di una particolare operazione asincrona.</summary>
      <returns>Un oggetto <see cref="T:System.ComponentModel.AsyncOperation" /> utilizzabile per tenere traccia della durata di una chiamata asincrona a un metodo.</returns>
      <param name="userSuppliedState">Oggetto utilizzato per associare informazioni sullo stato del client, ad esempio un ID attività, a un'operazione asincrona specifica. </param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>Ottiene o imposta il contesto di sincronizzazione per l'operazione asincrona.</summary>
      <returns>Il contesto di sincronizzazione per l'operazione asincrona.</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>Esegue un'operazione su un thread separato.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.ComponentModel.BackgroundWorker" />.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>Richiede l'annullamento di un'operazione in background in attesa.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> è false. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>Ottiene un valore che indica se l'applicazione ha richiesto l'annullamento di un'operazione in background.</summary>
      <returns>true se l'applicazione ha richiesto l'annullamento di un operazione in background; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>Si verifica quando viene chiamato il metodo <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" />.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>Ottiene un valore che indica se <see cref="T:System.ComponentModel.BackgroundWorker" /> sta eseguendo un'operazione asincrona.</summary>
      <returns>true se la classe <see cref="T:System.ComponentModel.BackgroundWorker" /> sta eseguendo un'operazione asincrona; in caso contrario false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>Genera l'evento <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />. </summary>
      <param name="e">Oggetto <see cref="T:System.EventArgs" /> che contiene i dati dell'evento.</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>Genera l'evento <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="e">Oggetto <see cref="T:System.EventArgs" /> che contiene i dati dell'evento. </param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>Genera l'evento <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" />.</summary>
      <param name="e">Oggetto <see cref="T:System.EventArgs" /> che contiene i dati dell'evento. </param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>Si verifica quando viene chiamato il metodo <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" />.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>Genera l'evento <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="percentProgress">La percentuale di completamento, da 0 a 100, dell'operazione in background. </param>
      <exception cref="T:System.InvalidOperationException">La proprietà <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> è impostata su false. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>Genera l'evento <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="percentProgress">La percentuale di completamento, da 0 a 100, dell'operazione in background.</param>
      <param name="userState">L'oggetto di stato passato a <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />.</param>
      <exception cref="T:System.InvalidOperationException">La proprietà <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> è impostata su false. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>Avvia l'esecuzione di un'operazione in background.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> è true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>Avvia l'esecuzione di un'operazione in background.</summary>
      <param name="argument">Parametro che deve essere utilizzato dall'operazione in background da eseguire nell'evento <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> è true. </exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>Viene generato quando l'operazione in background è stata annullata o ha generato un'eccezione.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>Ottiene o imposta un valore che indica se la classe <see cref="T:System.ComponentModel.BackgroundWorker" /> è in grado di segnalare gli aggiornamenti dello stato.</summary>
      <returns>true se la classe <see cref="T:System.ComponentModel.BackgroundWorker" /> supporta gli aggiornamenti dello stato; in caso contrario false.Il valore predefinito è false.</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>Ottiene o imposta un valore che indica se la classe <see cref="T:System.ComponentModel.BackgroundWorker" /> supporta l'annullamento asincrono.</summary>
      <returns>true se <see cref="T:System.ComponentModel.BackgroundWorker" /> supporta l'annullamento; in caso contrario, false.Il valore predefinito è false.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>Fornisce i dati per il gestore eventi <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.ComponentModel.DoWorkEventArgs" />.</summary>
      <param name="argument">Specifica un argomento per un'operazione asincrona.</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>Ottiene un valore che rappresenta l'argomento di un'operazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Object" /> che rappresenta l'argomento di un'operazione asincrona.</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>Ottiene o imposta un valore che rappresenta il risultato di un'operazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Object" /> che rappresenta il risultato di un'operazione asincrona.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>Rappresenta il metodo in base al quale verrà gestito l'evento <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />.La classe non può essere ereditata.</summary>
      <param name="sender">Origine dell’evento.</param>
      <param name="e">Oggetto <see cref="T:System.ComponentModel.DoWorkEventArgs" /> che contiene i dati dell'evento.</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>Fornisce dati per l'evento <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />.</summary>
      <param name="progressPercentage">Percentuale dell'attività asincrona già completata.</param>
      <param name="userState">Stato utente univoco.</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>Ottiene la percentuale di avanzamento dell'attività asincrona.</summary>
      <returns>Valore percentuale che indica l'avanzamento dell'attività asincrona.</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>Ottiene uno stato utente univoco.</summary>
      <returns>Oggetto <see cref="T:System.Object" /> univoco che indica lo stato utente.</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>Rappresenta il metodo da cui verrà gestito l'evento <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> della classe <see cref="T:System.ComponentModel.BackgroundWorker" />.La classe non può essere ereditata.</summary>
      <param name="sender">Origine dell’evento. </param>
      <param name="e">Oggetto <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> che contiene i dati dell'evento. </param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>Fornisce i dati per l'evento NomeMetodoCompleted.</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />.</summary>
      <param name="result">Risultato di un'operazione asincrona.</param>
      <param name="error">Qualsiasi errore verificatosi durante l'esecuzione dell'operazione asincrona.</param>
      <param name="cancelled">Valore che indica se l'operazione asincrona è stata annullata.</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>Ottiene un valore che rappresenta il risultato di un'operazione asincrona.</summary>
      <returns>Oggetto <see cref="T:System.Object" /> che rappresenta il risultato di un'operazione asincrona.</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> non è null.La proprietà <see cref="P:System.Exception.InnerException" /> contiene un riferimento a <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> è true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>Ottiene un valore che rappresenta lo stato dell'utente.</summary>
      <returns>Oggetto <see cref="T:System.Object" /> che rappresenta lo stato dell'utente.</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>Rappresenta il metodo che gestirà l'evento <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> di una classe <see cref="T:System.ComponentModel.BackgroundWorker" />.</summary>
      <param name="sender">Origine dell’evento. </param>
      <param name="e">Oggetto <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> che contiene i dati dell'evento. </param>
    </member>
  </members>
</doc>