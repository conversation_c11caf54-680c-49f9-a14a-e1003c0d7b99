/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
using System;
using System.IO;
using System.Data;
using System.Data.Common;
using System.Drawing;
using System.Web;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Xml;
using System.Diagnostics;
using System.Globalization;

namespace SplendidCRM.Administration.PaymentGateway
{
	/// <summary>
	///		Summary description for EditView.
	/// </summary>
	public class EditView : SplendidControl
	{
		protected _controls.DynamicButtons ctlDynamicButtons;

		protected DropDownList GATEWAY         ;
		protected CheckBox     TEST_MODE       ;
		protected TextBox      GATEWAY_LOGIN   ;
		protected TextBox      GATEWAY_PASSWORD;

		protected void Page_Command(Object sender, CommandEventArgs e)
		{
			if ( e.CommandName == "Save" )
			{
				GATEWAY_LOGIN   .Text = GATEWAY_LOGIN   .Text.Trim();
				GATEWAY_PASSWORD.Text = GATEWAY_PASSWORD.Text.Trim();
				if ( Page.IsValid )
				{
					try
					{
						Application["CONFIG.PaymentGateway"         ] = GATEWAY      .SelectedValue;
						Application["CONFIG.PaymentGateway_TestMode"] = TEST_MODE    .Checked.ToString();
						Application["CONFIG.PaymentGateway_Login"   ] = GATEWAY_LOGIN.Text;
						SqlProcs.spCONFIG_Update("payments","PaymentGateway"         , Sql.ToString(Application["CONFIG.PaymentGateway"         ]));
						SqlProcs.spCONFIG_Update("payments","PaymentGateway_TestMode", Sql.ToString(Application["CONFIG.PaymentGateway_TestMode"]));
						SqlProcs.spCONFIG_Update("payments","PaymentGateway_Login"   , Sql.ToString(Application["CONFIG.PaymentGateway_Login"   ]));

						if ( Sql.IsEmptyString(GATEWAY_LOGIN.Text) )
							GATEWAY_PASSWORD.Text = String.Empty;
						if ( GATEWAY_PASSWORD.Text != "**********" )
						{
							if ( !Sql.IsEmptyString(GATEWAY_PASSWORD.Text) )
							{
								Guid gCREDIT_CARD_KEY = Sql.ToGuid(Application["CONFIG.CreditCardKey"]);
								if ( Sql.IsEmptyGuid(gCREDIT_CARD_KEY) )
								{
									gCREDIT_CARD_KEY = Guid.NewGuid();
									SqlProcs.spCONFIG_Update("system", "CreditCardKey", gCREDIT_CARD_KEY.ToString());
									Application["CONFIG.CreditCardKey"] = gCREDIT_CARD_KEY;
								}
								Guid gCREDIT_CARD_IV = Sql.ToGuid(Application["CONFIG.CreditCardIV"]);
								if ( Sql.IsEmptyGuid(gCREDIT_CARD_IV) )
								{
									gCREDIT_CARD_IV = Guid.NewGuid();
									SqlProcs.spCONFIG_Update("system", "CreditCardIV", gCREDIT_CARD_IV.ToString());
									Application["CONFIG.CreditCardIV"] = gCREDIT_CARD_IV;
								}
								string sENCRYPTED_PASSWORD = Security.EncryptPassword(GATEWAY_PASSWORD.Text, gCREDIT_CARD_KEY, gCREDIT_CARD_IV);
								if ( Security.DecryptPassword(sENCRYPTED_PASSWORD, gCREDIT_CARD_KEY, gCREDIT_CARD_IV) != GATEWAY_PASSWORD.Text )
									throw(new Exception("Decryption failed"));
								// 02/23/2008 Paul.  Decrypt at the last minute to ensure that an unencrypted password is never sent to the browser. 
								Application["CONFIG.PaymentGateway_Password"] = sENCRYPTED_PASSWORD;
								SqlProcs.spCONFIG_Update("payments","PaymentGateway_Password", Sql.ToString(Application["CONFIG.PaymentGateway_Password"]));
							}
							else
							{
								Application["CONFIG.PaymentGateway_Password"] = String.Empty;
								SqlProcs.spCONFIG_Update("payments","PaymentGateway_Password", Sql.ToString(Application["CONFIG.PaymentGateway_Password"]));
							}
						}
					}
					catch(Exception ex)
					{
						SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
						ctlDynamicButtons.ErrorText = ex.Message;
						return;
					}
					Response.Redirect("~/Administration/default.aspx");
				}
			}
			else if ( e.CommandName == "Cancel" )
			{
				Response.Redirect("~/Administration/default.aspx");
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			SetPageTitle(L10n.Term("Administration.LBL_PAYMENT_GATEWAY_TITLE"));
			this.Visible = SplendidCRM.Security.IS_ADMIN;
			if ( !this.Visible )
				return;

			try
			{
				if ( !IsPostBack )
				{
					// 03/20/2008 Paul.  Dynamic buttons need to be recreated in order for events to fire. 
					ctlDynamicButtons.AppendButtons(m_sMODULE + ".EditView", Guid.Empty, null);

					// 03/18/2008 Paul.  The payment processors change to frequently, so use a terminology list. 
					GATEWAY.DataSource = SplendidCache.List("payment_gateway_dom");
					GATEWAY.DataBind();
					GATEWAY.Items.Add(new ListItem(L10n.Term(".LBL_NONE"), ""));
					/*
					foreach(string sProcessor in Enum.GetNames(typeof(SplendidCharge.Processor)))
					{
						GATEWAY.Items.Add(sProcessor);
					}
					*/
					try
					{
						GATEWAY.SelectedValue = Sql.ToString(Application["CONFIG.PaymentGateway"]);
					}
					catch
					{
					}
					TEST_MODE    .Checked = Sql.ToBoolean(Application["CONFIG.PaymentGateway_TestMode"]);
					GATEWAY_LOGIN.Text    = Sql.ToString (Application["CONFIG.PaymentGateway_Login"   ]);
					if ( !Sql.IsEmptyString(Application["CONFIG.PaymentGateway_Password"]) )
						GATEWAY_PASSWORD.Text = "**********";
				}
			}
			catch(Exception ex)
			{
				SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
				ctlDynamicButtons.ErrorText = ex.Message;
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		///		Required method for Designer support - do not modify
		///		the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.Load += new System.EventHandler(this.Page_Load);
			ctlDynamicButtons.Command += new CommandEventHandler(Page_Command);
			m_sMODULE = "PaymentGateway";
			if ( IsPostBack )
			{
				// 03/20/2008 Paul.  Dynamic buttons need to be recreated in order for events to fire. 
				ctlDynamicButtons.AppendButtons(m_sMODULE + ".EditView", Guid.Empty, null);
			}
		}
		#endregion
	}
}
