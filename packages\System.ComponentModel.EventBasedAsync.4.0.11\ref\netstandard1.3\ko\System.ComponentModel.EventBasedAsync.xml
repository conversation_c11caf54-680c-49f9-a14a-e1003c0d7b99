﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>MethodNameCompleted 이벤트에 대한 데이터를 제공합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>
        <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="error">비동기 작업 중 발생한 오류입니다.</param>
      <param name="cancelled">비동기 작업이 취소되었는지 여부를 나타내는 값입니다.</param>
      <param name="userState">
        <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> 메서드에 전달되는 사용자가 제공한 선택적인 상태 개체입니다.</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>비동기 작업이 취소되었는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>백그라운드 작업이 취소되었으면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>비동기 작업 중 발생한 오류를 나타내는 값을 가져옵니다.</summary>
      <returns>비동기 작업 중 오류가 발생했으면 <see cref="T:System.Exception" /> 인스턴스이고, 그렇지 않으면 null입니다.</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>비동기 작업에 실패한 경우 사용자가 제공한 예외를 발생시킵니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> 속성이 true일 경우(이 속성은 </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 속성이 비동기 작업에서 설정된 경우<see cref="P:System.Exception.InnerException" /> 속성에는 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />에 대한 참조가 들어 있습니다.</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>비동기 작업의 고유 식별자를 가져옵니다.</summary>
      <returns>비동기 작업을 고유하게 식별하는 개체 참조이거나, 설정된 값이 없으면 null입니다.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>비동기 작업의 MethodNameCompleted 이벤트를 처리할 메서드를 나타냅니다.</summary>
      <param name="sender">이벤트 소스입니다. </param>
      <param name="e">이벤트 데이터를 포함하는 <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />입니다. </param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>비동기 작업의 수명을 추적합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>비동기 작업을 완료합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>비동기 작업의 수명을 끝냅니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" />가 이 작업에 대해 이미 호출된 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>응용 프로그램 모델에 적절한 스레드나 컨텍스트에서 대리자를 호출합니다.</summary>
      <param name="d">작업이 끝날 때 호출될 대리자를 래핑하는 <see cref="T:System.Threading.SendOrPostCallback" /> 개체입니다. </param>
      <param name="arg">
        <paramref name="d" /> 매개 변수에 포함된 대리자에 대한 인수입니다. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> 메서드가 이 작업에 대해 이미 호출된 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" />가 null인 경우 </exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>비동기 작업의 수명을 끝냅니다.</summary>
      <param name="d">작업이 끝날 때 호출될 대리자를 래핑하는 <see cref="T:System.Threading.SendOrPostCallback" /> 개체입니다. </param>
      <param name="arg">
        <paramref name="d" /> 매개 변수에 포함된 대리자에 대한 인수입니다. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" />가 이 작업에 대해 이미 호출된 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" />가 null인 경우 </exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>생성자에 전달된 <see cref="T:System.Threading.SynchronizationContext" /> 개체를 가져옵니다.</summary>
      <returns>생성자에 전달된 <see cref="T:System.Threading.SynchronizationContext" /> 개체입니다.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>비동기 작업을 고유하게 식별하는 데 사용되는 개체를 가져오거나 설정합니다.</summary>
      <returns>비동기 메서드 호출에 전달된 상태 개체입니다.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>비동기 메서드 호출을 지원하는 클래스에 대한 동시성 관리 기능을 제공합니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>특정 비동기 작업의 기간을 추적하기 위한 <see cref="T:System.ComponentModel.AsyncOperation" />을 반환합니다.</summary>
      <returns>비동기 메서드 호출의 기간을 추적하는 데 사용할 수 있는 <see cref="T:System.ComponentModel.AsyncOperation" />입니다.</returns>
      <param name="userSuppliedState">작업 ID와 같은 클라이언트 상태 정보와 특정 비동기 작업을 연결하는 데 사용되는 개체입니다. </param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>비동기 작업의 동기화 컨텍스트를 가져오거나 설정합니다.</summary>
      <returns>비동기 작업의 동기화 컨텍스트입니다.</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>별도의 스레드에서 작업을 실행합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>보류 중인 백그라운드 작업의 취소를 요청합니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" />가 false입니다. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>응용 프로그램에서 백그라운드 작업의 취소를 요청했는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>응용 프로그램에서 백그라운드 작업의 취소를 요청했으면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>
        <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" />가 호출될 때 발생합니다.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" />가 비동기 작업을 실행하고 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.ComponentModel.BackgroundWorker" />가 비동기 작업을 실행하고 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 이벤트를 발생시킵니다. </summary>
      <param name="e">이벤트 데이터가 들어 있는 <see cref="T:System.EventArgs" />입니다.</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 이벤트를 발생시킵니다.</summary>
      <param name="e">이벤트 데이터가 들어 있는 <see cref="T:System.EventArgs" />입니다. </param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> 이벤트를 발생시킵니다.</summary>
      <param name="e">이벤트 데이터가 들어 있는 <see cref="T:System.EventArgs" />입니다. </param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>
        <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" />가 호출될 때 발생합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 이벤트를 발생시킵니다.</summary>
      <param name="percentProgress">백그라운드 작업의 완료율(0부터 100까지)입니다. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> 속성은 false로 설정됩니다. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 이벤트를 발생시킵니다.</summary>
      <param name="percentProgress">백그라운드 작업의 완료율(0부터 100까지)입니다.</param>
      <param name="userState">
        <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />로 전달된 상태 개체입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> 속성은 false로 설정됩니다. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>백그라운드 작업의 실행을 시작합니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" />가 true입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>백그라운드 작업의 실행을 시작합니다.</summary>
      <param name="argument">
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 이벤트 처리기에서 실행될 백그라운드 작업에서 사용하는 매개 변수입니다. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" />가 true입니다. </exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>백그라운드 작업이 완료되거나 취소되거나 예외를 발생시켰을 때 발생합니다.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" />가 진행률 업데이트를 보고할 수 있는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.ComponentModel.BackgroundWorker" />가 진행률 업데이트를 지원하면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" />가 비동기 취소를 지원하는지 여부를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.ComponentModel.BackgroundWorker" />가 취소를 지원하면 true이고, 그렇지 않으면 false입니다.기본값은 false입니다.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 이벤트 처리기에 대한 데이터를 제공합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>
        <see cref="T:System.ComponentModel.DoWorkEventArgs" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="argument">비동기 작업의 인수를 지정합니다.</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>비동기 작업의 인수를 나타내는 값을 가져옵니다.</summary>
      <returns>비동기 작업의 인수를 나타내는 <see cref="T:System.Object" />입니다.</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>비동기 작업의 결과를 나타내는 값을 가져오거나 설정합니다.</summary>
      <returns>비동기 작업의 결과를 나타내는 <see cref="T:System.Object" />입니다.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 이벤트를 처리할 메서드를 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
      <param name="sender">이벤트 소스입니다.</param>
      <param name="e">이벤트 데이터가 들어 있는 <see cref="T:System.ComponentModel.DoWorkEventArgs" />입니다.</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 이벤트에 대한 데이터를 제공합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="progressPercentage">비동기 작업의 완료율입니다.</param>
      <param name="userState">고유한 사용자 상태입니다.</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>비동기 작업의 진행률을 가져옵니다.</summary>
      <returns>비동기 작업의 진행을 나타내는 백분율 값입니다.</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>고유한 사용자 상태를 가져옵니다.</summary>
      <returns>사용자 상태를 나타내는 고유한 <see cref="T:System.Object" />입니다.</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> 클래스의 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 이벤트를 처리할 메서드를 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
      <param name="sender">이벤트 소스입니다. </param>
      <param name="e">이벤트 데이터가 들어 있는 <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />입니다. </param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>MethodNameCompleted 이벤트에 대한 데이터를 제공합니다.</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>
        <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="result">비동기 작업의 결과입니다.</param>
      <param name="error">비동기 작업 중 발생한 오류입니다.</param>
      <param name="cancelled">비동기 작업이 취소되었는지 여부를 나타내는 값입니다.</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>비동기 작업의 결과를 나타내는 값을 가져옵니다.</summary>
      <returns>비동기 작업의 결과를 나타내는 <see cref="T:System.Object" />입니다.</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />가 null가 아닌 경우<see cref="P:System.Exception.InnerException" /> 속성에는 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />에 대한 참조가 들어 있습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" />가 true입니다.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>사용자 상태를 나타내는 값을 가져옵니다.</summary>
      <returns>사용자 상태를 나타내는 <see cref="T:System.Object" />입니다.</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> 클래스의 <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> 이벤트를 처리할 메서드를 나타냅니다.</summary>
      <param name="sender">이벤트 소스입니다. </param>
      <param name="e">이벤트 데이터가 들어 있는 <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />입니다. </param>
    </member>
  </members>
</doc>