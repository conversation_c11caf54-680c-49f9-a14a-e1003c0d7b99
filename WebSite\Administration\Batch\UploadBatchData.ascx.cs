﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.IO;
using System.Data;

namespace SplendidCRM.Administration.Batch
{
    public partial class UploadBatchData : System.Web.UI.UserControl
    {
        public DataTable dt;
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnUpload_Click(object sender, EventArgs e)
        {
            try
            {
                if (Page.IsValid)
                {
                    string sFILENAME = string.Empty;
                    string sFILE_EXT = string.Empty;
                    string sFILE_MIME_TYPE = string.Empty;
                    HttpPostedFile pstIMPORT = fileIMPORT.PostedFile;
                    if (pstIMPORT != null)
                    {
                        if (pstIMPORT.FileName.Length > 0)
                        {
                            sFILENAME = Path.GetFileName(pstIMPORT.FileName);
                            sFILE_EXT = Path.GetExtension(sFILENAME);
                            sFILE_MIME_TYPE = pstIMPORT.ContentType;
                        }
                        //if (sFILE_EXT.ToLower() != ".xls")
                        //{
                        //    lblError.Text = "The file format is incorrect, please select excel 2003 format file.";
                        //    return;
                        //}

                        //ExcelDataReader.ExcelDataReader spreadsheet = new ExcelDataReader.ExcelDataReader(pstIMPORT.InputStream);

                        //ExcelHelper eh = new ExcelHelper(pstIMPORT.FileName);
                        //dt = eh.ReadStreamToDataTable(pstIMPORT.InputStream, pstIMPORT.FileName);

                        removeEmpty(dt);

                        DbProviderFactory dbf = DbProviderFactories.GetFactory();

                        #region 创建表
                        using (IDbConnection con = dbf.CreateConnection())
                        {
                            con.Open();
                            using (IDbTransaction trn = con.BeginTransaction())
                            {
                                try
                                {
                                    using (IDbCommand cmd = con.CreateCommand())
                                    {
                                        cmd.CommandTimeout = 0;
                                        cmd.Transaction = trn;
                                        cmd.CommandType = CommandType.Text;
                                        cmd.CommandText = getSqlStr(dt, "BatchExecSql");
                                        cmd.ExecuteNonQuery();
                                    }
                                    trn.Commit();
                                }
                                catch (Exception ex)
                                {
                                    trn.Rollback();
                                    throw (new Exception(ex.Message, ex.InnerException));
                                }
                            }
                        }
                        #endregion

                        //if (spreadsheet.WorkbookData.Tables.Count > 0)
                        //{
                            //dt = spreadsheet.WorkbookData.Tables[0];

                            //DataTable dt2 = new DataTable();
                            //dt2.Columns.Add("Column1", typeof(string));
                            //dt2.Columns.Add("Column2", typeof(string));
                            //dt2.Columns.Add("Column3", typeof(string));
                            //dt2.Columns.Add("Column4", typeof(string));
                            //dt2.Columns.Add("Column5", typeof(string));
                            //dt2.Columns.Add("Column6", typeof(string));
                            //DataRow dr;
                            //for (int i = 0; i < dt.Rows.Count; i++)
                            //{
                            //    string Column1 = Convert.ToString(dt.Rows[i]["Column1"]);
                            //    string Column2 = Convert.ToString(dt.Rows[i]["Column2"]);
                            //    string Column3 = Convert.ToString(dt.Rows[i]["Column3"]);
                            //    string Column4 = Convert.ToString(dt.Rows[i]["Column4"]);
                            //    string Column5 = Convert.ToString(dt.Rows[i]["Column5"]);
                            //    string Column6 = Convert.ToString(dt.Rows[i]["Column6"]);
                            //    if (
                            //        !string.IsNullOrEmpty(Column1) &&
                            //        !string.IsNullOrEmpty(Column2) &&
                            //        !string.IsNullOrEmpty(Column3) &&
                            //        !string.IsNullOrEmpty(Column4) &&
                            //        !string.IsNullOrEmpty(Column5) &&
                            //        !string.IsNullOrEmpty(Column6)
                            //        )
                            //    {
                                    //dr = dt2.NewRow();
                                    //dr["Column1"] = Column1;
                                    //dr["Column2"] = Column2;
                                    //dr["Column3"] = Column3;
                                    //dr["Column4"] = Column4;
                                    //dr["Column5"] = Column5;
                                    //dr["Column6"] = Column6;
                                    //dt2.Rows.Add(dr);
                            //    }
                            //}

                            if (HttpContext.Current.Session["UploadBatchData_DataTable"]==null)
                            {
                                //HttpContext.Current.Session["UploadBatchData_DataTable"] = dt2;
                                HttpContext.Current.Session["UploadBatchData_DataTable"] = dt;
                            }
                            //GridView1.DataSource = dt2;
                            GridView1.DataSource = dt;
                            GridView1.DataBind();
                            if (dt != null && dt.Rows.Count > 0)
                            {
                                lblError.Text = "Data read successfully";
                            }
                        //}
                    }
                }
            }
            catch (Exception ex)
            {
                HttpContext.Current.Session["UploadBatchData_DataTable"] = null;
                lblError.Text = "Data is null ,error:" + ex.Message;
            }
            
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            try
            {
                dt = HttpContext.Current.Session["UploadBatchData_DataTable"] as DataTable;
                if (dt != null && dt.Rows.Count > 0)
                {
                    string sSplendidHostingConnection = Utils.AppSettings["SplendidSQLServer"];
                    //table Write database
                    SplendidCRM.TGSAP.APSqlProcs.SqlBulkCopyByDataTable(sSplendidHostingConnection, "dbo.BatchExecSql", dt);
                    lblError.Text = "Number of affected rows：" + SplendidCRM.TGSAP.APSqlProcs.BatchExecSql();

                    //SplendidCRM.TGSAP.APSqlProcs.SqlBulkCopyByDataTable(sSplendidHostingConnection, "dbo.Hubert_Telamon_AP_Vendors", dt);
                    //SplendidCRM.TGSAP.APSqlProcs.SqlBulkCopyByDataTable(sSplendidHostingConnection, "dbo.Hubert_Telamon_PurchaseOrderDetail", dt);
                    //SplendidCRM.TGSAP.APSqlProcs.SqlBulkCopyByDataTable(sSplendidHostingConnection, "dbo.Hubert_Telamon_PurchaseOrders", dt);
                    //SplendidCRM.TGSAP.APSqlProcs.SqlBulkCopyByDataTable(sSplendidHostingConnection, "dbo.Hubert_Telamon_Receipts", dt);
                    


                    HttpContext.Current.Session["UploadBatchData_DataTable"] = null;
                }
                else
                {
                    lblError.Text = "Data is null ,Please select a file and load the file!";
                }
            }
            catch (Exception ex)
            {
                HttpContext.Current.Session["UploadBatchData_DataTable"] = null;
                lblError.Text = "error_Button1_Click:" + ex.Message;
            }
            
        }

        protected void Cancel_Click(object sender, EventArgs e)
        {
            HttpContext.Current.Session["UploadBatchData_DataTable"] = null;
            Response.Redirect("/Administration/TGS_CRMAdmin.aspx", true);
        }

        #region 拼接字符串
        public static string getSqlStr(DataTable dt, string tableName)
        {
            string sSql = "if object_id(N'" + tableName + "',N'U') is null begin Create table " + tableName + " ( ";

            for (int i = 0; i < dt.Columns.Count; i++)
            {
                sSql = sSql + "Column" + (i + 1).ToString() + " nvarchar(1000),";
            }
            sSql = sSql.TrimEnd(',') + ") end else begin delete from " + tableName + " end";
            return sSql;
        }
        #endregion

        #region 循环去除datatable中的空行
        protected static void removeEmpty(DataTable dt)
        {
            List<DataRow> removelist = new List<DataRow>();
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                bool rowdataisnull = true;
                for (int j = 0; j < dt.Columns.Count; j++)
                {

                    if (!string.IsNullOrEmpty(dt.Rows[i][j].ToString().Trim()))
                    {

                        rowdataisnull = false;
                    }

                }
                if (rowdataisnull)
                {
                    removelist.Add(dt.Rows[i]);
                }

            }
            for (int i = 0; i < removelist.Count; i++)
            {
                dt.Rows.Remove(removelist[i]);
            }
        }
        #endregion
    }
}