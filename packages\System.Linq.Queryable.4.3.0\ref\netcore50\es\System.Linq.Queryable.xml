﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Queryable</name>
  </assembly>
  <members>
    <member name="T:System.Linq.EnumerableExecutor">
      <summary>Representa un árbol de expresión y proporciona la funcionalidad para ejecutar este árbol después de rescribirlo.</summary>
    </member>
    <member name="M:System.Linq.EnumerableExecutor.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Linq.EnumerableExecutor" />.</summary>
    </member>
    <member name="T:System.Linq.EnumerableExecutor`1">
      <summary>Representa un árbol de expresión y proporciona la funcionalidad para ejecutar este árbol después de rescribirlo.</summary>
      <typeparam name="T">Tipo de datos del valor que es el resultado de ejecutar el árbol de expresión.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableExecutor`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Linq.EnumerableExecutor`1" />.</summary>
      <param name="expression">Árbol de expresión para asociar a la nueva instancia.</param>
    </member>
    <member name="T:System.Linq.EnumerableQuery">
      <summary>Representa una clase <see cref="T:System.Collections.IEnumerable" /> como origen de datos de <see cref="T:System.Linq.EnumerableQuery" />. </summary>
    </member>
    <member name="M:System.Linq.EnumerableQuery.#ctor">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Linq.EnumerableQuery" />.</summary>
    </member>
    <member name="T:System.Linq.EnumerableQuery`1">
      <summary>Representa una colección <see cref="T:System.Collections.Generic.IEnumerable`1" /> como origen de los datos <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <typeparam name="T">Tipo de los datos de la colección.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Linq.EnumerableQuery`1" /> y la asocia con una colección <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
      <param name="enumerable">Una colección para asociar a la nueva instancia.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>Inicializa una nueva instancia de la clase <see cref="T:System.Linq.EnumerableQuery`1" /> y la asocia con un árbol de expresión especificado.</summary>
      <param name="expression">Árbol de expresión para asociar a la nueva instancia.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Devuelve un enumerador que puede recorrer en iteración la colección <see cref="T:System.Collections.Generic.IEnumerable`1" /> asociada o, si es nulo, la colección que es el resultado de rescribir el árbol de expresión asociado como consulta en un origen de datos <see cref="T:System.Collections.Generic.IEnumerable`1" /> y de ejecutarlo.</summary>
      <returns>Enumerador que se puede utilizar para recorrer en iteración el origen de datos asociado.</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Devuelve un enumerador que puede recorrer en iteración la colección <see cref="T:System.Collections.Generic.IEnumerable`1" /> asociada o, si es nulo, la colección que es el resultado de rescribir el árbol de expresión asociado como consulta en un origen de datos <see cref="T:System.Collections.Generic.IEnumerable`1" /> y de ejecutarlo.</summary>
      <returns>Enumerador que se puede utilizar para recorrer en iteración el origen de datos asociado.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#ElementType">
      <summary>Obtiene el tipo de datos de la colección que esta instancia representa.</summary>
      <returns>Tipo de datos de la colección que esta instancia representa.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Expression">
      <summary>Obtiene el árbol de expresión que está asociado o que representa esta instancia.</summary>
      <returns>Árbol de expresión que está asociado o que representa esta instancia.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Provider">
      <summary>Obtiene el proveedor de consultas que está asociado a esta instancia.</summary>
      <returns>Proveedor de consultas que está asociado a esta instancia.</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>Construye un nuevo objeto <see cref="T:System.Linq.EnumerableQuery`1" /> y lo asocia a un árbol de expresión especificado que representa una colección <see cref="T:System.Linq.IQueryable`1" /> de datos.</summary>
      <returns>Objeto EnumerableQuery asociado a <paramref name="expression" />.</returns>
      <param name="expression">Árbol de expresión que se va a ejecutar.</param>
      <typeparam name="S">Tipo de datos de la colección que <paramref name="expression" /> representa.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery(System.Linq.Expressions.Expression)">
      <summary>Construye un nuevo objeto <see cref="T:System.Linq.EnumerableQuery`1" /> y lo asocia a un árbol de expresión especificado que representa una colección <see cref="T:System.Linq.IQueryable" /> de datos.</summary>
      <returns>Objeto <see cref="T:System.Linq.EnumerableQuery`1" /> asociado a <paramref name="expression" />.</returns>
      <param name="expression">Árbol de expresión que representa una colección <see cref="T:System.Linq.IQueryable" /> de datos.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute``1(System.Linq.Expressions.Expression)">
      <summary>Ejecuta una expresión después de rescribirla para llamar a los métodos <see cref="T:System.Linq.Enumerable" /> en lugar de a los métodos <see cref="T:System.Linq.Queryable" /> en cualquier origen de datos enumerable que no pueda ser consultado por los métodos <see cref="T:System.Linq.Queryable" />.</summary>
      <returns>Valor que es el resultado de ejecutar <paramref name="expression" />.</returns>
      <param name="expression">Árbol de expresión que se va a ejecutar.</param>
      <typeparam name="S">Tipo de datos de la colección que <paramref name="expression" /> representa.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute(System.Linq.Expressions.Expression)">
      <summary>Ejecuta una expresión después de rescribirla para llamar a los métodos <see cref="T:System.Linq.Enumerable" /> en lugar de a los métodos <see cref="T:System.Linq.Queryable" /> en cualquier origen de datos enumerable que no pueda ser consultado por los métodos <see cref="T:System.Linq.Queryable" />.</summary>
      <returns>Valor que es el resultado de ejecutar <paramref name="expression" />.</returns>
      <param name="expression">Árbol de expresión que se va a ejecutar.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.ToString">
      <summary>Devuelve una representación textual de la colección enumerable o, si es null, del árbol de expresión asociado a esta instancia.</summary>
      <returns>Representación textual de la colección enumerable o, si es null, del árbol de expresión asociado a esta instancia.</returns>
    </member>
    <member name="T:System.Linq.Queryable">
      <summary>Proporciona un conjunto de métodos static (Shared en Visual Basic) para consultar estructuras de datos que implementan <see cref="T:System.Linq.IQueryable`1" />.</summary>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0,``0}})">
      <summary>Aplica una función de acumulador a una secuencia.</summary>
      <returns>Valor final del acumulador.</returns>
      <param name="source">Secuencia a la que se va a agregar.</param>
      <param name="func">Función de acumulador que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="func" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``2(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}})">
      <summary>Aplica una función de acumulador a una secuencia.El valor de inicialización especificado se utiliza como valor de inicio del acumulador.</summary>
      <returns>Valor final del acumulador.</returns>
      <param name="source">Secuencia a la que se va a agregar.</param>
      <param name="seed">Valor de inicio del acumulador.</param>
      <param name="func">Función de acumulador que se va a invocar en cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">Tipo del valor del acumulador.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="func" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``3(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,``2}})">
      <summary>Aplica una función de acumulador a una secuencia.El valor de inicialización especificado se utiliza como valor inicial del acumulador y la función especificada se utiliza para seleccionar el valor resultante.</summary>
      <returns>El valor final del acumulador transformado.</returns>
      <param name="source">Secuencia a la que se va a agregar.</param>
      <param name="seed">Valor de inicio del acumulador.</param>
      <param name="func">Función de acumulador que se va a invocar en cada elemento.</param>
      <param name="selector">Función que va a transformar el valor final del acumulador en el valor del resultado.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">Tipo del valor del acumulador.</typeparam>
      <typeparam name="TResult">Tipo del valor resultante.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="func" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.All``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Determina si todos los elementos de una secuencia satisfacen una condición.</summary>
      <returns>true si todos los elementos de la secuencia de origen pasan la prueba del predicado especificado o si la secuencia está vacía; de lo contrario, false.</returns>
      <param name="source">Secuencia en cuyos elementos se va a comprobar una condición.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0})">
      <summary>Determina si una secuencia contiene elementos.</summary>
      <returns>true si la secuencia de origen contiene elementos; de lo contrario, false.</returns>
      <param name="source">Secuencia que se va a comprobar si está vacía.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Determina si algún elemento de una secuencia satisface una condición.</summary>
      <returns>true si algún elemento de la secuencia de origen pasa la prueba del predicado especificado; de lo contrario, false.</returns>
      <param name="source">Secuencia en cuyos elementos se va a comprobar una condición.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Convierte una interfaz <see cref="T:System.Collections.Generic.IEnumerable`1" /> genérica en una interfaz <see cref="T:System.Linq.IQueryable`1" /> genérica.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que representa la secuencia de entrada.</returns>
      <param name="source">Secuencia que se va a convertir.</param>
      <typeparam name="TElement">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable(System.Collections.IEnumerable)">
      <summary>Convierte una interfaz <see cref="T:System.Collections.IEnumerable" /> en <see cref="T:System.Linq.IQueryable" />.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable" /> que representa la secuencia de entrada.</returns>
      <param name="source">Secuencia que se va a convertir.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> no implementa <see cref="T:System.Collections.Generic.IEnumerable`1" /> para algunos parámetros <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Decimal})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Decimal" />.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Decimal" /> cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Double})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Double" />.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Double" /> cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int32})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Int32" />.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Int32" /> cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int64})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Int64" />.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Int64" /> cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Decimal" /> que aceptan valores NULL.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia de origen está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Decimal" /> que aceptan valores NULL cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Double" /> que aceptan valores NULL.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia de origen está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Double" /> que aceptan valores NULL cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Int32" /> que aceptan valores NULL.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia de origen está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Int32" /> que aceptan valores NULL cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>Calcular el promedio de una secuencia de valores <see cref="T:System.Int64" /> que aceptan valores NULL.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia de origen está vacía o contiene sólo valores null.</returns>
      <param name="source">Una secuencia de valores <see cref="T:System.Int64" /> que aceptan valores NULL cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Single" /> que aceptan valores NULL.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia de origen está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Single" /> que aceptan valores NULL cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Single})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Single" />.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Single" /> cuyo promedio se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Decimal" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores que se utilizan para calcular un promedio.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Double" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Int32" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Int64" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Decimal" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia <paramref name="source" /> está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Double" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia <paramref name="source" /> está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Int32" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia <paramref name="source" /> está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Int64" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia <paramref name="source" /> está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Single" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>Promedio de la secuencia de valores o null si la secuencia <paramref name="source" /> está vacía o contiene sólo valores null.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>Calcula el promedio de una secuencia de valores <see cref="T:System.Single" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>El promedio de la secuencia de valores.</returns>
      <param name="source">Secuencia de valores cuyo promedio se va a calcular.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> no contiene elementos.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Cast``1(System.Linq.IQueryable)">
      <summary>Convierte los elementos de <see cref="T:System.Linq.IQueryable" /> en el tipo especificado.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene cada elemento de la secuencia de origen convertido al tipo especificado.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable" /> que contiene los elementos que se van a convertir.</param>
      <typeparam name="TResult">Tipo al que se convierten los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidCastException">Un elemento de la secuencia no se puede convertir al tipo <paramref name="TResult" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Concat``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Concatena dos secuencias.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos concatenados de las dos secuencias de entrada.</returns>
      <param name="source1">Primera secuencia que se va a concatenar.</param>
      <param name="source2">Secuencia que se va a concatenar con la primera secuencia.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0)">
      <summary>Determina si una secuencia contiene un elemento especificado utilizando el comparador de igualdad predeterminado.</summary>
      <returns>true si la secuencia de entrada contiene un elemento que tiene el valor especificado; de lo contrario, false.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> en el que se va a buscar <paramref name="item" />.</param>
      <param name="item">Objeto que se va a buscar en la secuencia.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determina si una secuencia contiene un elemento especificado utilizando un objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> determinado.</summary>
      <returns>true si la secuencia de entrada contiene un elemento que tiene el valor especificado; de lo contrario, false.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> en el que se va a buscar <paramref name="item" />.</param>
      <param name="item">Objeto que se va a buscar en la secuencia.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar valores.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el número de elementos de una secuencia.</summary>
      <returns>El número de elementos de la secuencia de entrada.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos que se van a contar.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">El número de elementos de <paramref name="source" /> es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve el número de elementos de la secuencia especificada que satisfacen una condición.</summary>
      <returns>El número de elementos de la secuencia que satisfacen la condición de la función de predicado.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos que se van a contar.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
      <exception cref="T:System.OverflowException">El número de elementos de <paramref name="source" /> es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve los elementos de la secuencia especificada o el valor predeterminado del parámetro de tipo en una colección singleton si la secuencia está vacía.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene default(<paramref name="TSource" />) si <paramref name="source" /> está vacío; de lo contrario, <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> para el que se va a devolver un valor predeterminado si está vacío.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0},``0)">
      <summary>Devuelve los elementos de la secuencia especificada o el valor especificado en una colección singleton si la secuencia está vacía.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene <paramref name="defaultValue" /> si <paramref name="source" /> está vacío; de lo contrario, <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> para el que se va a devolver el valor especificado si está vacío.</param>
      <param name="defaultValue">Valor que se va a devolver si la secuencia está vacía.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve diversos elementos de una secuencia utilizando el comparador de igualdad predeterminado para comparar los valores.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IQueryable`1" /> que contiene diversos elementos de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se van a quitar los elementos duplicados.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Devuelve diversos elementos de una secuencia utilizando un objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado para comparar los valores.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IQueryable`1" /> que contiene diversos elementos de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se van a quitar los elementos duplicados.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar valores.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAt``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Devuelve el elemento situado en un índice especificado de una secuencia.</summary>
      <returns>El elemento situado en la posición especificada de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un elemento.</param>
      <param name="index">Índice de base cero del elemento que se debe recuperar.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> es menor que cero.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAtOrDefault``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Devuelve el elemento situado en un índice especificado de una secuencia o un valor predeterminado si el índice está fuera del intervalo.</summary>
      <returns>default (<paramref name="TSource" />) si <paramref name="index" /> está fuera de los límites de <paramref name="source" />; de lo contrario, el elemento situado en la posición especificada de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un elemento.</param>
      <param name="index">Índice de base cero del elemento que se debe recuperar.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Proporciona la diferencia de conjuntos de dos secuencias utilizando el comparador de igualdad predeterminado para comparar los valores.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene la diferencia de conjuntos de las dos secuencias.</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos que no se encuentren en  <paramref name="source2" /> se van a devolver.</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> cuyos elementos que se encuentren también en la primera secuencia no aparecerán en la secuencia devuelta.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Proporciona la diferencia de conjuntos de dos secuencias utilizando el objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado para comparar los valores.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene la diferencia de conjuntos de las dos secuencias.</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos que no se encuentren en  <paramref name="source2" /> se van a devolver.</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> cuyos elementos que se encuentren también en la primera secuencia no aparecerán en la secuencia devuelta.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar valores.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el primer elemento de una secuencia.</summary>
      <returns>El primer elemento de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver el primer elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">La secuencia de origen está vacía.</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve el primer elemento de una secuencia que satisface una condición especificada.</summary>
      <returns>El primer elemento de <paramref name="source" /> que pasa la prueba de <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un elemento.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">Ningún elemento satisface la condición de <paramref name="predicate" />.O bienLa secuencia de origen está vacía.</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el primer elemento de una secuencia o un valor predeterminado si la secuencia no contiene elementos.</summary>
      <returns>default (<paramref name="TSource" />) si <paramref name="source" /> está vacío; de lo contrario, el primer elemento de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver el primer elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve el primer elemento de una secuencia que satisface una condición especificada o un valor predeterminado si no se encuentra ningún elemento.</summary>
      <returns>default (<paramref name="TSource" />) si <paramref name="source" /> está vacío o si ningún elemento pasa la prueba especificada en <paramref name="predicate" />; de lo contrario, el primer elemento de <paramref name="source" /> que pasa la prueba especificada en <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un elemento.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Agrupa los elementos de una secuencia según una función del selector de claves especificada.</summary>
      <returns>IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt; en C# o IQueryable(Of IGrouping(Of TKey, TSource)) en Visual Basic donde cada objeto <see cref="T:System.Linq.IGrouping`2" /> contiene una secuencia de objetos y una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Agrupa los elementos de una secuencia según una función del selector de calves especificada y compara las claves utilizando un comparador especificado.</summary>
      <returns>IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt; en C# o IQueryable(Of IGrouping(Of TKey, TSource)) en Visual Basic donde cada <see cref="T:System.Linq.IGrouping`2" /> contiene una secuencia de objetos y una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}})">
      <summary>Agrupa los elementos de una secuencia según una función del selector de claves especificada y proyecta los elementos de cada grupo utilizando una función determinada.</summary>
      <returns>IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt; en C# o IQueryable(Of IGrouping(Of TKey, TElement)) en Visual Basic donde cada <see cref="T:System.Linq.IGrouping`2" /> contiene una secuencia de objetos de tipo <paramref name="TElement" /> y una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <param name="elementSelector">Función que asigna cada elemento de origen a un elemento de <see cref="T:System.Linq.IGrouping`2" />.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Tipo de los elementos de cada <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="elementSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Agrupa los elementos de una secuencia y proyecta los elementos de cada grupo utilizando una función especificada.Los valores de clave se comparan utilizando un comparador especificado.</summary>
      <returns>IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt; en C# o IQueryable(Of IGrouping(Of TKey, TElement)) en Visual Basic donde cada <see cref="T:System.Linq.IGrouping`2" /> contiene una secuencia de objetos de tipo <paramref name="TElement" /> y una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <param name="elementSelector">Función que asigna cada elemento de origen a un elemento de <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Tipo de los elementos de cada <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="elementSelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}})">
      <summary>Agrupa los elementos de una secuencia según una función del selector de claves especificada y crea un valor de resultado a partir de cada grupo y su clave.Los elementos de cada grupo se proyectan utilizando una función determinada.</summary>
      <returns>Objeto T:System.Linq.IQueryable`1 que tiene un argumento de tipo <paramref name="TResult" /> y en el que cada elemento representa una proyección sobre un grupo y su clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <param name="elementSelector">Función que asigna cada elemento de origen a un elemento de <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">Función que va a crear un valor de resultado a partir de cada grupo.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Tipo de los elementos de cada <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">Tipo del valor de resultado devuelto por <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="elementSelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Agrupa los elementos de una secuencia según una función del selector de claves especificada y crea un valor de resultado a partir de cada grupo y su clave.Las claves se comparan utilizando un comparador especificado y los elementos de cada grupo se proyectan utilizando una función determinada.</summary>
      <returns>Objeto T:System.Linq.IQueryable`1 que tiene un argumento de tipo <paramref name="TResult" /> y en el que cada elemento representa una proyección sobre un grupo y su clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <param name="elementSelector">Función que asigna cada elemento de origen a un elemento de <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">Función que va a crear un valor de resultado a partir de cada grupo.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Tipo de los elementos de cada <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">Tipo del valor de resultado devuelto por <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="elementSelector" /> o <paramref name="resultSelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}})">
      <summary>Agrupa los elementos de una secuencia según una función del selector de claves especificada y crea un valor de resultado a partir de cada grupo y su clave.</summary>
      <returns>Objeto T:System.Linq.IQueryable`1 que tiene un argumento de tipo <paramref name="TResult" /> y en el que cada elemento representa una proyección sobre un grupo y su clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <param name="resultSelector">Función que va a crear un valor de resultado a partir de cada grupo.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">Tipo del valor de resultado devuelto por <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Agrupa los elementos de una secuencia según una función del selector de claves especificada y crea un valor de resultado a partir de cada grupo y su clave.Las claves se comparan utilizando un comparador determinado.</summary>
      <returns>Objeto T:System.Linq.IQueryable`1 que tiene un argumento de tipo <paramref name="TResult" /> y en el que cada elemento representa una proyección sobre un grupo y su clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a agrupar.</param>
      <param name="keySelector">Función para extraer la clave de cada elemento.</param>
      <param name="resultSelector">Función que va a crear un valor de resultado a partir de cada grupo.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">Tipo del valor de resultado devuelto por <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="resultSelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}})">
      <summary>Establece una correlación entre los elementos de dos secuencias basándose en la igualdad de clave y agrupa los resultados.El comparador de igualdad predeterminado se usa para comparar claves.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene elementos de tipo <paramref name="TResult" /> que se han obtenido al realizar una combinación agrupada de dos secuencias.</returns>
      <param name="outer">Primera secuencia que se va a combinar.</param>
      <param name="inner">Secuencia que se va a combinar con la primera secuencia.</param>
      <param name="outerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la primera secuencia.</param>
      <param name="innerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la segunda secuencia.</param>
      <param name="resultSelector">Función para crear un elemento de resultado a partir de un elemento de la primera secuencia y una colección de elementos coincidentes de la segunda.</param>
      <typeparam name="TOuter">Tipo de los elementos de la primera secuencia.</typeparam>
      <typeparam name="TInner">Tipo de los elementos de la segunda secuencia.</typeparam>
      <typeparam name="TKey">Tipo de las claves devueltas por las funciones del selector de claves.</typeparam>
      <typeparam name="TResult">Tipo de los elementos del resultado.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> o <paramref name="inner" /> o <paramref name="outerKeySelector" /> o <paramref name="innerKeySelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Establece una correlación entre los elementos de dos secuencias basándose en la igualdad de clave y agrupa los resultados.Se usa un objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado para comparar claves.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene elementos de tipo <paramref name="TResult" /> que se han obtenido al realizar una combinación agrupada de dos secuencias.</returns>
      <param name="outer">Primera secuencia que se va a combinar.</param>
      <param name="inner">Secuencia que se va a combinar con la primera secuencia.</param>
      <param name="outerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la primera secuencia.</param>
      <param name="innerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la segunda secuencia.</param>
      <param name="resultSelector">Función para crear un elemento de resultado a partir de un elemento de la primera secuencia y una colección de elementos coincidentes de la segunda.</param>
      <param name="comparer">Comparador que va a aplicar un algoritmo hash y a comparar las claves.</param>
      <typeparam name="TOuter">Tipo de los elementos de la primera secuencia.</typeparam>
      <typeparam name="TInner">Tipo de los elementos de la segunda secuencia.</typeparam>
      <typeparam name="TKey">Tipo de las claves devueltas por las funciones del selector de claves.</typeparam>
      <typeparam name="TResult">Tipo de los elementos del resultado.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> o <paramref name="inner" /> o <paramref name="outerKeySelector" /> o <paramref name="innerKeySelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Proporciona la intersección de conjuntos de dos secuencias utilizando el comparador de igualdad predeterminado para comparar los valores.</summary>
      <returns>Una secuencia que contiene la intersección de conjuntos de las dos secuencias.</returns>
      <param name="source1">Secuencia de la que se devuelven los elementos que también aparecen en <paramref name="source2" />.</param>
      <param name="source2">Secuencia de la que se devuelven los elementos que también aparecen en la primera secuencia.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Proporciona la intersección de conjuntos de dos secuencias utilizando el objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado para comparar los valores.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IQueryable`1" /> que contiene la intersección de conjuntos de las dos secuencias.</returns>
      <param name="source1">Una interfaz <see cref="T:System.Linq.IQueryable`1" /> de la que se devuelven los elementos que también aparecen en <paramref name="source2" />.</param>
      <param name="source2">Una interfaz <see cref="T:System.Collections.Generic.IEnumerable`1" /> de la que se devuelven los elementos que también aparecen en la primera secuencia.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar valores.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}})">
      <summary>Establece la correlación de dos secuencias basándose en claves coincidentes.El comparador de igualdad predeterminado se usa para comparar claves.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IQueryable`1" /> que tiene elementos de tipo <paramref name="TResult" /> que se han obtenido al realizar una combinación interna de dos secuencias.</returns>
      <param name="outer">Primera secuencia que se va a combinar.</param>
      <param name="inner">Secuencia que se va a combinar con la primera secuencia.</param>
      <param name="outerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la primera secuencia.</param>
      <param name="innerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la segunda secuencia.</param>
      <param name="resultSelector">Función que va a crear un elemento de resultado a partir de dos elementos coincidentes.</param>
      <typeparam name="TOuter">Tipo de los elementos de la primera secuencia.</typeparam>
      <typeparam name="TInner">Tipo de los elementos de la segunda secuencia.</typeparam>
      <typeparam name="TKey">Tipo de las claves devueltas por las funciones del selector de claves.</typeparam>
      <typeparam name="TResult">Tipo de los elementos del resultado.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> o <paramref name="inner" /> o <paramref name="outerKeySelector" /> o <paramref name="innerKeySelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Establece la correlación de dos secuencias basándose en claves coincidentes.Se usa un objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado para comparar claves.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IQueryable`1" /> que tiene elementos de tipo <paramref name="TResult" /> que se han obtenido al realizar una combinación interna de dos secuencias.</returns>
      <param name="outer">Primera secuencia que se va a combinar.</param>
      <param name="inner">Secuencia que se va a combinar con la primera secuencia.</param>
      <param name="outerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la primera secuencia.</param>
      <param name="innerKeySelector">Función para extraer la clave de combinación a partir de cada elemento de la segunda secuencia.</param>
      <param name="resultSelector">Función que va a crear un elemento de resultado a partir de dos elementos coincidentes.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que va a aplicar un algoritmo hash y a comparar las claves.</param>
      <typeparam name="TOuter">Tipo de los elementos de la primera secuencia.</typeparam>
      <typeparam name="TInner">Tipo de los elementos de la segunda secuencia.</typeparam>
      <typeparam name="TKey">Tipo de las claves devueltas por las funciones del selector de claves.</typeparam>
      <typeparam name="TResult">Tipo de los elementos del resultado.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> o <paramref name="inner" /> o <paramref name="outerKeySelector" /> o <paramref name="innerKeySelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el último elemento de una secuencia.</summary>
      <returns>El valor de la última posición de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver el último elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">La secuencia de origen está vacía.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve el último elemento de una secuencia que satisface una condición especificada.</summary>
      <returns>El último elemento de <paramref name="source" /> que pasa la prueba especificada en <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un elemento.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">Ningún elemento satisface la condición de <paramref name="predicate" />.O bienLa secuencia de origen está vacía.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el último elemento de una secuencia o un valor predeterminado si la secuencia no contiene elementos.</summary>
      <returns>default (<paramref name="TSource" />) si <paramref name="source" /> está vacío; de lo contrario, el último elemento de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver el último elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve el último elemento de una secuencia que satisface una condición o un valor predeterminado si no se encuentra dicho elemento.</summary>
      <returns>default (<paramref name="TSource" />) si <paramref name="source" /> está vacío o si ningún elemento pasa la prueba de la función de predicado; de lo contrario, el último elemento de <paramref name="source" /> que pasa la prueba de la función de predicado.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un elemento.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve un valor <see cref="T:System.Int64" /> que representa el número total de elementos de una secuencia.</summary>
      <returns>El número de elementos de <paramref name="source" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos que se van a contar.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">Número de elementos supera el valor <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve un valor <see cref="T:System.Int64" /> que representa el número de elementos de una secuencia que satisfacen una condición.</summary>
      <returns>El número de elementos de <paramref name="source" /> que satisfacen la condición de la función de predicado.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos que se van a contar.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
      <exception cref="T:System.OverflowException">El número de elementos que coinciden supera el valor <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el valor máximo de una interfaz <see cref="T:System.Linq.IQueryable`1" /> genérica.</summary>
      <returns>El valor máximo de la secuencia.</returns>
      <param name="source">Secuencia de valores cuyo valor máximo se va a determinar.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Invoca una función de proyección en cada elemento de una interfaz <see cref="T:System.Linq.IQueryable`1" /> genérica y devuelve el valor máximo resultante.</summary>
      <returns>El valor máximo de la secuencia.</returns>
      <param name="source">Secuencia de valores cuyo valor máximo se va a determinar.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Tipo del valor devuelto por la función representada por <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el valor mínimo de una interfaz <see cref="T:System.Linq.IQueryable`1" /> genérica.</summary>
      <returns>El valor mínimo de la secuencia.</returns>
      <param name="source">Secuencia de valores cuyo valor mínimo se va a determinar.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Invoca una función de proyección en cada elemento de una interfaz <see cref="T:System.Linq.IQueryable`1" /> genérica y devuelve el valor mínimo resultante.</summary>
      <returns>El valor mínimo de la secuencia.</returns>
      <param name="source">Secuencia de valores cuyo valor mínimo se va a determinar.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Tipo del valor devuelto por la función representada por <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OfType``1(System.Linq.IQueryable)">
      <summary>Filtra los elementos de <see cref="T:System.Linq.IQueryable" /> en función de un tipo especificado.</summary>
      <returns>Colección que contiene los elementos de <paramref name="source" /> que son de tipo <paramref name="TResult" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable" /> cuyos elementos se van a filtrar.</param>
      <typeparam name="TResult">El tipo según el cual se van a filtrar los elementos de la secuencia.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Ordena de manera ascendente los elementos de una secuencia en función de una clave.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IOrderedQueryable`1" /> cuyos elementos se ordenan con arreglo a una clave.</returns>
      <param name="source">Secuencia de valores que se va a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de un elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelto por la función representada por <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Ordena de manera ascendente los elementos de una secuencia utilizando un comparador especificado.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IOrderedQueryable`1" /> cuyos elementos se ordenan con arreglo a una clave.</returns>
      <param name="source">Secuencia de valores que se va a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de un elemento.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelto por la función representada por <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Ordena de manera descendente los elementos de una secuencia en función de una clave.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IOrderedQueryable`1" /> cuyos elementos se ordenan de manera descendente con arreglo a una clave.</returns>
      <param name="source">Secuencia de valores que se va a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de un elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelto por la función representada por <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Ordena de manera descendente los elementos de una secuencia utilizando un comparador especificado.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IOrderedQueryable`1" /> cuyos elementos se ordenan de manera descendente con arreglo a una clave.</returns>
      <param name="source">Secuencia de valores que se va a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de un elemento.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelto por la función representada por <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Reverse``1(System.Linq.IQueryable{``0})">
      <summary>Invierte el orden de los elementos de una secuencia.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se corresponden en orden inverso con los de la secuencia de entrada.</returns>
      <param name="source">Secuencia de valores que se va a invertir.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Proyecta cada elemento de una secuencia en un nuevo formulario.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos son el resultado de invocar una función de proyección en cada elemento de <paramref name="source" />.</returns>
      <param name="source">Secuencia de valores que se va a proyectar.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Tipo del valor devuelto por la función representada por <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,``1}})">
      <summary>Proyecta cada elemento de una secuencia en un nuevo formulario incorporando el índice del elemento.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos son el resultado de invocar una función de proyección en cada elemento de <paramref name="source" />.</returns>
      <param name="source">Secuencia de valores que se va a proyectar.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Tipo del valor devuelto por la función representada por <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Proyecta cada elemento de una secuencia en <see cref="T:System.Collections.Generic.IEnumerable`1" /> e invoca una función del selector de resultados en cada elemento.Los valores resultantes de cada secuencia intermedia se combinan en una única secuencia unidimensional y se devuelven.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos son el resultado de invocar la función de proyección uno a varios <paramref name="collectionSelector" /> en cada elemento de <paramref name="source" /> y de asignar a continuación cada uno de los elementos de la secuencia y sus elementos de <paramref name="source" /> correspondientes a un elemento de resultado.</returns>
      <param name="source">Secuencia de valores que se va a proyectar.</param>
      <param name="collectionSelector">Función de proyección que se va a aplicar a cada elemento de la secuencia de entrada.</param>
      <param name="resultSelector">Función de proyección que se va a aplicar a cada elemento de la secuencia intermedia.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">El tipo de los elementos intermedios recopilados por la función representada por <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">Tipo de los elementos de la secuencia resultante.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="collectionSelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>Proyecta cada elemento de una secuencia en una interfaz <see cref="T:System.Collections.Generic.IEnumerable`1" /> y combina las secuencias resultantes en una secuencia.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos son el resultado de invocar una función de proyección uno a varios en cada elemento de la secuencia de entrada.</returns>
      <param name="source">Secuencia de valores que se va a proyectar.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Tipo de los elementos de la secuencia devuelta por la función representada por <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Proyecta cada elemento de una secuencia en una interfaz <see cref="T:System.Collections.Generic.IEnumerable`1" /> que incorpora el índice del elemento de origen que lo generó.Una función del selector de resultados se invoca en cada elemento de todas las secuencias intermedias y, a continuación, los valores resultantes se combinan en una única secuencia unidimensional y se devuelven.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos son el resultado de invocar la función de proyección uno a varios <paramref name="collectionSelector" /> en cada elemento de <paramref name="source" /> y de asignar a continuación cada uno de los elementos de la secuencia y sus elementos de <paramref name="source" /> correspondientes a un elemento de resultado.</returns>
      <param name="source">Secuencia de valores que se va a proyectar.</param>
      <param name="collectionSelector">Función de proyección que se va a aplicar a cada elemento de la secuencia de entrada; el segundo parámetro de esta función representa el índice del elemento de origen.</param>
      <param name="resultSelector">Función de proyección que se va a aplicar a cada elemento de la secuencia intermedia.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">El tipo de los elementos intermedios recopilados por la función representada por <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">Tipo de los elementos de la secuencia resultante.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="collectionSelector" /> o <paramref name="resultSelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>Proyecta cada elemento de una secuencia en una interfaz <see cref="T:System.Collections.Generic.IEnumerable`1" /> y combina las secuencias resultantes en una secuencia.El índice de cada elemento de origen se utiliza en el formulario proyectado de ese elemento.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos son el resultado de invocar una función de proyección uno a varios en cada elemento de la secuencia de entrada.</returns>
      <param name="source">Secuencia de valores que se va a proyectar.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento; el segundo parámetro de esta función representa el índice del elemento de origen.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Tipo de los elementos de la secuencia devuelta por la función representada por <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Determina si dos secuencias son iguales utilizando el comparador de igualdad predeterminado para comparar los elementos.</summary>
      <returns>true si las dos secuencias de origen tienen la misma longitud y sus elementos correspondientes son iguales; de lo contrario, false.</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a comparar con los de <paramref name="source2" />.</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> cuyos elementos se van a comparar con los de la primera secuencia.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determina si dos secuencias son iguales utilizando una interfaz <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificada para comparar los elementos.</summary>
      <returns>true si las dos secuencias de origen tienen la misma longitud y sus elementos correspondientes son iguales; de lo contrario, false.</returns>
      <param name="source1">
        <see cref="T:System.Linq.IQueryable`1" /> cuyos elementos se van a comparar con los de <paramref name="source2" />.</param>
      <param name="source2">
        <see cref="T:System.Collections.Generic.IEnumerable`1" /> cuyos elementos se van a comparar con los de la primera secuencia.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> que se va a utilizar para comparar elementos.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el único elemento de una secuencia y produce una excepción si no hay exactamente un elemento en la secuencia.</summary>
      <returns>El único elemento de la secuencia de entrada.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyo único elemento se va a devolver.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> tiene más de un elemento.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve el único elemento de una secuencia que cumpla la condición especificada y produce una excepción si más de un elemento la cumple.</summary>
      <returns>El único elemento de la secuencia de entrada que satisface la condición de <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un único elemento.</param>
      <param name="predicate">Función que va a probar si un elemento satisface una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">Ningún elemento satisface la condición de <paramref name="predicate" />.O bienVarios elementos satisfacen la condición de <paramref name="predicate" />.O bienLa secuencia de origen está vacía.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Devuelve el único elemento de una secuencia o un valor predeterminado si la secuencia está vacía; este método produce una excepción si hay más de un elemento en la secuencia.</summary>
      <returns>El único elemento de la secuencia de entrada o default(<paramref name="TSource" />) si la secuencia no contiene ningún elemento.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> cuyo único elemento se va a devolver.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> tiene más de un elemento.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve el único elemento de una secuencia que cumpla la condición especificada, o bien, un valor predeterminado si ese elemento no existe; este método produce una excepción si varios elementos cumplen la condición.</summary>
      <returns>El único elemento de la secuencia de entrada que satisface la condición de <paramref name="predicate" /> o default(<paramref name="TSource" />) si no se encuentra dicho elemento.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se va a devolver un único elemento.</param>
      <param name="predicate">Función que va a probar si un elemento satisface una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
      <exception cref="T:System.InvalidOperationException">Varios elementos satisfacen la condición de <paramref name="predicate" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Skip``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Omite un número especificado de elementos en una secuencia y, a continuación, devuelve los elementos restantes.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos que hay después del índice especificado en la secuencia de entrada.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se van a devolver los elementos.</param>
      <param name="count">Número de elementos que se van a omitir antes de devolver los elementos restantes.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Omite los elementos de una secuencia en tanto que el valor de una condición especificada sea true y, a continuación, devuelve los elementos restantes.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de <paramref name="source" /> comenzando por el primer elemento de la serie lineal que no pasa la prueba especificada <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se van a devolver los elementos.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Omite los elementos de una secuencia en tanto que el valor de una condición especificada sea true y, a continuación, devuelve los elementos restantes.El índice del elemento se usa en la lógica de la función de predicado.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de <paramref name="source" /> comenzando por el primer elemento de la serie lineal que no pasa la prueba especificada <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> del que se van a devolver los elementos.</param>
      <param name="predicate">Función que va a probar cada elemento para determinar si satisface una condición; el segundo parámetro de esta función representa el índice del elemento de origen.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Decimal})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Decimal" />.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Decimal" /> cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Double})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Double" />.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Double" /> cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int32})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Int32" />.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Int32" /> cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int64})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Int64" />.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Int64" /> cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Decimal" /> que aceptan valores NULL.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Decimal" /> que aceptan valores NULL cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Double" /> que aceptan valores NULL.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Double" /> que aceptan valores NULL cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Int32" /> que aceptan valores NULL.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Una secuencia de valores <see cref="T:System.Int32" /> que aceptan valores NULL cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Int64" /> que aceptan valores NULL.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Int64" /> que aceptan valores NULL cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Single" /> que aceptan valores NULL.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Single" /> que aceptan valores NULL cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Single})">
      <summary>Calcula la suma de una secuencia de valores <see cref="T:System.Single" />.</summary>
      <returns>La suma de los valores de la secuencia.</returns>
      <param name="source">Secuencia de valores <see cref="T:System.Single" /> cuya suma se va a calcular.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Decimal" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Double" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Int32" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Int64" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Decimal" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Double" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Int32" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Int64" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
      <exception cref="T:System.OverflowException">La suma es mayor que <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Single" /> que aceptan valores NULL que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>Calcula la suma de la secuencia de valores <see cref="T:System.Single" /> que se obtiene al invocar una función de proyección en cada elemento de la secuencia de entrada.</summary>
      <returns>La suma de los valores proyectados.</returns>
      <param name="source">Secuencia de valores de tipo <paramref name="TSource" />.</param>
      <param name="selector">Función de proyección que se va a aplicar a cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="selector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Take``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Devuelve un número especificado de elementos contiguos desde el principio de una secuencia.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene el número especificado de elementos desde el comienzo de <paramref name="source" />.</returns>
      <param name="source">Secuencia cuyos elementos se van a devolver.</param>
      <param name="count">Número de elementos que se van a devolver.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Devuelve los elementos de una secuencia en tanto que el valor de una condición especificada sea true.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de la secuencia de entrada que se encuentran antes del elemento en el que la prueba especificada <paramref name="predicate" /> no se realiza correctamente.</returns>
      <param name="source">Secuencia cuyos elementos se van a devolver.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Devuelve los elementos de una secuencia en tanto que el valor de una condición especificada sea true.El índice del elemento se usa en la lógica de la función de predicado.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de la secuencia de entrada que se encuentran antes del elemento en el que la prueba especificada <paramref name="predicate" /> no se realiza correctamente.</returns>
      <param name="source">Secuencia cuyos elementos se van a devolver.</param>
      <param name="predicate">Función que va a probar cada elemento para determinar si satisface una condición; el segundo parámetro de la función representa el índice del elemento de la secuencia de origen.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Realiza una clasificación posterior de los elementos de una secuencia en orden ascendentes con arreglo a una clave.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IOrderedQueryable`1" /> cuyos elementos se ordenan con arreglo a una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IOrderedQueryable`1" /> que contiene los elementos que se van a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Realiza una clasificación posterior de los elementos de una secuencia en orden ascendente utilizando un comparador especificado.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IOrderedQueryable`1" /> cuyos elementos se ordenan con arreglo a una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IOrderedQueryable`1" /> que contiene los elementos que se van a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de cada elemento.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Realiza una clasificación posterior de los elementos de una secuencia en orden descendente con arreglo a una clave.</summary>
      <returns>Una interfaz <see cref="T:System.Linq.IOrderedQueryable`1" /> cuyos elementos se ordenan de manera descendente con arreglo a una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IOrderedQueryable`1" /> que contiene los elementos que se van a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de cada elemento.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave devuelta por la función representada en <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Realiza una clasificación posterior de los elementos de una secuencia en orden descendente utilizando un comparador especificado.</summary>
      <returns>Colección cuyos elementos están ordenados de manera descendente de acuerdo con una clave.</returns>
      <param name="source">
        <see cref="T:System.Linq.IOrderedQueryable`1" /> que contiene los elementos que se van a ordenar.</param>
      <param name="keySelector">Función para extraer una clave a partir de cada elemento.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IComparer`1" /> para comparar claves.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Tipo de la clave que la función <paramref name="keySelector" /> devuelve.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="keySelector" /> o <paramref name="comparer" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Proporciona la unión de conjuntos de dos secuencias utilizando el comparador de igualdad predeterminado.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de las dos secuencias de entrada, excepto los duplicados.</returns>
      <param name="source1">Secuencia cuyos elementos forman el primer conjunto de la operación de unión.</param>
      <param name="source2">Secuencia cuyos elementos forman el segundo conjunto de la operación de unión.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Proporciona la unión de conjuntos de dos secuencias a través de un objeto <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> especificado.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de las dos secuencias de entrada, excepto los duplicados.</returns>
      <param name="source1">Secuencia cuyos elementos forman el primer conjunto de la operación de unión.</param>
      <param name="source2">Secuencia cuyos elementos forman el segundo conjunto de la operación de unión.</param>
      <param name="comparer">
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> para comparar valores.</param>
      <typeparam name="TSource">Tipo de los elementos de las secuencias de entrada.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> o <paramref name="source2" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Filtra una secuencia de valores en función de un predicado.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de la secuencia de entrada que satisfacen la condición especificada en <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> que se va a filtrar.</param>
      <param name="predicate">Función para probar cada elemento de una condición.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Filtra una secuencia de valores en función de un predicado.El índice de cada elemento se usa en la lógica de la función de predicado.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene los elementos de la secuencia de entrada que satisfacen la condición especificada en <paramref name="predicate" />.</returns>
      <param name="source">
        <see cref="T:System.Linq.IQueryable`1" /> que se va a filtrar.</param>
      <param name="predicate">Función que va a probar cada elemento para determinar si satisface una condición; el segundo parámetro de la función representa el índice del elemento de la secuencia de origen.</param>
      <typeparam name="TSource">Tipo de los elementos de <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> o <paramref name="predicate" /> es null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Zip``3(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Combina dos secuencias utilizando la función de predicado especificada.</summary>
      <returns>
        <see cref="T:System.Linq.IQueryable`1" /> que contiene elementos combinados de las dos secuencias de entrada.</returns>
      <param name="source1">Primera secuencia que se va a combinar.</param>
      <param name="source2">Segunda secuencia que se va a combinar.</param>
      <param name="resultSelector">Función que especifica cómo combinar los elementos de las dos secuencias.</param>
      <typeparam name="TFirst">Tipo de los elementos de la primera secuencia de entrada.</typeparam>
      <typeparam name="TSecond">Tipo de los elementos de la segunda secuencia de entrada.</typeparam>
      <typeparam name="TResult">Tipo de los elementos de la secuencia de resultados.</typeparam>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="source1" /> o de <paramref name="source2 " />es null.</exception>
    </member>
  </members>
</doc>