﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Requests</name>
  </assembly>
  <members>
    <member name="T:System.Net.HttpWebRequest">
      <summary>提供 <see cref="T:System.Net.WebRequest" /> 类的 HTTP 特定的实现。</summary>
    </member>
    <member name="M:System.Net.HttpWebRequest.Abort">
      <summary>取消对 Internet 资源的请求。</summary>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Accept">
      <summary>获取或设置 Accept HTTP 标头的值。</summary>
      <returns>Accept HTTP 标头的值。默认值为 null。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.AllowReadStreamBuffering">
      <summary>获取或设置一个值，该值指示是否对从 Internet 资源接收的数据进行缓冲处理。</summary>
      <returns>true要缓冲接收到来自 Internet 资源 ；否则为false。true 允许对从 Internet 资源接收的数据进行缓冲处理，false 禁用缓冲处理。默认值为 true。</returns>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>开始对用来写入数据的 <see cref="T:System.IO.Stream" /> 对象的异步请求。</summary>
      <returns>引用该异步请求的 <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> 委托。</param>
      <param name="state">此请求的状态对象。</param>
      <exception cref="T:System.Net.ProtocolViolationException">
        <see cref="P:System.Net.HttpWebRequest.Method" /> 属性为 GET 或 HEAD。- 或 - <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> 为 true，<see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> 为 false，<see cref="P:System.Net.HttpWebRequest.ContentLength" /> 为 -1，<see cref="P:System.Net.HttpWebRequest.SendChunked" /> 为 false，<see cref="P:System.Net.HttpWebRequest.Method" /> 为 POST 或 PUT。</exception>
      <exception cref="T:System.InvalidOperationException">流正由上一个 <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> 调用使用。- 或 - <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> 被设置为一个值，并且 <see cref="P:System.Net.HttpWebRequest.SendChunked" /> 为 false。- 或 -线程池中的线程即将用完。</exception>
      <exception cref="T:System.NotSupportedException">请求缓存验证程序指示对此请求的响应可从缓存中提供；但是写入数据的请求不得使用缓存。如果您正在使用错误实现的自定义缓存验证程序，则会发生此异常。</exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> 以前被调用过。</exception>
      <exception cref="T:System.ObjectDisposedException">在 .NET Compact Framework 应用程序中，未正确获得和关闭一个内容长度为零的请求流。有关处理内容长度为零的请求的更多信息，请参见 Network Programming in the .NET Compact Framework。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>开始对 Internet 资源的异步请求。</summary>
      <returns>引用对响应的异步请求的 <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> 委托</param>
      <param name="state">此请求的状态对象。</param>
      <exception cref="T:System.InvalidOperationException">流正由上一个 <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> 调用使用- 或 - <see cref="P:System.Net.HttpWebRequest.TransferEncoding" /> 被设置为一个值，并且 <see cref="P:System.Net.HttpWebRequest.SendChunked" /> 为 false。- 或 -线程池中的线程即将用完。 </exception>
      <exception cref="T:System.Net.ProtocolViolationException">
        <see cref="P:System.Net.HttpWebRequest.Method" /> 为 GET 或 HEAD，且 <see cref="P:System.Net.HttpWebRequest.ContentLength" /> 大于零或 <see cref="P:System.Net.HttpWebRequest.SendChunked" /> 为 true。- 或 - <see cref="P:System.Net.HttpWebRequest.KeepAlive" /> 为 true，<see cref="P:System.Net.HttpWebRequest.AllowWriteStreamBuffering" /> 为 false，同时 <see cref="P:System.Net.HttpWebRequest.ContentLength" /> 为 -1，<see cref="P:System.Net.HttpWebRequest.SendChunked" /> 为 false，或者 <see cref="P:System.Net.HttpWebRequest.Method" /> 为 POST 或 PUT。- 或 -该 <see cref="T:System.Net.HttpWebRequest" /> 具有实体，但不用调用 <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> 方法调用 <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> 方法。- 或 -<see cref="P:System.Net.HttpWebRequest.ContentLength" /> 大于零，但应用程序不会写入所有承诺的数据。</exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> 以前被调用过。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.DnsPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Diagnostics.PerformanceCounterPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContentType">
      <summary>获取或设置 Content-type HTTP 标头的值。</summary>
      <returns>Content-type HTTP 标头的值。默认值为 null。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.ContinueTimeout">
      <summary>获取或设置在接收到来自服务器的 100 次连续响应之前要等待的超时（以毫秒为单位）。</summary>
      <returns>在接收到 100-Continue 之前要等待的超时（以毫秒为单位）。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.CookieContainer">
      <summary>获取或设置与此请求关联的 Cookie。</summary>
      <returns>包含与此请求关联的 Cookie 的 <see cref="T:System.Net.CookieContainer" />。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Credentials">
      <summary>获取或设置请求的身份验证信息。</summary>
      <returns>包含与该请求关联的身份验证凭据的 <see cref="T:System.Net.ICredentials" />。默认值为 null。</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>结束对用于写入数据的 <see cref="T:System.IO.Stream" /> 对象的异步请求。</summary>
      <returns>用来写入请求数据的 <see cref="T:System.IO.Stream" />。</returns>
      <param name="asyncResult">对流的挂起请求。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 为 null。</exception>
      <exception cref="T:System.IO.IOException">请求未完成，没有可用的流。 </exception>
      <exception cref="T:System.ArgumentException">当前实例没有从 <see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" /> 调用返回 <paramref name="asyncResult" />。</exception>
      <exception cref="T:System.InvalidOperationException">以前使用 <paramref name="asyncResult" /> 调用过此方法。 </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> 以前被调用过。- 或 -处理请求时发生错误。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.HttpWebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>结束对 Internet 资源的异步请求。</summary>
      <returns>包含来自 Internet 资源的响应的 <see cref="T:System.Net.WebResponse" />。</returns>
      <param name="asyncResult">挂起的对响应的请求。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="asyncResult" /> 为 null。</exception>
      <exception cref="T:System.InvalidOperationException">以前使用 <paramref name="asyncResult." /> 调用过此方法。- 或 -<see cref="P:System.Net.HttpWebRequest.ContentLength" /> 属性大于 0，但是数据尚未写入请求流。 </exception>
      <exception cref="T:System.Net.WebException">
        <see cref="M:System.Net.HttpWebRequest.Abort" /> 以前被调用过。- 或 -处理请求时发生错误。 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="asyncResult" /> was not returned by the current instance from a call to <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.HaveResponse">
      <summary>获取一个值，该值指示是否收到了来自 Internet 资源的响应。</summary>
      <returns>如果接收到了响应，则为 true，否则为 false。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.Headers">
      <summary>指定构成 HTTP 标头的名称/值对的集合。</summary>
      <returns>包含构成 HTTP 请求标头的名称/值对的 <see cref="T:System.Net.WebHeaderCollection" />。</returns>
      <exception cref="T:System.InvalidOperationException">已通过调用 <see cref="M:System.Net.HttpWebRequest.GetRequestStream" />、<see cref="M:System.Net.HttpWebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)" />、<see cref="M:System.Net.HttpWebRequest.GetResponse" /> 或 <see cref="M:System.Net.HttpWebRequest.BeginGetResponse(System.AsyncCallback,System.Object)" /> 方法启动了该请求。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebRequest.Method">
      <summary>获取或设置请求的方法。</summary>
      <returns>用于联系 Internet 资源的请求方法。默认值为 GET。</returns>
      <exception cref="T:System.ArgumentException">未提供任何方法。- 或 -方法字符串包含无效字符。</exception>
    </member>
    <member name="P:System.Net.HttpWebRequest.RequestUri">
      <summary>获取请求的原始统一资源标识符 (URI)。</summary>
      <returns>一个 <see cref="T:System.Uri" />，其中包含传递给 <see cref="M:System.Net.WebRequest.Create(System.String)" /> 方法的 Internet 资源的 URI。</returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.SupportsCookieContainer">
      <summary>获取一个值，该值指示请求是否为 <see cref="T:System.Net.CookieContainer" /> 提供支持。</summary>
      <returns>true如果请求提供了对支持<see cref="T:System.Net.CookieContainer" />；否则为false。如果支持 <see cref="T:System.Net.CookieContainer" />，则为 true；否则为 false。 </returns>
    </member>
    <member name="P:System.Net.HttpWebRequest.UseDefaultCredentials">
      <summary>获取或设置一个 <see cref="T:System.Boolean" /> 值，该值控制默认凭据是否随请求一起发送。</summary>
      <returns>如果使用默认凭据，则为 true；否则为 false。默认值为 false。</returns>
      <exception cref="T:System.InvalidOperationException">您尝试在该请求发送之后设置此属性。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Read="USERNAME" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.HttpWebResponse">
      <summary>提供 <see cref="T:System.Net.WebResponse" /> 类的 HTTP 特定的实现。</summary>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentLength">
      <summary>获取请求返回的内容的长度。</summary>
      <returns>由请求所返回的字节数。内容长度不包括标头信息。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ContentType">
      <summary>获取响应的内容类型。</summary>
      <returns>包含响应的内容类型的字符串。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Cookies">
      <summary>获取或设置与此响应关联的 Cookie。</summary>
      <returns>
        <see cref="T:System.Net.CookieCollection" />，包含与此响应关联的 Cookie。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="M:System.Net.HttpWebResponse.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.Net.HttpWebResponse" /> 使用的非托管资源，并可根据需要释放托管资源。</summary>
      <param name="disposing">如果释放托管资源和非托管资源，则为 true；如果仅释放非托管资源，则为 false。</param>
    </member>
    <member name="M:System.Net.HttpWebResponse.GetResponseStream">
      <summary>获取流，该流用于读取来自服务器的响应的体。</summary>
      <returns>一个 <see cref="T:System.IO.Stream" />，包含响应的体。</returns>
      <exception cref="T:System.Net.ProtocolViolationException">没有响应流。</exception>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.HttpWebResponse.Headers">
      <summary>获取来自服务器的与此响应关联的标头。</summary>
      <returns>一个 <see cref="T:System.Net.WebHeaderCollection" />，包含与响应一起返回的标头信息。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.Method">
      <summary>获取用于返回响应的方法。</summary>
      <returns>一个字符串，包含用于返回响应的 HTTP 方法。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.ResponseUri">
      <summary>获取响应请求的 Internet 资源的 URI。</summary>
      <returns>一个 <see cref="T:System.Uri" />，包含响应请求的 Internet 资源的 URI。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusCode">
      <summary>获取响应的状态。</summary>
      <returns>
        <see cref="T:System.Net.HttpStatusCode" /> 值之一。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.StatusDescription">
      <summary>获取与响应一起返回的状态说明。</summary>
      <returns>一个字符串，描述响应的状态。</returns>
      <exception cref="T:System.ObjectDisposedException">已释放当前的实例。</exception>
    </member>
    <member name="P:System.Net.HttpWebResponse.SupportsHeaders">
      <summary>获取指示是否支持标题的值。</summary>
      <returns>返回 <see cref="T:System.Boolean" />。如果标题受支持，则为 true；否则为 false。</returns>
    </member>
    <member name="T:System.Net.IWebRequestCreate">
      <summary>提供用于创建 <see cref="T:System.Net.WebRequest" /> 实例的基接口。</summary>
    </member>
    <member name="M:System.Net.IWebRequestCreate.Create(System.Uri)">
      <summary>创建一个 <see cref="T:System.Net.WebRequest" /> 实例。</summary>
      <returns>一个 <see cref="T:System.Net.WebRequest" /> 实例。</returns>
      <param name="uri">Web 资源的统一资源标识符 (URI)。</param>
      <exception cref="T:System.NotSupportedException">此 <see cref="T:System.Net.IWebRequestCreate" /> 实例不支持在 <paramref name="uri" /> 中指定的请求方案。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="uri" /> 为 null。</exception>
      <exception cref="T:System.UriFormatException">在 .NET for Windows Store 应用程序 或 可移植类库 中，请改为捕获基类异常 <see cref="T:System.FormatException" />。<paramref name="uri" /> 中指定的 URI 不是有效的 URI。</exception>
    </member>
    <member name="T:System.Net.ProtocolViolationException">
      <summary>使用网络协议期间出错时引发的异常。</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor">
      <summary>初始化 <see cref="T:System.Net.ProtocolViolationException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Net.ProtocolViolationException.#ctor(System.String)">
      <summary>用指定消息初始化 <see cref="T:System.Net.ProtocolViolationException" /> 类的新实例。</summary>
      <param name="message">错误消息字符串。</param>
    </member>
    <member name="T:System.Net.WebException">
      <summary>通过可插接协议访问网络期间出错时引发的异常。</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor">
      <summary>初始化 <see cref="T:System.Net.WebException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String)">
      <summary>使用指定的错误消息初始化 <see cref="T:System.Net.WebException" /> 类的新实例。</summary>
      <param name="message">错误消息的文本。</param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception)">
      <summary>用指定的错误信息和嵌套异常初始化 <see cref="T:System.Net.WebException" /> 类的新实例。</summary>
      <param name="message">错误消息的文本。</param>
      <param name="innerException">嵌套异常。</param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Exception,System.Net.WebExceptionStatus,System.Net.WebResponse)">
      <summary>用指定的错误信息、嵌套异常、状态和响应初始化 <see cref="T:System.Net.WebException" /> 类的新实例。</summary>
      <param name="message">错误消息的文本。</param>
      <param name="innerException">嵌套异常。</param>
      <param name="status">
        <see cref="T:System.Net.WebExceptionStatus" /> 值之一。</param>
      <param name="response">包含来自远程主机的响应的 <see cref="T:System.Net.WebResponse" /> 实例。</param>
    </member>
    <member name="M:System.Net.WebException.#ctor(System.String,System.Net.WebExceptionStatus)">
      <summary>用指定的错误信息和状态初始化 <see cref="T:System.Net.WebException" /> 类的新实例。</summary>
      <param name="message">错误消息的文本。</param>
      <param name="status">
        <see cref="T:System.Net.WebExceptionStatus" /> 值之一。</param>
    </member>
    <member name="P:System.Net.WebException.Response">
      <summary>获取远程主机返回的响应。</summary>
      <returns>如果可从 Internet 资源获得响应，则为包含来自 Internet 资源的错误响应的 <see cref="T:System.Net.WebResponse" /> 实例；否则为 null。</returns>
    </member>
    <member name="P:System.Net.WebException.Status">
      <summary>获取响应的状态。</summary>
      <returns>
        <see cref="T:System.Net.WebExceptionStatus" /> 值之一。</returns>
    </member>
    <member name="T:System.Net.WebExceptionStatus">
      <summary>为 <see cref="T:System.Net.WebException" /> 类定义状态代码。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.ConnectFailure">
      <summary>未能在传输级联系到远程服务点。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.MessageLengthLimitExceeded">
      <summary>当发送请求或从服务器接收响应时，会接收到超出指定限制的消息。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Pending">
      <summary>内部异步请求挂起。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.RequestCanceled">
      <summary>请求被取消，<see cref="M:System.Net.WebRequest.Abort" /> 方法被调用，或者发生了不可分类的错误。这是 <see cref="P:System.Net.WebException.Status" /> 的默认值。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.SendFailure">
      <summary>未能将完整请求发送到远程服务器。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.Success">
      <summary>未遇到任何错误。</summary>
    </member>
    <member name="F:System.Net.WebExceptionStatus.UnknownError">
      <summary>发生未知类型的异常。</summary>
    </member>
    <member name="T:System.Net.WebRequest">
      <summary>对统一资源标识符 (URI) 发出请求。这是一个 abstract 类。</summary>
    </member>
    <member name="M:System.Net.WebRequest.#ctor">
      <summary>初始化 <see cref="T:System.Net.WebRequest" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Net.WebRequest.Abort">
      <summary>中止请求 </summary>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetRequestStream(System.AsyncCallback,System.Object)">
      <summary>当在子类中重写时，提供 <see cref="M:System.Net.WebRequest.GetRequestStream" /> 方法的异步版本。</summary>
      <returns>引用该异步请求的 <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> 委托。</param>
      <param name="state">包含此异步请求的状态信息的对象。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.BeginGetResponse(System.AsyncCallback,System.Object)">
      <summary>当在子类中被重写时，开始对 Internet 资源的异步请求。</summary>
      <returns>引用该异步请求的 <see cref="T:System.IAsyncResult" />。</returns>
      <param name="callback">
        <see cref="T:System.AsyncCallback" /> 委托。</param>
      <param name="state">包含此异步请求的状态信息的对象。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="P:System.Net.WebRequest.ContentType">
      <summary>当在子类中被重写时，获取或设置所发送的请求数据的内容类型。</summary>
      <returns>请求数据的内容类型。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.String)">
      <summary>为指定的 URI 方案初始化新的 <see cref="T:System.Net.WebRequest" /> 实例。</summary>
      <returns>特定 URI 方案的 <see cref="T:System.Net.WebRequest" /> 子代。</returns>
      <param name="requestUriString">标识 Internet 资源的 URI。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> has not been registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">In the .NET for Windows Store apps or the Portable Class Library, catch the base class exception, <see cref="T:System.FormatException" />, instead.The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.Create(System.Uri)">
      <summary>为指定的 URI 方案初始化新的 <see cref="T:System.Net.WebRequest" /> 实例。</summary>
      <returns>指定的 URI 方案的 <see cref="T:System.Net.WebRequest" /> 子代。</returns>
      <param name="requestUri">包含请求的资源的 URI 的 <see cref="T:System.Uri" />。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is not registered. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.String)">
      <summary>为指定的 URI 字符串初始化新的 <see cref="T:System.Net.HttpWebRequest" /> 实例。</summary>
      <returns>返回 <see cref="T:System.Net.HttpWebRequest" />。特定 URI 字符串的 <see cref="T:System.Net.HttpWebRequest" /> 实例。</returns>
      <param name="requestUriString">标识 Internet 资源的 URI 字符串。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUriString" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUriString" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUriString" /> is not a valid URI. </exception>
    </member>
    <member name="M:System.Net.WebRequest.CreateHttp(System.Uri)">
      <summary>为指定的 URI 初始化新的 <see cref="T:System.Net.HttpWebRequest" /> 实例。</summary>
      <returns>返回 <see cref="T:System.Net.HttpWebRequest" />。特定 URI 字符串的 <see cref="T:System.Net.HttpWebRequest" /> 实例。</returns>
      <param name="requestUri">标识 Internet 资源的 URI。</param>
      <exception cref="T:System.NotSupportedException">The request scheme specified in <paramref name="requestUri" /> is the http or https scheme. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="requestUri" /> is null. </exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have permission to connect to the requested URI or a URI that the request is redirected to. </exception>
      <exception cref="T:System.UriFormatException">The URI specified in <paramref name="requestUri" /> is not a valid URI. </exception>
    </member>
    <member name="P:System.Net.WebRequest.Credentials">
      <summary>当在子类中被重写时，获取或设置用于对 Internet 资源请求进行身份验证的网络凭据。</summary>
      <returns>包含与该请求关联的身份验证凭据的 <see cref="T:System.Net.ICredentials" />。默认值为 null。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.DefaultWebProxy">
      <summary>获取或设置全局 HTTP 代理。</summary>
      <returns>对 <see cref="T:System.Net.WebRequest" /> 实例的每一次调用所使用的 <see cref="T:System.Net.IWebProxy" />。</returns>
    </member>
    <member name="M:System.Net.WebRequest.EndGetRequestStream(System.IAsyncResult)">
      <summary>当在子类中重写时，返回用于将数据写入 Internet 资源的 <see cref="T:System.IO.Stream" />。</summary>
      <returns>将数据写入的 <see cref="T:System.IO.Stream" />。</returns>
      <param name="asyncResult">引用对流的挂起请求的 <see cref="T:System.IAsyncResult" />。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.EndGetResponse(System.IAsyncResult)">
      <summary>当在子类中重写时，返回 <see cref="T:System.Net.WebResponse" />。</summary>
      <returns>包含对 Internet 请求的响应的 <see cref="T:System.Net.WebResponse" />。</returns>
      <param name="asyncResult">引用对响应的挂起请求的 <see cref="T:System.IAsyncResult" />。</param>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the method, when the method is not overridden in a descendant class. </exception>
    </member>
    <member name="M:System.Net.WebRequest.GetRequestStreamAsync">
      <summary>当在子类中被重写时，将用于写入数据的 <see cref="T:System.IO.Stream" /> 作为异步操作返回到 Internet 资源。</summary>
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。表示异步操作的任务对象。</returns>
    </member>
    <member name="M:System.Net.WebRequest.GetResponseAsync">
      <summary>当在子代类中被重写时，将作为异步操作返回对 Internet 请求的响应。</summary>
      <returns>返回 <see cref="T:System.Threading.Tasks.Task`1" />。表示异步操作的任务对象。</returns>
    </member>
    <member name="P:System.Net.WebRequest.Headers">
      <summary>当在子类中被重写时，获取或设置与请求关联的标头名称/值对的集合。</summary>
      <returns>包含与此请求关联的标头名称/值对的 <see cref="T:System.Net.WebHeaderCollection" />。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Method">
      <summary>当在子类中被重写时，获取或设置要在此请求中使用的协议方法。</summary>
      <returns>要在此请求中使用的协议方法。</returns>
      <exception cref="T:System.NotImplementedException">If the property is not overridden in a descendant class, any attempt is made to get or set the property. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.Proxy">
      <summary>当在子类中被重写时，获取或设置用于访问此 Internet 资源的网络代理。</summary>
      <returns>用于访问 Internet 资源的 <see cref="T:System.Net.IWebProxy" />。</returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebRequest.RegisterPrefix(System.String,System.Net.IWebRequestCreate)">
      <summary>为指定的 URI 注册 <see cref="T:System.Net.WebRequest" /> 子代。</summary>
      <returns>如果注册成功，则为 true；否则为 false。</returns>
      <param name="prefix">
        <see cref="T:System.Net.WebRequest" /> 子代为其提供服务的完整 URI 或 URI 前缀。</param>
      <param name="creator">创建方法，<see cref="T:System.Net.WebRequest" /> 调用它以创建 <see cref="T:System.Net.WebRequest" /> 子代。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="prefix" /> is null-or- <paramref name="creator" /> is null. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
        <IPermission class="System.Net.WebPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.RequestUri">
      <summary>当在子类中被重写时，获取与请求关联的 Internet 资源的 URI。</summary>
      <returns>表示与请求关联的资源的 <see cref="T:System.Uri" />。 </returns>
      <exception cref="T:System.NotImplementedException">Any attempt is made to get or set the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebRequest.UseDefaultCredentials">
      <summary>当在子代类中重写时，获取或设置一个 <see cref="T:System.Boolean" /> 值，该值控制 <see cref="P:System.Net.CredentialCache.DefaultCredentials" /> 是否随请求一起发送。</summary>
      <returns>如果使用默认凭据，则为 true；否则为 false。默认值为 false。</returns>
      <exception cref="T:System.InvalidOperationException">You attempted to set this property after the request was sent.</exception>
      <exception cref="T:System.NotImplementedException">Any attempt is made to access the property, when the property is not overridden in a descendant class. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.WebResponse">
      <summary>提供来自统一资源标识符 (URI) 的响应。这是一个 abstract 类。</summary>
    </member>
    <member name="M:System.Net.WebResponse.#ctor">
      <summary>初始化 <see cref="T:System.Net.WebResponse" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Net.WebResponse.ContentLength">
      <summary>当在子类中重写时，获取或设置接收的数据的内容长度。</summary>
      <returns>从 Internet 资源返回的字节数。</returns>
      <exception cref="T:System.NotSupportedException">当未在子类中重写该属性时，试图获取或设置该属性。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ContentType">
      <summary>当在派生类中重写时，获取或设置接收的数据的内容类型。</summary>
      <returns>包含响应的内容类型的字符串。</returns>
      <exception cref="T:System.NotSupportedException">当未在子类中重写该属性时，试图获取或设置该属性。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.WebResponse.Dispose">
      <summary>释放 <see cref="T:System.Net.WebResponse" /> 对象使用的非托管资源。</summary>
    </member>
    <member name="M:System.Net.WebResponse.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.Net.WebResponse" /> 对象使用的非托管资源，并可根据需要释放托管资源。</summary>
      <param name="disposing">如果释放托管资源和非托管资源，则为 true；如果仅释放非托管资源，则为 false。</param>
    </member>
    <member name="M:System.Net.WebResponse.GetResponseStream">
      <summary>当在子类中重写时，从 Internet 资源返回数据流。</summary>
      <returns>用于从 Internet 资源中读取数据的 <see cref="T:System.IO.Stream" /> 类的实例。</returns>
      <exception cref="T:System.NotSupportedException">当未在子类中重写该方法时，试图访问该方法。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.Headers">
      <summary>当在派生类中重写时，获取与此请求关联的标头名称/值对的集合。</summary>
      <returns>
        <see cref="T:System.Net.WebHeaderCollection" /> 类的实例，包含与此响应关联的标头值。</returns>
      <exception cref="T:System.NotSupportedException">当未在子类中重写该属性时，试图获取或设置该属性。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.ResponseUri">
      <summary>当在派生类中重写时，获取实际响应此请求的 Internet 资源的 URI。</summary>
      <returns>
        <see cref="T:System.Uri" /> 类的实例，包含实际响应此请求的 Internet 资源的 URI。</returns>
      <exception cref="T:System.NotSupportedException">当未在子类中重写该属性时，试图获取或设置该属性。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.WebResponse.SupportsHeaders">
      <summary>获取指示是否支持标题的值。</summary>
      <returns>返回 <see cref="T:System.Boolean" />。如果标题受支持，则为 true；否则为 false。</returns>
    </member>
  </members>
</doc>