<%@ Control CodeBehind="ListView.ascx.cs" Language="c#" AutoEventWireup="false" Inherits="SplendidCRM.Administration.Currencies.ListView" %>
<script runat="server">
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
</script>
<div id="divListView">
	<%@ Register TagPrefix="SplendidCRM" Tagname="ListHeader" Src="~/_controls/ListHeader.ascx" %>
	<SplendidCRM:ListHeader Title="Currencies.LBL_LIST_FORM_TITLE" Runat="Server" />
	
	<asp:Panel CssClass="button-panel" Visible="<%# !PrintView %>" runat="server">
		<asp:Label ID="lblError" CssClass="error" EnableViewState="false" Runat="server" />
	</asp:Panel>
	
	<SplendidCRM:SplendidGrid id="grdMain" AllowPaging="false" AllowSorting="true" EnableViewState="true" runat="server">
		<Columns>
			<asp:TemplateColumn  HeaderText="Currencies.LBL_LIST_NAME" SortExpression="NAME" ItemStyle-Width="20%" ItemStyle-CssClass="listViewTdLinkS1">
				<ItemTemplate>
					<div style="DISPLAY: <%# Sql.ToString(DataBinder.Eval(Container.DataItem, "ISO4217")) == "USD" ? "inline" : "none" %>">
						<%# DataBinder.Eval(Container.DataItem, "NAME") %>
					</div>
					<div style="DISPLAY: <%# Sql.ToString(DataBinder.Eval(Container.DataItem, "ISO4217")) != "USD" ? "inline" : "none" %>">
						<asp:HyperLink Text='<%# DataBinder.Eval(Container.DataItem, "NAME") %>' NavigateUrl='<%# "default.aspx?ID=" + DataBinder.Eval(Container.DataItem, "ID") %>' CssClass="listViewTdLinkS1" Runat="server" />
					</div>
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:BoundColumn     HeaderText="Currencies.LBL_LIST_ISO4217" DataField="ISO4217"         SortExpression="ISO4217"         ItemStyle-Width="10%" />
			<asp:BoundColumn     HeaderText="Currencies.LBL_LIST_SYMBOL"  DataField="SYMBOL"          SortExpression="SYMBOL"          ItemStyle-Width="10%" />
			<asp:BoundColumn     HeaderText="Currencies.LBL_LIST_RATE"    DataField="CONVERSION_RATE" SortExpression="CONVERSION_RATE" ItemStyle-Width="10%" />
			<asp:BoundColumn     HeaderText="Currencies.LBL_LIST_STATUS"  DataField="STATUS"          SortExpression="STATUS"          ItemStyle-Width="10%" />
			<asp:TemplateColumn HeaderText=".LBL_LIST_DEFAULT" ItemStyle-Width="8%" ItemStyle-HorizontalAlign="Left" ItemStyle-Wrap="false">
				<ItemTemplate>
					<div style="DISPLAY: <%# String.Compare(Sql.ToString(DataBinder.Eval(Container.DataItem, "ID")), Sql.ToString(Application["CONFIG.default_currency"]), true) == 0 ? "inline" : "none" %>">
						<%= L10n.Term(".LBL_YES") %>
					</div>
					<div style="DISPLAY: <%# String.Compare(Sql.ToString(DataBinder.Eval(Container.DataItem, "ID")), Sql.ToString(Application["CONFIG.default_currency"]), true) != 0 ? "inline" : "none" %>">
						<%= L10n.Term(".LBL_NO") %>&nbsp;
						(<asp:LinkButton  CommandName="Currencies.MakeDefault" CommandArgument='<%# DataBinder.Eval(Container.DataItem, "ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term(".LNK_MAKE_DEFAULT") %>' Runat="server" />)
					</div>
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn HeaderText="" ItemStyle-Width="8%" ItemStyle-HorizontalAlign="Left" ItemStyle-Wrap="false">
				<ItemTemplate>
					<div style="DISPLAY: <%# Sql.ToString(DataBinder.Eval(Container.DataItem, "ISO4217")) != "USD" ? "inline" : "none" %>">
						<span onclick="return confirm('<%= L10n.TermJavaScript(".NTC_DELETE_CONFIRMATION") %>')">
							<asp:ImageButton CommandName="Currencies.Delete" CommandArgument='<%# DataBinder.Eval(Container.DataItem, "ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term(".LNK_DELETE") %>' SkinID="delete_inline" Runat="server" />
							<asp:LinkButton  CommandName="Currencies.Delete" CommandArgument='<%# DataBinder.Eval(Container.DataItem, "ID") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term(".LNK_DELETE") %>' Runat="server" />
						</span>
					</div>
				</ItemTemplate>
			</asp:TemplateColumn>
		</Columns>
	</SplendidCRM:SplendidGrid>

	<%@ Register TagPrefix="SplendidCRM" Tagname="DumpSQL" Src="~/_controls/DumpSQL.ascx" %>
	<SplendidCRM:DumpSQL ID="ctlDumpSQL" Visible="<%# !PrintView %>" Runat="Server" />
</div>
