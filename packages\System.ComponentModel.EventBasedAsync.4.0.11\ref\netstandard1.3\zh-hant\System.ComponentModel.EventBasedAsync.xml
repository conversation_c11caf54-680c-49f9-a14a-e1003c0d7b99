﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>提供 MethodNameCompleted 事件的資料。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>初始化 <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> 類別的新執行個體。</summary>
      <param name="error">非同步作業期間發生的任何錯誤。</param>
      <param name="cancelled">指出非同步作業是否取消的值。</param>
      <param name="userState">傳遞至 <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> 方法的選擇性使用者提供的狀態物件。</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>取得值，指出非同步作業是否已取消。</summary>
      <returns>如果背景作業已取消，則為 true，否則為 false。預設為 false。</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>取得值，指出非同步作業期間是否發生錯誤。</summary>
      <returns>如果非同步作業期間發生錯誤，則為 <see cref="T:System.Exception" /> 執行個體，否則為 null。</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>如果非同步作業失敗，引發使用者提供的例外狀況。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> 屬性為 true。</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">非同步作業已設定 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 屬性。<see cref="P:System.Exception.InnerException" /> 屬性會保持對 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 的參考。</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>取得非同步工作的唯一識別項。</summary>
      <returns>唯一識別非同步工作的物件參考，如果未設定值，則為 null。</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>表示將處理非同步作業之 MethodNameCompleted 事件的方法。</summary>
      <param name="sender">事件的來源。</param>
      <param name="e">包含事件資料的 <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>追蹤非同步作業的存留期。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>結束非同步作業。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>結束非同步作業的存留期。</summary>
      <exception cref="T:System.InvalidOperationException">先前已針對此工作呼叫 <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" />。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>在適合應用程式模型的執行緒或內容上叫用委派。</summary>
      <param name="d">
        <see cref="T:System.Threading.SendOrPostCallback" /> 物件，包裝作業結束時要呼叫的委派。</param>
      <param name="arg">包含在 <paramref name="d" /> 參數中的委派引數。</param>
      <exception cref="T:System.InvalidOperationException">先前已針對此工作呼叫 <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 為 null。</exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>結束非同步作業的存留期。</summary>
      <param name="d">
        <see cref="T:System.Threading.SendOrPostCallback" /> 物件，包裝作業結束時要呼叫的委派。</param>
      <param name="arg">包含在 <paramref name="d" /> 參數中的委派引數。</param>
      <exception cref="T:System.InvalidOperationException">先前已針對此工作呼叫 <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 為 null。</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>取得傳遞給建構函式的 <see cref="T:System.Threading.SynchronizationContext" /> 物件。</summary>
      <returns>傳遞給建構函式的 <see cref="T:System.Threading.SynchronizationContext" /> 物件。</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>取得或設定用來唯一識別非同步作業的物件。</summary>
      <returns>傳遞至非同步方法引動過程的狀態物件。</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>為支援非同步方法呼叫的類別，提供並行管理。此類別無法被繼承。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>傳回 <see cref="T:System.ComponentModel.AsyncOperation" />，以追蹤特定非同步作業的持續期間。</summary>
      <returns>
        <see cref="T:System.ComponentModel.AsyncOperation" />，可以用來追蹤非同步方法引動過程的持續期間。</returns>
      <param name="userSuppliedState">物件，用來使一項用戶端狀態 (例如工作 ID) 與特定非同步作業產生關聯。</param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>取得或設定非同步作業的同步處理內容。</summary>
      <returns>非同步作業的同步處理內容。</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>在不同執行緒上執行作業。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.BackgroundWorker" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>要求取消暫止的背景作業。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> 為 false。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>取得值，指出應用程式是否已經要求取消背景作業。</summary>
      <returns>如果應用程式已要求取消背景作業，則為 true，否則為 false。預設為 false。</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>當呼叫 <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" /> 時發生。</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>取得值，指出 <see cref="T:System.ComponentModel.BackgroundWorker" /> 是否正在執行非同步作業。</summary>
      <returns>如果 <see cref="T:System.ComponentModel.BackgroundWorker" /> 正在執行非同步作業，則為 true，否則為 false。</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>引發 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件。</summary>
      <param name="e">包含事件資料的 <see cref="T:System.EventArgs" />。</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>引發 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件。</summary>
      <param name="e">包含事件資料的 <see cref="T:System.EventArgs" />。</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>引發 <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> 事件。</summary>
      <param name="e">包含事件資料的 <see cref="T:System.EventArgs" />。</param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>當呼叫 <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" /> 時發生。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>引發 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件。</summary>
      <param name="percentProgress">背景作業的完成百分比，從 0 到 100。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> 屬性設定為 false。</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>引發 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件。</summary>
      <param name="percentProgress">背景作業的完成百分比，從 0 到 100。</param>
      <param name="userState">傳遞至 <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> 的狀態物件。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> 屬性設定為 false。</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>開始執行背景作業。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> 為 true。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>開始執行背景作業。</summary>
      <param name="argument">在 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件處理常式中，執行背景作業時使用的參數。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> 為 true。</exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>當背景作業已完成、取消或引發例外狀況時發生。</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>取得或設定值，指出 <see cref="T:System.ComponentModel.BackgroundWorker" /> 是否可以報告進度更新。</summary>
      <returns>如果 <see cref="T:System.ComponentModel.BackgroundWorker" /> 支援進度更新，則為 true，否則為 false。預設為 false。</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>取得或設定值，指出 <see cref="T:System.ComponentModel.BackgroundWorker" /> 是否支援非同步取消。</summary>
      <returns>如果 <see cref="T:System.ComponentModel.BackgroundWorker" /> 支援取消，則為 true，否則為 false。預設為 false。</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>提供 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件處理常式的資料。</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>初始化 <see cref="T:System.ComponentModel.DoWorkEventArgs" /> 類別的新執行個體。</summary>
      <param name="argument">指定非同步作業的引數。</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>取得值，表示非同步作業的引數。</summary>
      <returns>
        <see cref="T:System.Object" />，表示非同步作業的引數。</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>取得或設定值，表示非同步作業的結果。</summary>
      <returns>
        <see cref="T:System.Object" />，表示非同步作業的結果。</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>表示處理 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件的方法。此類別無法被繼承。</summary>
      <param name="sender">事件的來源。</param>
      <param name="e">包含事件資料的 <see cref="T:System.ComponentModel.DoWorkEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>提供 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件的資料。</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>初始化 <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> 類別的新執行個體。</summary>
      <param name="progressPercentage">非同步工作的完成百分比。</param>
      <param name="userState">唯一的使用者狀態。</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>取得非同步工作的進度百分比。</summary>
      <returns>指出非同步工作進度的百分比值。</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>取得唯一的使用者狀態。</summary>
      <returns>指出使用者狀態的唯一 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>表示處理 <see cref="T:System.ComponentModel.BackgroundWorker" /> 類別的 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件的方法。此類別無法被繼承。</summary>
      <param name="sender">事件的來源。</param>
      <param name="e">包含事件資料的 <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>提供 MethodNameCompleted 事件的資料。</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>初始化 <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> 類別的新執行個體。</summary>
      <param name="result">非同步作業的結果。</param>
      <param name="error">非同步作業期間發生的任何錯誤。</param>
      <param name="cancelled">指出非同步作業是否取消的值。</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>取得值，表示非同步作業的結果。</summary>
      <returns>
        <see cref="T:System.Object" />，表示非同步作業的結果。</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 不是 null。<see cref="P:System.Exception.InnerException" /> 屬性會保持對 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 的參考。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> 為 true。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>取得表示使用者狀態的值。</summary>
      <returns>表示使用者狀態的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>表示處理 <see cref="T:System.ComponentModel.BackgroundWorker" /> 類別的 <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> 事件的方法。</summary>
      <param name="sender">事件的來源。</param>
      <param name="e">包含事件資料的 <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />。</param>
    </member>
  </members>
</doc>