<%@ Control CodeBehind="ImportView.ascx.cs" Language="c#" AutoEventWireup="false"
    Inherits="SplendidCRM.Administration.REPLACE_IMAGES.ImportView" %>
<script runat="server">
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005-2008 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
</script>
<div id="divImportView">    
    <table border="0" cellspacing="0" cellpadding="0" width="100%">
        <tr><td style="font-size:20px">Update Images</td></tr>
        <tr><%--<%# "  " + L10n.Term("Import.LBL_UPLOAD_BUTTON_LABEL" ) + "  " %>--%>
            <td class="dataLabel">
                <asp:Button ID="btnUpload" CommandName="Import.Upload" OnCommand="Page_Command" CssClass="button"
                    Text="Update image by dcn" ToolTip='<%# L10n.Term("Import.LBL_UPLOAD_BUTTON_TITLE" ) %>'
                    runat="server" />
            </td>
        </tr>

        <tr>
            <td class="dataLabel">                
                <asp:Button ID="btnUplodImageByPDFFile" CommandName="Import.UploadPdf" OnCommand="Page_Command" CssClass="button"
                    Text="Update image by pdf name" ToolTip='<%# L10n.Term("Import.LBL_UPLOAD_BUTTON_TITLE" ) %>'
                    runat="server" />
            </td>
        </tr>

        <tr><td style="font-size:20px"><asp:Label ID="successTxt" runat="server"></asp:Label></td></tr>
    </table>
</div>
