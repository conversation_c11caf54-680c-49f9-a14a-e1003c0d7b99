﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Dynamic.Runtime</name>
  </assembly>
  <members>
    <member name="T:System.Dynamic.BinaryOperationBinder">
      <summary>호출 사이트의 동적 이항 연산을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>
        <see cref="T:System.Dynamic.BinaryOperationBinder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="operation">이항 연산 종류입니다.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 이항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 연산의 대상입니다.</param>
      <param name="args">동적 연산의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 이항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 이항 연산의 대상입니다.</param>
      <param name="arg">동적 이항 연산의 오른쪽 피연산자입니다.</param>
    </member>
    <member name="M:System.Dynamic.BinaryOperationBinder.FallbackBinaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 이항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 이항 연산의 대상입니다.</param>
      <param name="arg">동적 이항 연산의 오른쪽 피연산자입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.Operation">
      <summary>이항 연산 종류입니다.</summary>
      <returns>이항 연산 종류를 나타내는 <see cref="T:System.Linq.Expressions.ExpressionType" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Dynamic.BinaryOperationBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식입니다.</returns>
    </member>
    <member name="T:System.Dynamic.BindingRestrictions">
      <summary>동적 바인딩이 유효한 <see cref="T:System.Dynamic.DynamicMetaObject" />의 바인딩 제한 집합을 나타냅니다.</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Combine(System.Collections.Generic.IList{System.Dynamic.DynamicMetaObject})">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스 목록의 바인딩 제한을 하나의 제한 집합으로 결합합니다.</summary>
      <returns>새 바인딩 제한 집합입니다.</returns>
      <param name="contributingObjects">제한을 결합할 <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스 목록입니다.</param>
    </member>
    <member name="F:System.Dynamic.BindingRestrictions.Empty">
      <summary>빈 바인딩 제한 집합을 나타냅니다.이 필드는 읽기 전용입니다.</summary>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetExpressionRestriction(System.Linq.Expressions.Expression)">
      <summary>식에서 임의의 변경할 수 없는 속성을 확인하는 바인딩 제한을 만듭니다.</summary>
      <returns>새 바인딩 제한입니다.</returns>
      <param name="expression">제한을 나타내는 식입니다.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetInstanceRestriction(System.Linq.Expressions.Expression,System.Object)">
      <summary>식에서 개체 인스턴스 ID를 확인하는 바인딩 제한을 만듭니다.</summary>
      <returns>새 바인딩 제한입니다.</returns>
      <param name="expression">테스트할 식입니다.</param>
      <param name="instance">테스트할 정확한 개체 인스턴스입니다.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.GetTypeRestriction(System.Linq.Expressions.Expression,System.Type)">
      <summary>식에서 런타임 형식 ID를 확인하는 바인딩 제한을 만듭니다.</summary>
      <returns>새 바인딩 제한입니다.</returns>
      <param name="expression">테스트할 식입니다.</param>
      <param name="type">테스트할 정확한 형식입니다.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.Merge(System.Dynamic.BindingRestrictions)">
      <summary>바인딩 제한 집합을 현재 바인딩 제한과 병합합니다.</summary>
      <returns>새 바인딩 제한 집합입니다.</returns>
      <param name="restrictions">현재 바인딩 제한과 병합할 제한 집합입니다.</param>
    </member>
    <member name="M:System.Dynamic.BindingRestrictions.ToExpression">
      <summary>바인딩 제한을 나타내는 <see cref="T:System.Linq.Expressions.Expression" />을 만듭니다.</summary>
      <returns>제한을 나타내는 식 트리입니다.</returns>
    </member>
    <member name="T:System.Dynamic.CallInfo">
      <summary>동적 바인딩 프로세스의 인수에 대해 설명합니다.</summary>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.Collections.Generic.IEnumerable{System.String})">
      <summary>동적 바인딩 프로세스의 인수를 나타내는 새 CallInfo를 만듭니다.</summary>
      <param name="argCount">인수의 수입니다.</param>
      <param name="argNames">인수 이름입니다.</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.#ctor(System.Int32,System.String[])">
      <summary>새 PositionalArgumentInfo를 만듭니다.</summary>
      <param name="argCount">인수의 수입니다.</param>
      <param name="argNames">인수 이름입니다.</param>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentCount">
      <summary>인수의 수입니다.</summary>
      <returns>인수의 수입니다.</returns>
    </member>
    <member name="P:System.Dynamic.CallInfo.ArgumentNames">
      <summary>인수 이름입니다.</summary>
      <returns>인수 이름의 읽기 전용 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Dynamic.CallInfo.Equals(System.Object)">
      <summary>지정된 CallInfo 인스턴스와 현재 인스턴스가 같다고 간주할지 여부를 확인합니다.</summary>
      <returns>지정된 인스턴스가 현재 인스턴스와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="obj">현재 인스턴스와 비교할 <see cref="T:System.Dynamic.CallInfo" />의 인스턴스입니다.</param>
    </member>
    <member name="M:System.Dynamic.CallInfo.GetHashCode">
      <summary>현재 <see cref="T:System.Dynamic.CallInfo" />의 해시 함수로 사용됩니다.</summary>
      <returns>현재 <see cref="T:System.Dynamic.CallInfo" />의 해시 코드입니다.</returns>
    </member>
    <member name="T:System.Dynamic.ConvertBinder">
      <summary>호출 사이트의 동적 변환 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.#ctor(System.Type,System.Boolean)">
      <summary>
        <see cref="T:System.Dynamic.ConvertBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="type">변환할 대상 형식입니다.</param>
      <param name="explicit">변환 시 명시적 변환을 고려해야 하면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 변환 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 변환 작업의 대상입니다.</param>
      <param name="args">동적 변환 작업의 인수 배열입니다.</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Explicit">
      <summary>변환 시 명시적 변환을 고려해야 하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>명시적 변환이 있으면 True이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 변환 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 변환 작업의 대상입니다.</param>
    </member>
    <member name="M:System.Dynamic.ConvertBinder.FallbackConvert(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 변환 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 변환 작업의 대상입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Dynamic.ConvertBinder.Type">
      <summary>변환할 대상 형식입니다.</summary>
      <returns>변환할 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.CreateInstanceBinder">
      <summary>호출 사이트의 동적 만들기 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>
        <see cref="T:System.Dynamic.CreateInstanceBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="callInfo">호출 사이트의 인수 서명입니다.</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 만들기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 만들기 작업의 대상입니다.</param>
      <param name="args">동적 만들기 작업의 인수 배열입니다.</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.CallInfo">
      <summary>호출 사이트의 인수 서명을 가져옵니다.</summary>
      <returns>호출 사이트의 인수 서명입니다.</returns>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 만들기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 만들기 작업의 대상입니다.</param>
      <param name="args">동적 만들기 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Dynamic.CreateInstanceBinder.FallbackCreateInstance(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 만들기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 만들기 작업의 대상입니다.</param>
      <param name="args">동적 만들기 작업의 인수입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.CreateInstanceBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.DeleteIndexBinder">
      <summary>호출 사이트의 동적 인덱스 삭제 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>
        <see cref="T:System.Dynamic.DeleteIndexBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="callInfo">호출 사이트의 인수 서명입니다.</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 인덱스 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 삭제 작업의 대상입니다.</param>
      <param name="args">동적 인덱스 삭제 작업의 인수 배열입니다.</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.CallInfo">
      <summary>호출 사이트의 인수 서명을 가져옵니다.</summary>
      <returns>호출 사이트의 인수 서명입니다.</returns>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 인덱스 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 삭제 작업의 대상입니다.</param>
      <param name="indexes">동적 인덱스 삭제 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Dynamic.DeleteIndexBinder.FallbackDeleteIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 인덱스 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 삭제 작업의 대상입니다.</param>
      <param name="indexes">동적 인덱스 삭제 작업의 인수입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.DeleteIndexBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.DeleteMemberBinder">
      <summary>호출 사이트의 동적 멤버 삭제 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>
        <see cref="T:System.Dynamic.DeleteIndexBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">삭제할 멤버의 이름입니다.</param>
      <param name="ignoreCase">대/소문자를 무시하고 이름이 일치해야 하면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 멤버 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 삭제 작업의 대상입니다.</param>
      <param name="args">동적 멤버 삭제 작업의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 멤버 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 삭제 작업의 대상입니다.</param>
    </member>
    <member name="M:System.Dynamic.DeleteMemberBinder.FallbackDeleteMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 멤버 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 삭제 작업의 대상입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.IgnoreCase">
      <summary>문자열 비교 시 멤버 이름의 대/소문자를 무시할지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>문자열 비교 시 대/소문자를 무시해야 하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.Name">
      <summary>삭제할 멤버의 이름을 가져옵니다.</summary>
      <returns>삭제할 멤버의 이름입니다.</returns>
    </member>
    <member name="P:System.Dynamic.DeleteMemberBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObject">
      <summary>동적 바인딩에 참가하는 개체의 바인딩 논리와 동적 바인딩을 나타냅니다.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions)">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="expression">동적 바인딩을 수행하는 동안 이 <see cref="T:System.Dynamic.DynamicMetaObject" />를 나타내는 식입니다.</param>
      <param name="restrictions">바인딩이 유효한 바인딩 제한 집합입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.#ctor(System.Linq.Expressions.Expression,System.Dynamic.BindingRestrictions,System.Object)">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="expression">동적 바인딩을 수행하는 동안 이 <see cref="T:System.Dynamic.DynamicMetaObject" />를 나타내는 식입니다.</param>
      <param name="restrictions">바인딩이 유효한 바인딩 제한 집합입니다.</param>
      <param name="value">
        <see cref="T:System.Dynamic.DynamicMetaObject" />가 나타내는 런타임 값입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Dynamic.DynamicMetaObject)">
      <summary>동적 이항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.BinaryOperationBinder" />의 인스턴스입니다.</param>
      <param name="arg">이항 연산의 오른쪽을 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />의 인스턴스입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindConvert(System.Dynamic.ConvertBinder)">
      <summary>동적 변환 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.ConvertBinder" />의 인스턴스입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindCreateInstance(System.Dynamic.CreateInstanceBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 인스턴스 만들기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.CreateInstanceBinder" />의 인스턴스입니다.</param>
      <param name="args">인스턴스 만들기 작업에 대한 인수로서 <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스의 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 인덱스 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.DeleteIndexBinder" />의 인스턴스입니다.</param>
      <param name="indexes">인덱스 삭제 작업에 대한 <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스 - 인덱스의 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>동적 멤버 삭제 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.DeleteMemberBinder" />의 인스턴스입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetIndex(System.Dynamic.GetIndexBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 인덱스 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.GetIndexBinder" />의 인스턴스입니다.</param>
      <param name="indexes">인덱스 가져오기 작업에 대한 <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스 - 인덱스의 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindGetMember(System.Dynamic.GetMemberBinder)">
      <summary>동적 멤버 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.GetMemberBinder" />의 인스턴스입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvoke(System.Dynamic.InvokeBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.InvokeBinder" />의 인스턴스입니다.</param>
      <param name="args">호출 작업에 대한 인수로서 <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스의 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindInvokeMember(System.Dynamic.InvokeMemberBinder,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 멤버 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.InvokeMemberBinder" />의 인스턴스입니다.</param>
      <param name="args">멤버 호출 작업에 대한 인수로서 <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스의 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetIndex(System.Dynamic.SetIndexBinder,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>동적 인덱스 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.SetIndexBinder" />의 인스턴스입니다.</param>
      <param name="indexes">인덱스 설정 작업에 대한 <see cref="T:System.Dynamic.DynamicMetaObject" /> 인스턴스 - 인덱스의 배열입니다.</param>
      <param name="value">인덱스 설정 작업의 값을 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindSetMember(System.Dynamic.SetMemberBinder,System.Dynamic.DynamicMetaObject)">
      <summary>동적 멤버 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 작업의 세부 정보를 나타내는 <see cref="T:System.Dynamic.SetMemberBinder" />의 인스턴스입니다.</param>
      <param name="value">멤버 설정 작업의 값을 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.BindUnaryOperation(System.Dynamic.UnaryOperationBinder)">
      <summary>동적 단항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 새 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="binder">동적 연산의 세부 정보를 나타내는 <see cref="T:System.Dynamic.UnaryOperationBinder" />의 인스턴스입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.Create(System.Object,System.Linq.Expressions.Expression)">
      <summary>지정한 개체에 대한 메타 개체를 만듭니다.</summary>
      <returns>지정된 개체가 <see cref="T:System.Dynamic.IDynamicMetaObjectProvider" />를 구현하고 현재 AppDomain 외부의 원격 개체가 아니면 <see cref="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)" />에서 반환된 개체의 특정 메타 개체를 반환합니다.그렇지 않으면 아무 제한도 없는 일반 메타 개체가 새로 만들어져 반환됩니다.</returns>
      <param name="value">메타 개체를 가져올 개체입니다.</param>
      <param name="expression">동적 바인딩을 수행하는 동안 이 <see cref="T:System.Dynamic.DynamicMetaObject" />를 나타내는 식입니다.</param>
    </member>
    <member name="F:System.Dynamic.DynamicMetaObject.EmptyMetaObjects">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 형식의 빈 배열을 나타냅니다.이 필드는 읽기 전용입니다.</summary>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Expression">
      <summary>동적 바인딩을 수행하는 동안 <see cref="T:System.Dynamic.DynamicMetaObject" />를 나타내는 식입니다.</summary>
      <returns>동적 바인딩을 수행하는 동안 <see cref="T:System.Dynamic.DynamicMetaObject" />를 나타내는 식입니다.</returns>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObject.GetDynamicMemberNames">
      <summary>모든 동적 멤버 이름의 열거형을 반환합니다.</summary>
      <returns>동적 멤버 이름의 목록입니다.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.HasValue">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObject" />에 런타임 값이 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Dynamic.DynamicMetaObject" />에 런타임 값이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.LimitType">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObject" />의 제한 형식을 가져옵니다.</summary>
      <returns>런타임 값을 사용할 수 있으면 <see cref="P:System.Dynamic.DynamicMetaObject.RuntimeType" />이고, 그렇지 않으면 <see cref="P:System.Dynamic.DynamicMetaObject.Expression" />의 형식입니다.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Restrictions">
      <summary>바인딩이 유효한 바인딩 제한 집합입니다.</summary>
      <returns>바인딩 제한 집합입니다.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.RuntimeType">
      <summary>런타임 값의 <see cref="T:System.Type" /> 또는 <see cref="T:System.Dynamic.DynamicMetaObject" />에 연결된 값이 없는 경우 null을 가져옵니다.</summary>
      <returns>런타임 값의 <see cref="T:System.Type" /> 또는 null입니다.</returns>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObject.Value">
      <summary>이 <see cref="T:System.Dynamic.DynamicMetaObject" />가 나타내는 런타임 값입니다.</summary>
      <returns>이 <see cref="T:System.Dynamic.DynamicMetaObject" />가 나타내는 런타임 값입니다.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicMetaObjectBinder">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 바인딩 프로토콜에 참가하는 동적 호출 사이트 바인더입니다.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.#ctor">
      <summary>
        <see cref="T:System.Dynamic.DynamicMetaObjectBinder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>파생 클래스에서 재정의된 경우 동적 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 연산의 대상입니다.</param>
      <param name="args">동적 연산의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>인수 집합에서 동적 연산의 런타임 바인딩을 수행합니다.</summary>
      <returns>동적 연산의 인수에서 테스트를 수행하고 테스트에 성공하는 경우 동적 연산을 수행하는 식입니다.이후의 동적 연산에서 테스트에 실패하는 경우 새 인수 형식에 대한 새 <see cref="T:System.Linq.Expressions.Expression" />을 생성하기 위해 Bind가 다시 호출됩니다.</returns>
      <param name="args">동적 연산에 대한 인수 배열입니다.</param>
      <param name="parameters">바인딩 프로세스에서 호출 사이트의 매개 변수를 나타내는 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 인스턴스의 배열입니다.</param>
      <param name="returnLabel">동적 바인딩의 결과를 반환하는 데 사용되는 LabelTarget입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>나중에 모든 동적 연산 인수의 런타임 값이 계산될 때까지 작업의 바인딩을 지연합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 연산의 대상입니다.</param>
      <param name="args">동적 연산의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.Defer(System.Dynamic.DynamicMetaObject[])">
      <summary>나중에 모든 동적 연산 인수의 런타임 값이 계산될 때까지 작업의 바인딩을 지연합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="args">동적 연산의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicMetaObjectBinder.GetUpdateExpression(System.Type)">
      <summary>바인딩이 업데이트되도록 할 식을 가져옵니다.식의 바인딩이 더 이상 유효하지 않음을 나타냅니다.일반적으로 동적 개체의 "버전"이 변경될 때 사용됩니다.</summary>
      <returns>업데이트 식입니다.</returns>
      <param name="type">결과 식의 <see cref="P:System.Linq.Expressions.Expression.Type" /> 속성입니다. 모든 형식을 사용할 수 있습니다.</param>
    </member>
    <member name="P:System.Dynamic.DynamicMetaObjectBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.DynamicObject">
      <summary>런타임에 동적 동작을 지정하기 위한 기본 클래스를 제공합니다.이 클래스는 상속되어야 하며 직접 인스턴스화할 수 없습니다.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.#ctor">
      <summary>파생 형식이 <see cref="T:System.Dynamic.DynamicObject" /> 형식의 새 인스턴스를 초기화할 수 있도록 합니다.</summary>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetDynamicMemberNames">
      <summary>모든 동적 멤버 이름의 열거형을 반환합니다. </summary>
      <returns>동적 멤버 이름이 들어 있는 시퀀스입니다.</returns>
    </member>
    <member name="M:System.Dynamic.DynamicObject.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>동적 가상 메서드에 디스패치할 <see cref="T:System.Dynamic.DynamicMetaObject" />를 제공합니다.개체를 다른 <see cref="T:System.Dynamic.DynamicMetaObject" /> 내에 캡슐화하여 개별 작업에 대해 사용자 지정 동작을 제공할 수 있습니다.이 메서드는 언어 구현자에 대한 동적 언어 런타임 인프라를 지원하며 사용자 코드에서 직접 사용할 수 없습니다.</summary>
      <returns>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 형식의 개체입니다.</returns>
      <param name="parameter">동적 가상 메서드에 디스패치할 <see cref="T:System.Dynamic.DynamicMetaObject" />를 나타내는 식입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryBinaryOperation(System.Dynamic.BinaryOperationBinder,System.Object,System.Object@)">
      <summary>이항 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 더하기와 곱하기 같은 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">이항 연산에 대한 정보를 제공합니다.binder.Operation 속성은 <see cref="T:System.Linq.Expressions.ExpressionType" /> 개체를 반환합니다.예를 들어 sum = first + second 문의 경우, first 및 second는 DynamicObject 클래스에서 파생되고 binder.Operation은 ExpressionType.Add를 반환합니다.</param>
      <param name="arg">이항 연산의 오른쪽 피연산자입니다.예를 들어 sum = first + second 문의 경우, first 및 second는 DynamicObject 클래스에서 파생되고 <paramref name="arg" />는 second와 동일합니다.</param>
      <param name="result">이항 연산의 결과입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryConvert(System.Dynamic.ConvertBinder,System.Object@)">
      <summary>형식 변환 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 개체를 한 형식에서 다른 형식으로 변환하는 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">변환 연산에 대한 정보를 제공합니다.binder.Type 속성은 개체가 변환되어야 하는 형식을 제공합니다.예를 들어 C#의 (String)sampleObject 문(Visual Basic의 CType(sampleObject, Type))의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스의 인스턴스이고 binder.Type은 <see cref="T:System.String" /> 형식을 반환합니다.binder.Explicit 속성은 발생하는 변환의 종류에 대한 정보를 제공합니다.명시적 변환의 경우 true를 반환하고, 암시적 변환의 경우 false를 반환합니다.</param>
      <param name="result">형식 변환 연산의 결과입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryCreateInstance(System.Dynamic.CreateInstanceBinder,System.Object[],System.Object@)">
      <summary>동적 개체의 새 인스턴스를 초기화하는 연산에 대한 구현을 제공합니다.이 메서드는 C# 또는 Visual Basic에서 사용할 수 없습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">초기화 연산에 대한 정보를 제공합니다.</param>
      <param name="args">초기화하는 동안 개체에 전달되는 인수입니다.예를 들어 new SampleType(100) 연산의 경우, SampleType은 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 형식이고 <paramref name="args[0]" />은 100과 같습니다.</param>
      <param name="result">초기화의 결과입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteIndex(System.Dynamic.DeleteIndexBinder,System.Object[])">
      <summary>인덱스별로 개체를 삭제하는 연산에 대한 구현을 제공합니다.이 메서드는 C# 또는 Visual Basic에서 사용할 수 없습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">삭제에 대한 정보를 제공합니다.</param>
      <param name="indexes">삭제할 인덱스입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryDeleteMember(System.Dynamic.DeleteMemberBinder)">
      <summary>개체 멤버를 삭제하는 연산에 대한 구현을 제공합니다.이 메서드는 C# 또는 Visual Basic에서 사용할 수 없습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">삭제에 대한 정보를 제공합니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetIndex(System.Dynamic.GetIndexBinder,System.Object[],System.Object@)">
      <summary>인덱스별로 값을 가져오는 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 인덱싱 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 런타임 예외가 throw됩니다.</returns>
      <param name="binder">연산에 대한 정보를 제공합니다. </param>
      <param name="indexes">연산에 사용되는 인덱스입니다.예를 들어 C#의 sampleObject[3] 연산(Visual Basic의 sampleObject(3))의 경우, sampleObject는 DynamicObject 클래스에서 파생되고 <paramref name="indexes[0]" />은 3과 같습니다.</param>
      <param name="result">인덱스 연산의 결과입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryGetMember(System.Dynamic.GetMemberBinder,System.Object@)">
      <summary>멤버 값을 가져오는 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 속성 값 가져오기와 같은 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 런타임 예외가 throw됩니다.</returns>
      <param name="binder">동적 연산을 호출한 개체에 대한 정보를 제공합니다.binder.Name 속성은 동적 연산이 수행된 멤버의 이름을 제공합니다.예를 들어 Console.WriteLine(sampleObject.SampleProperty) 문의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스의 인스턴스이고 binder.Name은 "SampleProperty"를 반환합니다.binder.IgnoreCase 속성은 멤버 이름이 대/소문자를 구분하는지 여부를 지정합니다.</param>
      <param name="result">가져오기 연산의 결과입니다.예를 들어 속성에 대한 메서드가 호출되면 <paramref name="result" />에 속성 값을 할당할 수 있습니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvoke(System.Dynamic.InvokeBinder,System.Object[],System.Object@)">
      <summary>개체를 호출하는 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 개체 또는 대리자 호출과 같은 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">호출 연산에 대한 정보를 제공합니다.</param>
      <param name="args">호출 연산을 수행하는 동안 개체에 전달되는 인수입니다.예를 들어 sampleObject(100) 연산의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생되고 <paramref name="args[0]" />은 100과 같습니다.</param>
      <param name="result">개체 호출의 결과입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryInvokeMember(System.Dynamic.InvokeMemberBinder,System.Object[],System.Object@)">
      <summary>멤버를 호출하는 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 메서드 호출과 같은 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">동적 연산에 대한 정보를 제공합니다.binder.Name 속성은 동적 연산이 수행된 멤버의 이름을 제공합니다.예를 들어 sampleObject.SampleMethod(100) 문의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스의 인스턴스이고 binder.Name은 "SampleMethod"를 반환합니다.binder.IgnoreCase 속성은 멤버 이름이 대/소문자를 구분하는지 여부를 지정합니다.</param>
      <param name="args">호출 연산을 수행하는 동안 개체 멤버에 전달되는 인수입니다.예를 들어 sampleObject.SampleMethod(100) 문의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생되고 <paramref name="args[0]" />은 100과 같습니다.</param>
      <param name="result">멤버 호출의 결과입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetIndex(System.Dynamic.SetIndexBinder,System.Object[],System.Object)">
      <summary>인덱스별로 값을 설정하는 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 지정된 인덱스별로 개체에 액세스하는 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">연산에 대한 정보를 제공합니다. </param>
      <param name="indexes">연산에 사용되는 인덱스입니다.예를 들어 C#의 sampleObject[3] = 10 연산(Visual Basic의 sampleObject(3) = 10)의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생되고 <paramref name="indexes[0]" />은 3과 같습니다.</param>
      <param name="value">지정된 인덱스를 가진 개체로 설정할 값입니다.예를 들어 C#의 sampleObject[3] = 10 연산(Visual Basic의 sampleObject(3) = 10)의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생되고 <paramref name="value" />는 10과 같습니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TrySetMember(System.Dynamic.SetMemberBinder,System.Object)">
      <summary>멤버 값을 설정하는 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 속성 값 설정과 같은 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">동적 연산을 호출한 개체에 대한 정보를 제공합니다.binder.Name 속성은 값이 할당될 멤버의 이름을 제공합니다.예를 들어 sampleObject.SampleProperty = "Test"문의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스의 인스턴스이고 binder.Name은 "SampleProperty"를 반환합니다.binder.IgnoreCase 속성은 멤버 이름이 대/소문자를 구분하는지 여부를 지정합니다.</param>
      <param name="value">멤버에 설정할 값입니다.예를 들어 sampleObject.SampleProperty = "Test"의 경우, sampleObject는 <see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스의 인스턴스이고 <paramref name="value" />는 "Test"입니다.</param>
    </member>
    <member name="M:System.Dynamic.DynamicObject.TryUnaryOperation(System.Dynamic.UnaryOperationBinder,System.Object@)">
      <summary>단항 연산에 대한 구현을 제공합니다.<see cref="T:System.Dynamic.DynamicObject" /> 클래스에서 파생된 클래스로 이 메서드를 재정의하여 부정, 증가 또는 감소와 같은 연산의 동적 동작을 지정할 수 있습니다.</summary>
      <returns>연산이 성공적으로 수행되면 true이고, 그렇지 않으면 false입니다.이 메서드가 false를 반환하는 경우 언어의 런타임 바인더에 따라 동작이 결정됩니다. 대부분의 경우 언어별 런타임 예외가 throw됩니다.</returns>
      <param name="binder">단항 연산에 대한 정보를 제공합니다.binder.Operation 속성은 <see cref="T:System.Linq.Expressions.ExpressionType" /> 개체를 반환합니다.예를 들어 negativeNumber = -number 문의 경우, number는 DynamicObject에서 파생되고 binder.Operation은 "Negate"를 반환합니다.</param>
      <param name="result">단항 연산의 결과입니다.</param>
    </member>
    <member name="T:System.Dynamic.ExpandoObject">
      <summary>런타임에 동적으로 추가 및 제거할 수 있는 멤버가 있는 개체를 나타냅니다.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.#ctor">
      <summary>멤버가 없는 새 ExpandoObject를 초기화합니다.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>지정된 키가 있는 <see cref="T:System.Collections.Generic.ICollection`1" />에 지정된 값을 추가합니다.</summary>
      <param name="item">컬렉션에 추가할 키와 값을 나타내는 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Clear">
      <summary>컬렉션에서 모든 항목을 제거합니다.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 특정 키와 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>컬렉션에 특정 키와 값이 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{System.String,System.Object}[],System.Int32)">
      <summary>지정한 배열 인덱스부터 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소를 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 형식의 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 복사한 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 요소의 대상인 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 형식의 1차원 배열입니다.배열에서 0부터 시작하는 인덱스를 사용해야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Count">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />의 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />의 요소 수입니다.</returns>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{System.String,System.Object})">
      <summary>컬렉션에서 키와 값을 제거합니다.</summary>
      <returns>키와 값을 성공적으로 찾아서 제거한 경우 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.ICollection`1" />에서 키와 값을 찾을 수 없으면 이 메서드는 false를 반환합니다.</returns>
      <param name="item">컬렉션에서 제거할 키와 값을 나타내는 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Add(System.String,System.Object)">
      <summary>지정한 키와 값을 사전에 추가합니다.</summary>
      <param name="key">키로 사용할 개체입니다.</param>
      <param name="value">값으로 사용할 개체입니다.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#ContainsKey(System.String)">
      <summary>사전에 지정한 키가 들어 있는지 여부를 확인합니다.</summary>
      <returns>사전에 지정한 키를 가진 요소가 포함되어 있는 경우 true이고 그렇지 않은 경우 false입니다.</returns>
      <param name="key">사전에서 찾을 키입니다.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Item(System.String)">
      <summary>지정된 키를 갖는 요소를 가져오거나 설정합니다.</summary>
      <returns>지정된 키를 갖는 요소입니다.</returns>
      <param name="key">가져오거나 설정할 요소의 키입니다.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />를 구현하는 개체의 키가 들어 있는 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Remove(System.String)">
      <summary>
        <see cref="T:System.Collections.IDictionary" />에서 지정된 키를 가진 요소를 제거합니다.</summary>
      <returns>요소가 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="key" />가 원래 <see cref="T:System.Collections.Generic.IDictionary`2" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="key">제거할 요소의 키입니다.</param>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#TryGetValue(System.String,System.Object@)">
      <summary>지정된 키와 연결된 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />를 구현하는 개체에 지정된 키를 가진 요소가 들어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="key">가져올 값의 키입니다.</param>
      <param name="value">이 메서드가 반환될 때 지정된 키가 있으면 해당 키와 연결된 값을 포함하고, 그렇지 않으면 <paramref name="value" /> 매개 변수의 형식에 대한 기본값을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
    </member>
    <member name="P:System.Dynamic.ExpandoObject.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 값이 들어 있는 <see cref="T:System.Collections.Generic.ICollection`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />를 구현하는 개체의 값이 들어 있는 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="E:System.Dynamic.ExpandoObject.System#ComponentModel#INotifyPropertyChanged#PropertyChanged">
      <summary>속성 값이 변경될 때 발생합니다.</summary>
    </member>
    <member name="M:System.Dynamic.ExpandoObject.System#Dynamic#IDynamicMetaObjectProvider#GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>제공된 MetaObject가 동적 가상 메서드에 디스패치됩니다.개체를 다른 MetaObject에 캡슐화하여 개별 작업에 대해 사용자 지정 동작을 제공할 수 있습니다.</summary>
      <returns>
        <see cref="T:System.Dynamic.DynamicMetaObject" /> 형식의 개체입니다.</returns>
      <param name="parameter">동적 가상 메서드에 디스패치할 MetaObject를 나타내는 식입니다.</param>
    </member>
    <member name="T:System.Dynamic.GetIndexBinder">
      <summary>호출 사이트의 동적 인덱스 가져오기 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>
        <see cref="T:System.Dynamic.GetIndexBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="callInfo">호출 사이트의 인수 서명입니다.</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 인덱스 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 가져오기 작업의 대상입니다.</param>
      <param name="args">동적 인덱스 가져오기 작업의 인수 배열입니다.</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.CallInfo">
      <summary>호출 사이트의 인수 서명을 가져옵니다.</summary>
      <returns>호출 사이트의 인수 서명입니다.</returns>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 인덱스 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 가져오기 작업의 대상입니다.</param>
      <param name="indexes">동적 인덱스 가져오기 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Dynamic.GetIndexBinder.FallbackGetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 인덱스 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 가져오기 작업의 대상입니다.</param>
      <param name="indexes">동적 인덱스 가져오기 작업의 인수입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.GetIndexBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.GetMemberBinder">
      <summary>호출 사이트의 동적 멤버 가져오기 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>
        <see cref="T:System.Dynamic.GetMemberBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">가져올 멤버의 이름입니다.</param>
      <param name="ignoreCase">대/소문자를 무시하고 이름이 일치해야 하면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 멤버 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 가져오기 작업의 대상입니다.</param>
      <param name="args">동적 멤버 가져오기 작업의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 멤버 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 가져오기 작업의 대상입니다.</param>
    </member>
    <member name="M:System.Dynamic.GetMemberBinder.FallbackGetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 멤버 가져오기 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 가져오기 작업의 대상입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.IgnoreCase">
      <summary>문자열 비교 시 멤버 이름의 대/소문자를 무시할지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>대/소문자가 무시되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.Name">
      <summary>가져올 멤버의 이름을 가져옵니다.</summary>
      <returns>가져올 멤버의 이름입니다.</returns>
    </member>
    <member name="P:System.Dynamic.GetMemberBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.IDynamicMetaObjectProvider">
      <summary>런타임에 작업이 바인딩될 수 있는 동적 개체를 나타냅니다.</summary>
    </member>
    <member name="M:System.Dynamic.IDynamicMetaObjectProvider.GetMetaObject(System.Linq.Expressions.Expression)">
      <summary>이 개체에 대해 바인딩 작업을 수행하는 <see cref="T:System.Dynamic.DynamicMetaObject" />를 반환합니다.</summary>
      <returns>이 개체를 바인딩할 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="parameter">런타임 값의 식 트리 표현입니다.</param>
    </member>
    <member name="T:System.Dynamic.IInvokeOnGetBinder">
      <summary>get 작업을 수행할 때 get 멤버가 속성을 호출할지 여부를 알려 주는 동적 get 멤버 작업에 대한 정보를 나타냅니다.</summary>
    </member>
    <member name="P:System.Dynamic.IInvokeOnGetBinder.InvokeOnGet">
      <summary>get 작업을 수행할 때 이 get 멤버 작업이 속성을 호출할지 여부를 알려 주는 값을 가져옵니다.이 인터페이스가 없으면 기본값은 true입니다.</summary>
      <returns>get 작업을 수행할 때 이 get 멤버 작업이 속성을 호출해야 하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="T:System.Dynamic.InvokeBinder">
      <summary>호출 사이트의 동적 호출 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>
        <see cref="T:System.Dynamic.InvokeBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="callInfo">호출 사이트의 인수 서명입니다.</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 호출 작업의 대상입니다.</param>
      <param name="args">동적 호출 작업의 인수 배열입니다.</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.CallInfo">
      <summary>호출 사이트의 인수 서명을 가져옵니다.</summary>
      <returns>호출 사이트의 인수 서명입니다.</returns>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 호출 작업의 대상입니다.</param>
      <param name="args">동적 호출 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Dynamic.InvokeBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 호출 작업의 대상입니다.</param>
      <param name="args">동적 호출 작업의 인수입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.InvokeBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.InvokeMemberBinder">
      <summary>호출 사이트의 동적 멤버 호출 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.#ctor(System.String,System.Boolean,System.Dynamic.CallInfo)">
      <summary>
        <see cref="T:System.Dynamic.InvokeMemberBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">호출할 멤버의 이름입니다.</param>
      <param name="ignoreCase">대/소문자를 무시하고 이름이 일치해야 하면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="callInfo">호출 사이트의 인수 서명입니다.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 멤버 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 호출 작업의 대상입니다.</param>
      <param name="args">동적 멤버 호출 작업의 인수 배열입니다.</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.CallInfo">
      <summary>호출 사이트의 인수 서명을 가져옵니다.</summary>
      <returns>호출 사이트의 인수 서명입니다.</returns>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvoke(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 호출 작업의 대상입니다.</param>
      <param name="args">동적 호출 작업의 인수입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 멤버 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 호출 작업의 대상입니다.</param>
      <param name="args">동적 멤버 호출 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Dynamic.InvokeMemberBinder.FallbackInvokeMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 멤버 호출 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 호출 작업의 대상입니다.</param>
      <param name="args">동적 멤버 호출 작업의 인수입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.IgnoreCase">
      <summary>문자열 비교 시 멤버 이름의 대/소문자를 무시할지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>대/소문자가 무시되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.Name">
      <summary>호출할 멤버의 이름을 가져옵니다.</summary>
      <returns>호출할 멤버의 이름입니다.</returns>
    </member>
    <member name="P:System.Dynamic.InvokeMemberBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.SetIndexBinder">
      <summary>호출 사이트의 동적 인덱스 설정 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.#ctor(System.Dynamic.CallInfo)">
      <summary>
        <see cref="T:System.Dynamic.SetIndexBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="callInfo">호출 사이트의 인수 서명입니다.</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 인덱스 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 설정 작업의 대상입니다.</param>
      <param name="args">동적 인덱스 설정 작업의 인수 배열입니다.</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.CallInfo">
      <summary>호출 사이트의 인수 서명을 가져옵니다.</summary>
      <returns>호출 사이트의 인수 서명입니다.</returns>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 인덱스 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 설정 작업의 대상입니다.</param>
      <param name="indexes">동적 인덱스 설정 작업의 인수입니다.</param>
      <param name="value">컬렉션에 설정할 값입니다.</param>
    </member>
    <member name="M:System.Dynamic.SetIndexBinder.FallbackSetIndex(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[],System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>파생 클래스에서 재정의된 경우 대상 동적 개체가 바인딩할 수 없으면 동적 인덱스 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 인덱스 설정 작업의 대상입니다.</param>
      <param name="indexes">동적 인덱스 설정 작업의 인수입니다.</param>
      <param name="value">컬렉션에 설정할 값입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.SetIndexBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.SetMemberBinder">
      <summary>호출 사이트의 동적 멤버 설정 작업을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.#ctor(System.String,System.Boolean)">
      <summary>
        <see cref="T:System.Dynamic.SetMemberBinder" />의 새 인스턴스를 초기화합니다.</summary>
      <param name="name">가져올 멤버의 이름입니다.</param>
      <param name="ignoreCase">대/소문자를 무시하고 이름이 일치해야 하면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 멤버 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 설정 작업의 대상입니다.</param>
      <param name="args">동적 멤버 설정 작업의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 멤버 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 설정 작업의 대상입니다.</param>
      <param name="value">멤버에 설정할 값입니다.</param>
    </member>
    <member name="M:System.Dynamic.SetMemberBinder.FallbackSetMember(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 멤버 설정 작업의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 멤버 설정 작업의 대상입니다.</param>
      <param name="value">멤버에 설정할 값입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 사용할 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.IgnoreCase">
      <summary>문자열 비교 시 멤버 이름의 대/소문자를 무시할지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>대/소문자가 무시되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.Name">
      <summary>가져올 멤버의 이름을 가져옵니다.</summary>
      <returns>가져올 멤버의 이름입니다.</returns>
    </member>
    <member name="P:System.Dynamic.SetMemberBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Dynamic.UnaryOperationBinder">
      <summary>호출 사이트의 동적 단항 연산을 나타내며 바인딩 의미 체계와 작업에 대한 세부 정보를 제공합니다.</summary>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.#ctor(System.Linq.Expressions.ExpressionType)">
      <summary>
        <see cref="T:System.Dynamic.BinaryOperationBinder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="operation">단항 연산 종류입니다.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.Bind(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject[])">
      <summary>동적 단항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 연산의 대상입니다.</param>
      <param name="args">동적 연산의 인수 배열입니다.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 단항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 단항 연산의 대상입니다.</param>
    </member>
    <member name="M:System.Dynamic.UnaryOperationBinder.FallbackUnaryOperation(System.Dynamic.DynamicMetaObject,System.Dynamic.DynamicMetaObject)">
      <summary>대상 동적 개체가 바인딩할 수 없는 경우 동적 단항 연산의 바인딩을 수행합니다.</summary>
      <returns>바인딩 결과를 나타내는 <see cref="T:System.Dynamic.DynamicMetaObject" />입니다.</returns>
      <param name="target">동적 단항 연산의 대상입니다.</param>
      <param name="errorSuggestion">바인딩이 실패한 경우 바인딩 결과 또는 null입니다.</param>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.Operation">
      <summary>단항 연산 종류입니다.</summary>
      <returns>단항 연산 종류를 나타내는 <see cref="T:System.Linq.Expressions.ExpressionType" />의 개체입니다.</returns>
    </member>
    <member name="P:System.Dynamic.UnaryOperationBinder.ReturnType">
      <summary>작업의 결과 형식입니다.</summary>
      <returns>작업의 결과 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpression">
      <summary>동적 작업을 나타냅니다.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Accept(System.Linq.Expressions.ExpressionVisitor)">
      <summary>이 노드 형식에 대한 특정 Visit 메서드로 디스패치합니다.예를 들어 <see cref="T:System.Linq.Expressions.MethodCallExpression" />은 <see cref="M:System.Linq.Expressions.ExpressionVisitor.VisitMethodCall(System.Linq.Expressions.MethodCallExpression)" />을 호출합니다.</summary>
      <returns>이 노드를 열어 본 결과입니다.</returns>
      <param name="visitor">이 노드를 열어 볼 방문자입니다.</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Arguments">
      <summary>동적 작업의 인수를 가져옵니다.</summary>
      <returns>동적 작업의 인수가 들어 있는 읽기 전용 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Binder">
      <summary>동적 사이트의 런타임 동작을 결정하는 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />를 가져옵니다.</summary>
      <returns>동적 사이트의 런타임 동작을 결정하는 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />입니다.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.DelegateType">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용하는 대리자의 형식을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용하는 대리자의 형식을 나타내는 <see cref="T:System.Type" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="returnType">동적 식의 결과 형식입니다.</param>
      <param name="arguments">동적 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="returnType">동적 식의 결과 형식입니다.</param>
      <param name="arg0">동적 작업의 첫 번째 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="returnType">동적 식의 결과 형식입니다.</param>
      <param name="arg0">동적 작업의 첫 번째 인수입니다.</param>
      <param name="arg1">동적 작업의 두 번째 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="returnType">동적 식의 결과 형식입니다.</param>
      <param name="arg0">동적 작업의 첫 번째 인수입니다.</param>
      <param name="arg1">동적 작업의 두 번째 인수입니다.</param>
      <param name="arg2">동적 작업의 세 번째 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="returnType">동적 식의 결과 형식입니다.</param>
      <param name="arg0">동적 작업의 첫 번째 인수입니다.</param>
      <param name="arg1">동적 작업의 두 번째 인수입니다.</param>
      <param name="arg2">동적 작업의 세 번째 인수입니다.</param>
      <param name="arg3">동적 작업의 네 번째 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Dynamic(System.Runtime.CompilerServices.CallSiteBinder,System.Type,System.Linq.Expressions.Expression[])">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="returnType">동적 식의 결과 형식입니다.</param>
      <param name="arguments">동적 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용되는 대리자의 형식입니다.</param>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="arguments">동적 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 및 하나의 인수에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용되는 대리자의 형식입니다.</param>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="arg0">동적 작업의 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 및 두 개의 인수에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용되는 대리자의 형식입니다.</param>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="arg0">동적 작업의 첫 번째 인수입니다.</param>
      <param name="arg1">동적 작업의 두 번째 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 및 세 개의 인수에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용되는 대리자의 형식입니다.</param>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="arg0">동적 작업의 첫 번째 인수입니다.</param>
      <param name="arg1">동적 작업의 두 번째 인수입니다.</param>
      <param name="arg2">동적 작업의 세 번째 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression,System.Linq.Expressions.Expression)">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 및 네 개의 인수에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용되는 대리자의 형식입니다.</param>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="arg0">동적 작업의 첫 번째 인수입니다.</param>
      <param name="arg1">동적 작업의 두 번째 인수입니다.</param>
      <param name="arg2">동적 작업의 세 번째 인수입니다.</param>
      <param name="arg3">동적 작업의 네 번째 인수입니다.</param>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.MakeDynamic(System.Type,System.Runtime.CompilerServices.CallSiteBinder,System.Linq.Expressions.Expression[])">
      <summary>제공된 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" />에 의해 바인딩된 동적 작업을 나타내는 <see cref="T:System.Linq.Expressions.DynamicExpression" />을 만듭니다.</summary>
      <returns>
        <see cref="P:System.Linq.Expressions.DynamicExpression.NodeType" />이 <see cref="F:System.Linq.Expressions.ExpressionType.Dynamic" />이고 <see cref="P:System.Linq.Expressions.DynamicExpression.DelegateType" />, <see cref="P:System.Linq.Expressions.DynamicExpression.Binder" /> 및 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" />가 지정된 값으로 설정된 <see cref="T:System.Linq.Expressions.DynamicExpression" />입니다.</returns>
      <param name="delegateType">
        <see cref="T:System.Runtime.CompilerServices.CallSite" />에서 사용되는 대리자의 형식입니다.</param>
      <param name="binder">동적 작업의 런타임 바인더입니다.</param>
      <param name="arguments">동적 작업의 인수입니다.</param>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.NodeType">
      <summary>이 식의 노드 형식을 반환합니다.확장 노드는 이 메서드를 재정의할 때 <see cref="F:System.Linq.Expressions.ExpressionType.Extension" />을 반환해야 합니다.</summary>
      <returns>식의 <see cref="T:System.Linq.Expressions.ExpressionType" />입니다.</returns>
    </member>
    <member name="P:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#ArgumentCount"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IArgumentProvider#GetArgument(System.Int32)"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#CreateCallSite"></member>
    <member name="M:System.Linq.Expressions.DynamicExpression.System#Linq#Expressions#IDynamicExpression#Rewrite(System.Linq.Expressions.Expression[])"></member>
    <member name="P:System.Linq.Expressions.DynamicExpression.Type">
      <summary>이 <see cref="T:System.Linq.Expressions.Expression" />이 나타내는 식의 정적 형식을 가져옵니다.</summary>
      <returns>식의 정적 형식을 나타내는 <see cref="P:System.Linq.Expressions.DynamicExpression.Type" />입니다.</returns>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpression.Update(System.Collections.Generic.IEnumerable{System.Linq.Expressions.Expression})">
      <summary>매개 변수 arguments로 보낸 값을 DynamicExpression의 현재 인스턴스의 Arguments 속성과 비교합니다.매개 변수 및 속성의 값이 같으면 현재 인스턴스가 반환됩니다.인스턴스가 일치하지 않으면 Arguments  속성이 매개 변수 arguments의 값으로 설정되는 경우를 제외하고 현재 인스턴스와 동일한 새 DynamicExpression 인스턴스가 반환됩니다.</summary>
      <returns>변경된 자식이 없으면 이 식이고, 그렇지 않으면 업데이트된 자식을 사용한 식입니다.</returns>
      <param name="arguments">결과의 <see cref="P:System.Linq.Expressions.DynamicExpression.Arguments" /> 속성입니다.</param>
    </member>
    <member name="T:System.Linq.Expressions.DynamicExpressionVisitor">
      <summary>동적 식 트리에 대한 방문자 또는 재작성기를 나타냅니다.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.#ctor">
      <summary>
        <see cref="T:System.Linq.Expressions.DynamicExpressionVisitor" />의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Linq.Expressions.DynamicExpressionVisitor.VisitDynamic(System.Linq.Expressions.DynamicExpression)">
      <summary>
        <see cref="T:System.Linq.Expressions.DynamicExpression" />의 자식 항목을 열어 봅니다.</summary>
      <returns>식 또는 하위 식이 수정되었으면 수정된 식인 <see cref="T:System.Linq.Expressions.Expression" />을 반환하고, 그렇지 않으면 원래 식을 반환합니다.</returns>
      <param name="node">열어 볼 식입니다.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite">
      <summary>동적 호출 사이트의 기본 클래스입니다.이 형식은 동적 사이트 대상에 대한 매개 변수 형식으로 사용됩니다.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite.Binder">
      <summary>동적 사이트에서 동적 연산을 바인딩하는 클래스입니다.</summary>
      <returns>동적 연산을 바인딩하는 <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite.Create(System.Type,System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>지정된 대리자 형식 및 바인더를 사용하여 호출 사이트를 만듭니다.</summary>
      <returns>새 호출 사이트입니다.</returns>
      <param name="delegateType">호출 사이트의 대리자 형식입니다.</param>
      <param name="binder">호출 사이트의 바인더입니다.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSite`1">
      <summary>동적 사이트 형식입니다.</summary>
      <typeparam name="T">대리자 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSite`1.Create(System.Runtime.CompilerServices.CallSiteBinder)">
      <summary>이 호출 사이트에서 동적 연산의 런타임 바인딩을 수행하는 바인더를 사용하여 초기화되는 동적 호출 사이트의 인스턴스를 만듭니다.</summary>
      <returns>동적 호출 사이트의 새 인스턴스입니다.</returns>
      <param name="binder">이 호출 사이트에서 동적 연산의 런타임 바인딩을 수행하는 바인더입니다.</param>
    </member>
    <member name="F:System.Runtime.CompilerServices.CallSite`1.Target">
      <summary>수준 0 캐시 - 사이트 기록을 기준으로 특수화된 대리자입니다.</summary>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSite`1.Update">
      <summary>업데이트 대리자입니다.동적 사이트에서 캐시가 누락되는 경우 호출됩니다.</summary>
      <returns>업데이트 대리자입니다.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteBinder">
      <summary>동적 호출 사이트에서 동적 연산의 런타임 바인딩을 수행하는 클래스입니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.#ctor">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.CallSiteBinder" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.Bind(System.Object[],System.Collections.ObjectModel.ReadOnlyCollection{System.Linq.Expressions.ParameterExpression},System.Linq.Expressions.LabelTarget)">
      <summary>인수 집합에서 동적 연산의 런타임 바인딩을 수행합니다.</summary>
      <returns>동적 연산의 인수에서 테스트를 수행하고 테스트에 성공하는 경우 동적 연산을 수행하는 식입니다.이후의 동적 연산에서 테스트에 실패하는 경우 새 인수 형식에 대한 새 <see cref="T:System.Linq.Expressions.Expression" />을 생성하기 위해 Bind가 다시 호출됩니다.</returns>
      <param name="args">동적 연산에 대한 인수 배열입니다.</param>
      <param name="parameters">바인딩 프로세스에서 호출 사이트의 매개 변수를 나타내는 <see cref="T:System.Linq.Expressions.ParameterExpression" /> 인스턴스의 배열입니다.</param>
      <param name="returnLabel">동적 바인딩의 결과를 반환하는 데 사용되는 LabelTarget입니다.</param>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.BindDelegate``1(System.Runtime.CompilerServices.CallSite{``0},System.Object[])">
      <summary>하위 수준 런타임 바인딩 지원을 제공합니다.클래스를 사용하여 이를 재정의하고 규칙 구현을 위한 대리자를 직접 제공할 수 있습니다.이를 통해 디스크에 규칙을 저장하거나 런타임에서 특수화된 규칙을 사용할 수 있게 하거나 다른 캐싱 정책을 제공할 수 있습니다.</summary>
      <returns>CallSite 대상을 대체하는 새 대리자입니다.</returns>
      <param name="site">바인드가 수행되는 CallSite입니다.</param>
      <param name="args">바인더의 인수입니다.</param>
      <typeparam name="T">CallSite의 대상 형식입니다.</typeparam>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteBinder.CacheTarget``1(``0)">
      <summary>알려진 대상의 캐시에 대상을 추가합니다.캐시된 대상은 새 규칙을 생성하기 위해 BindDelegate를 호출하기 전에 검색됩니다.</summary>
      <param name="target">캐시에 추가될 대상 대리자입니다.</param>
      <typeparam name="T">추가되는 대상의 형식입니다.</typeparam>
    </member>
    <member name="P:System.Runtime.CompilerServices.CallSiteBinder.UpdateLabel">
      <summary>바인딩이 업데이트되도록 하는 데 사용할 수 있는 레이블을 가져옵니다.식의 바인딩이 더 이상 유효하지 않음을 나타냅니다.일반적으로 동적 개체의 "버전"이 변경될 때 사용됩니다.</summary>
      <returns>바인딩 업데이트를 트리거하는 데 사용할 수 있는 레이블을 나타내는 <see cref="T:System.Linq.Expressions.LabelTarget" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Runtime.CompilerServices.CallSiteHelpers">
      <summary>DLR CallSite에 대한 도우미 메서드가 들어 있는 클래스입니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.CallSiteHelpers.IsInternalFrame(System.Reflection.MethodBase)">
      <summary>
        <see cref="T:System.Reflection.MethodBase" />를 DLR에서 내부적으로 사용하고 언어 코드의 스택에 표시하면 안 되는지 확인합니다.</summary>
      <returns>입력 <see cref="T:System.Reflection.MethodBase" />를 DLR에서 내부적으로 사용하고 언어 코드의 스택에 표시하면 안 되면 true이고,그렇지 않으면 false입니다.</returns>
      <param name="mb">입력 <see cref="T:System.Reflection.MethodBase" />입니다.</param>
    </member>
    <member name="T:System.Runtime.CompilerServices.DynamicAttribute">
      <summary>멤버에서 <see cref="T:System.Object" />를 사용하면 동적으로 디스패치되는 형식으로 처리된다는 것을 나타냅니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Runtime.CompilerServices.DynamicAttribute.#ctor(System.Boolean[])">
      <summary>
        <see cref="T:System.Runtime.CompilerServices.DynamicAttribute" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="transformFlags">형식의 접두사 순회 생성 시 동적으로 디스패치되는 형식으로 처리될 <see cref="T:System.Object" /> 를 지정합니다.</param>
    </member>
    <member name="P:System.Runtime.CompilerServices.DynamicAttribute.TransformFlags">
      <summary>형식의 접두사 순회 생성 시 동적으로 디스패치되는 형식으로 처리될 <see cref="T:System.Object" /> 를 지정합니다.</summary>
      <returns>동적으로 디스패치되는 형식으로 처리될 <see cref="T:System.Object" />의 목록입니다.</returns>
    </member>
  </members>
</doc>