﻿<%@ Control CodeBehind="ListView.ascx.cs" Language="c#" AutoEventWireup="false" Inherits="SplendidCRM.UtilityBillPay.Config.ListView" %>

<div id="divListView" class="Requisition">

    <div class="Title"><span>Config Search</span></div>

    <div class="bg1">
        <table class="Table1" id="searchDiv">
            <tr>
                <td width="15%" class="left">
                    <label>Category :</label>
                </td>
                <td width="18%">
                    <input type="text" class="CommonWidth" col="CATEGORY">
                </td>

                <td width="15%" class="left">
                    <label>Name :</label>
                </td>
                <td width="18%">
                    <input type="text" class="CommonWidth" col="NAME">
                </td>

                <td width="15%" class="left">
                    <label>Value :</label>
                </td>
                <td width="18%">
                    <input type="text" class="CommonWidth" col="VALUE">
                </td>
            </tr>

        </table>
    </div>

    <br />

    <input type="button" class="button" value="Search" onclick="searchGrid()" />&nbsp; &nbsp;<input type="button" class="button" value="Clear" onclick="clearGrid()" />

    <div class="ListTitle">
        <div style="float: left; width: 20%;">
            <span>Config List</span>
        </div>
        <div style="float: left; text-align: right; width: 80%; margin-bottom: 5px;">
            <select id="sltExport" style="display: none">
                <option value="All">Entire List</option>
                <option value="Selected">Selected Records</option>
            </select>
            <input type="button" value="Export" class="button" onclick="ExportConfig()" style="display: none" />

            <input id="btnAdd" type="button" value="Add " class="button" onclick="openDlg()" />
            <input id="btnAddClass" type="button" value="Add Utility Type Class" style="display:none" class="button" onclick="openUtilityTypeClassDlg()" />
        </div>
    </div>

    <div id="tableDiv">
        <table id="ConfigList" class="tableNoWrap"></table>
    </div>

    <div id="dlg-container" style="display: none;">
        <table id="dlgTable" class="Table1">
            <tr>
                <td width="15%" class="left">
                    <label class="control-label">Category :<font class="red"> *</font></label>
                </td>
                <td width="18%">
                    <input type="text" id="CATEGORY" name="CATEGORY" class="CommonWidth form-control dlg-input" col="CATEGORY">
                </td>
            </tr>
            <tr>
                <td width="15%" class="left">
                    <label class="control-label">Name :<font class="red"> *</font></label>
                </td>
                <td width="18%">
                    <input type="text" id="NAME" name="NAME" class="CommonWidth form-control dlg-input" col="NAME">
                </td>
            </tr>
            <tr>
                <td width="15%" class="left">
                    <label class="control-label">Value :<font class="red"> *</font></label>
                </td>
                <td width="18%">
                    <input type="text" id="VALUE" name="VALUE" class="CommonWidth form-control dlg-input" col="VALUE">
                </td>
            </tr>
        </table>
    </div>

    <div id="utc-container" style="display: none; padding:10px">


        <div class="bg1" >
            <table id="dlgTable1" class="Table1">
                <tr>
                    <td width="15%" class="left">
                        <label class="control-label">Category :<font class="red"> *</font></label>
                    </td>
                    <td width="18%">
                        <select id="selCATEGORY" disabled="disabled" class="form-control" col="CATEGORY" style="width:318px">
                            <option selected="selected">UtilityType</option>
                        </select>

                    </td>
                </tr>
                <tr>
                    <td width="15%" class="left">
                        <label class="control-label">Utility Type Name :<font class="red"> *</font></label>
                    </td>
                    <td width="18%">
                        <input type="text" id="NAME" name="NAME" class="CommonWidth form-control" style="width:318px"  col="NAME">
                    </td>
                </tr>
                
            </table>
        </div>
        <table id="tableUom" class="table">
            <thead  style="background-color: rgba(0,0,0,.05);">
                <tr>
                    <th scope="col">#</th>
                    <th scope="col" style="width:400px">Uom </th>
                    <th scope="col">Action</th>
                </tr>
            </thead>
            <tbody>
                
            </tbody>
        </table>


        <!-- Add rows button-->
       <div style="text-align:center">
           <input type="button" value="Add new row" class="button"  id="insertRow" />
       </div>


    </div>
</div>

<%--防止js缓存--%>
<script type="text/javascript">
    document.write("<script src='<%= Application["rootURL"]%>UtilityBillPay/js/ConfigList.js?" + Math.random() + "'><\/script>");
</script>

<style type="text/css">
    #dlgTable .dlg-input { width: 250px }
</style>









