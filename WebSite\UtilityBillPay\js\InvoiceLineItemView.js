
var queryAccountItems = ResolveUrl('~/UtilityBillPay/GeneralHandle/UtilityVendorAccountItems.ashx');
var queryInvoiceLine = ResolveUrl('~/UtilityBillPay/GeneralHandle/InvoiceLine.ashx');
var queryLineItemManagementUrl = ResolveUrl('~/UtilityBillPay/GeneralHandle/LineItemManagement.ashx');
var reportUrl = ResolveUrl('~/UtilityBillPay/GeneralHandle/Report.ashx');
var urlSearchInvoice = ResolveUrl('~/UtilityBillPay/CommonPage/SearchInvoice.aspx');

var parentOpener = window.opener;
var lineData = parentOpener.lineData;
var finalLineData = parentOpener.finalLineData;

var lineId = ex.ConvertToGuid(ex.request("line_id") === "undefined" ? "" : ex.request("line_id"));
var invoiceId = ex.ConvertToGuid(ex.request("Inv_id"));
var lineEnterDate = ex.request("lineEnterDate");
var statusId = ex.request("statusId");
var siteId = ex.request("site_id");
var vendorId = ex.request("vendor_id");
var accountId = ex.request("account_id");
var accNumber = ex.request("acc_number");
var uType = ex.request("u_type");
var hideShowInvoiceRelativeButton = ex.request("hideShowInvoiceRelativeButton");
var hasLimit = hasAllField();

var isSave = false;

var logId = parentOpener.logId;


//只有在该页面有操作，并且时间间隔大于5秒左右即会有提示
window.addEventListener("beforeunload", function (event) {
    if (isSave) {
        return;
    }

    var e = window.event || e;
    e.returnValue = ("Do you really want to close this window ？");
});

$(function () {
    if (uType === "Electricity") {
        $("#lbDemandInKw").show();
        $("#lineDEMAND_IN_KW").show();
    }

    DateControlBind();

    ValidateInit();

    BindMeters(accountId, uType);

    $("#METER").change(getLastInvoiceLine);

    console.log("statusName:", statusId);

    var isAdmin_TGSDev = ex.IsAdmin_TGSDev();
    console.log("isAdmin_TGSDev:", isAdmin_TGSDev);
    //Ready to Pay 以后的状状
    if (isAdmin_TGSDev == false && (statusId == "98a1b583-c8cf-4edb-b0f1-2bed6c56720c"
        || statusId == "d9854597-e2ec-4809-bae3-6d58988923a6"
        || statusId == "d325e97e-218f-47c9-a635-f7923c14ce1d"
        || statusId == "d9854597-e2ec-4809-bae3-6d58988923a6")) {
        $("#linePAY_AMOUNT").attr("disabled", "disabled");
    }
    else {
        $("#linePAY_AMOUNT").removeAttr();
    }


 

    InitPageData();

    $("#btnSaveDetail").hide();

    if (hasLimit) {
        $("#btnSaveDetail").show();
    }

    if (hideShowInvoiceRelativeButton === "hide") {
        // $("#btnSaveDetail").css("display", "none");
        // 
    }
    var feeVal = $("#lineLate_Fee").val();
    if (ex.toFixed(feeVal) == 0.00) {
        $(".lateFeeShow").hide();
    }
    else {
        $(".lateFeeShow").show();
    }


    $("#lineLate_Fee").keyup(function () {
        var feeVal = $("#lineLate_Fee").val();
        if (ex.toFixed(feeVal) == 0.00) {
            $(".lateFeeShow").hide();
        }
        else {
            $(".lateFeeShow").show();
        }
    });

    BindLATE_FEE_RELATED_INVOICE();
    

});

function ValidateInit() {
    var defaultRules = {
        BILLING_PERIOD_START_DATE: { required: true },
        READING_DATE: { required: true },
        TOTAL_USAGE: { required: true },
        USAGE_UNIT: { required: true },
        PAY_AMOUNT: { required: true },
        BALANCE_BF: { required: true },
        LATE_FEE_REASON: {
            required: function IsZero() {
                var feeVal = $("#lineLate_Fee").val();
                if (ex.toFixed(feeVal) > 0.00) {
                    return true;
                }
                return false;
            }
        },
        LATE_FEE_RELATED_INVOICE: { required: true },
    };

    if (uType === "Electricity") {
        defaultRules.DEMAND_IN_KW = { required: true };
    }

    $("[id$=aspnetForm]").validate({
        ignore: "",
        rules: defaultRules
    });
}

function changeDate() {
    $("#lineDAYS_COVERED").val("0");
    if (validData()) {
        var readingDate = $("#lineREADING_DATE").val();
        var bpsDate = $("#lineBILLING_PERIOD_START_DATE").val();
        if (bpsDate.length === 0 || readingDate.length === 0) {
            $("#lineDAYS_COVERED").val("0");
            return;
        }
        var days = ex.getDistanceDays(readingDate, bpsDate);
        $("#lineDAYS_COVERED").val(days + 1);
    }
}

function DateControlBind() {
    laydate.render({
        elem: '#lineREADING_DATE',
        lang: 'en',
        format: 'MM/dd/yyyy',
        trigger: 'focus',
        done: function (value, data) {
            $("[id$=aspnetForm]").validate().element($("#lineREADING_DATE"));

            changeDate();

            getDaysCovered();
        }
    });

    //没有最近的数据，控件变成可设置
    laydate.render({
        elem: '#lineBILLING_PERIOD_START_DATE',
        lang: 'en',
        format: 'MM/dd/yyyy',
        trigger: 'focus',
        done: function (value, data) {
            $("[id$=aspnetForm]").validate().element($("#lineBILLING_PERIOD_START_DATE"));

            getDaysCovered();
        }
    });

    $("#lineBILLING_PERIOD_START_DATE").change(getDaysCovered);
    $("#lineREADING_DATE").change(getDaysCovered);
}

function validData() {
    var value = $("#lineREADING_DATE").val();
    var billingDate = $("#lineBILLING_PERIOD_START_DATE").val();
    var ts = ex.toTimeStamp(value) - ex.toTimeStamp(billingDate);

    if (ts < 0) {
        ex.msgError("[Billing Period Start Date] cannot be greater than [Reading Date]");
        $("#lineREADING_DATE").val("");
        return false;
    }

    return true;
}

//自动计算
function getDaysCovered() {
    if (!validData()) return;

    /* var readingDate = $("#lineREADING_DATE").val();
     var bpsDate = $("#lineBILLING_PERIOD_START_DATE").val();
     if (bpsDate.length == 0 || readingDate.length == 0) {
         $("#lineDAYS_COVERED").val("0");
         return;
     }
     var days = ex.getDistanceDays(readingDate, bpsDate);
     $("#lineDAYS_COVERED").val(days+1);*/

    var days = parseFloat($("#lineDAYS_COVERED").val());

    //获取所有 line item 的数据
    var data = getTableRows();

    if (data.length === 0) return;

    //处理一下 相关数据
    data.forEach(item => {
        if (!item.COST || item.length === 0 || item.COST === '-')
            item.COST = 0;
        if (!item.UNITS || item.length === 0 || item.UNITS === '-')
            item.UNITS = 0;
    });

    //本单合计金额
    var totalAmountThisPeriod = 0;
    data.map(a => { return a.COST; }).forEach(item => {
        totalAmountThisPeriod += ex.toFloat(item);
    });

    $("#lineTOTAL_AMOUNT_THIS_PERIOD").val(totalAmountThisPeriod.toFixed(2));

    //总税费
    var totalTax = 0;
    data.filter(a => a.CLASS === "Tax").map(a => { return a.COST; }).forEach(i => { totalTax += ex.toFloat(i) });
    $("#lineTOTAL_TAXES").val(totalTax.toFixed(2));

    //税后合计金额
    var totalNetofTax = totalAmountThisPeriod - totalTax;
    $("#lineTOTAL_NET_OF_TAX").val(totalNetofTax.toFixed(2));

    //每日成本
    var costPerDay = days > 0 ? totalNetofTax / days : 0;
    $("#lineCOST_PER_DAY").val(costPerDay.toFixed(2));

    //每日成本（含税）
    var costPerDayIncludingTax = days > 0 ? totalAmountThisPeriod / days : 0;
    $("#lineCOST_PER_DAY_INCLUDING_TAX").val(costPerDayIncludingTax.toFixed(2));

    //每天使用量
    var totalUnits = ex.toFloat($("#lineTotal_Usage").val());
    var days = parseInt($("#lineDAYS_COVERED").val());
    var usageperDay = days > 0 ? totalUnits / days : 0;
    $("#lineUSAGE_PER_DAY").val(usageperDay.toFixed(2));

    //滞纳金
    var lateFee = ex.toFloat($("#lineLate_Fee").val());

    //总金额=本单+上一次欠款
    var balanceBF = ex.toFloat($("#lineBALANCE_BF").val());
    var totalBilledAmount = totalAmountThisPeriod + balanceBF + lateFee;
    $("#lineTOTAL_BILLED_AMOUNT").val(totalBilledAmount.toFixed(2));

    //付款
    var strPay = $.trim($("#linePAY_AMOUNT").val());
    if (strPay.length > 0) {
        var payAmount = ex.toFloat(strPay);
    }
}

function getTableRows() {
    var data = [];
    $('tr.item-info').each(function () {
        var item = {
            PREVIOUS_READING: $(this).find(".PREVIOUS_READING").val(),
            CURRENT_READING: $(this).find(".CURRENT_READING").val(),
            COST: $(this).find(".COST").val(),
            CLASS: $(this).find(".CLASS").val(),
            LINE_ITEM_DESCRIPTION: $(this).find(".LINE_ITEM_DESCRIPTION").val(),
            CHARGE_FREQUENCY: $(this).find(".CHARGE_FREQUENCY").val(),
            UNITS: $(this).find(".UNITS").val(),
            UNIT_TYPE: $(this).find(".UNIT_TYPE").val(),
            UNIT_RATE: $(this).find(".UNIT_RATE").val(),
            LINE_INDEX: $(this).find(".LINE_NUMBER").val(),
        }
        data.push(item);
    });

    return data;
}

function BindMeters(accountId, uType) {
    var data = [
        { DictType: "GetMeters", DicTypeWhere: " and EX_UTILITY_VENDOR_ACCOUNT_ID='" + accountId + "' and UTILITY_TYPE='" + uType + "'" },
        { DictType: "UnitType", DicTypeWhere: " and CATEGORY='" + uType + "' " },
        { DictType: "GetIsExistsInvoiceLineData", DicTypeWhere: " and EX_INVOICE_ID='" + invoiceId + "' and UTILITY_TYPE='" + uType + "' " }
    ];

    top.initDataDict({ url: queryAccountItems, dicTypeJson: { Type: "GetDataDictInfo", dicObj: encodeURI(JSON.stringify(data)) } });

    $("#lineUSAGE_UNIT").exComboBoxNew({
        data: top.getDataDict("UnitType"),
        key: "DictKey",
        value: "DictValue",
        class: "form-control"
    });

    var meterData = top.getDataDict("GetMeters");

    $("#METER").exComboBoxNew({
        data: meterData,
        key: "DictKey",
        value: "DictValue",
        class: "form-control"
    });

    //var index = lineData.findIndex(a => a.UTILITY_TYPE === uType);
    var index = parseInt(top.getDataDict("GetIsExistsInvoiceLineData")[0].DictValue);

    if (index === 0) {
        index = finalLineData.findIndex(a => a.UTILITY_TYPE === uType);
    }

    if (ex.isEmptyGuid(lineId)) {
        index = -1;
    }

    if (index < 0) {
        var obj = {
            DAYS_COVERED: 0,
            BALANCE_BF: 0,
            TOTAL_NET_OF_TAX: 0,
            TOTAL_AMOUNT_THIS_PERIOD: 0,
            TOTAL_BILLED_AMOUNT: 0,
            COST_PER_DAY: 0,
            COST_PER_DAY_INCLUDING_TAX: 0,
            USAGE_PER_DAY: 0,
            LATE_FEE: 0,
        }

        $("[id$=inv-line]").setWebControls(obj, ["invoicecol"]);//清空 Line Header
        $("#maintable tbody").html("");//情况 line item
    }

    //大于0 表示修改
    if (index >= 0) {
        return;
    }

    //如果meter 只有一项，那就默认
    if (meterData.length === 1) {
        $("#METER").val(meterData[0].DictKey);
        getLastInvoiceLine();
    }

    //如果有多项 就取上一次用过的 meter ，没有就随便找一个有glcode值的
    if (meterData.length > 1) {
        $.ajax({
            url: String.format("{0}?Type=GetLastLine&siteId={1}&acc={2}&uType={3}", queryInvoiceLine, siteId, accNumber, uType),
            type: "get",
            async: false,
            success: function (rs) {
                var defV = '';
                var obj = rs.Data;
                if (obj != null) {
                    meterData.forEach(a => {
                        if (a.DataKey === obj.Meter) {
                            defV = obj.Meter;
                        }
                    });
                }

                if (defV.length === 0) {
                    var arr = meterData.filter(a => !ex.isNullOrWhiteSpace(a.DictValueThree)).map(a => a.DictKey);
                    defV = arr[0];
                }

                $("#METER").val(defV);

                getLastInvoiceLine();
            }
        });
    }

    if (ex.isNullOrEmpty($("#lineUSAGE_UNIT").val())) {
        $("#lineUSAGE_UNIT").val("");
    }
}

//根据meter获取最近的账单
function getLastInvoiceLine() {
    $("#lineGL_CODE").val("");

    var meter = $("#METER").val();

    //取GL CODE
    var glCode = '';
    var meterData = top.getDataDict("GetMeters");
    meterData.forEach(item => {
        if (item.DictValue === meter) {
            glCode = item.DictValueThree;
            return;
        }
    });

    $("#lineGL_CODE").val(glCode);

    if (glCode.length === 0) {
        ex.confirm("Glcode has not been configured for empty meter. You can choose other options or configure it.",
            function () {
                location.href = '/UtilityBillPay/UtilityVendorAccount/edit.aspx';
            }, "Go to configure");

        return;
    }

    $.ajax({
        url: String.format("{0}?Type=GetLastInvoiceLine&siteId={1}&acc={2}&uType={3}&meter={4}&vid={5}", queryInvoiceLine, siteId, accNumber, uType, meter, vendorId),
        type: "get",
        async: false,
        success: function (rs) {
            var obj = rs.Data;
            if (obj == null) return;
            if (obj.LastReadingDate != null) {
                $("#lineBILLING_PERIOD_START_DATE").val(obj.LastReadingDate);
            }
            if (obj.BalanceBF != null) {
                var ss = ex.toFixed(obj.BalanceBF);

                $("#lineBALANCE_BF_TIP").html(ss);
            }

            //如果account过去的invoice都是同一个unit，自动填入；
            //如果这个account过去的invoice使用不同的invoice或者没有关联的invoice，unit就为空
            if (!ex.isNullOrEmpty(obj.USAGE_UNIT)) {
                $("#lineUSAGE_UNIT").val(obj.USAGE_UNIT);
            }
        },
        beforeSend: function () {
            $("#lineBALANCE_BF").val("");
            $("#lineBILLING_PERIOD_START_DATE").val("");
        }
    });
}

function InitPageData() {
    var allConfig = [];
    var items = [];

    var data1 = [
        { DictType: "GetIsExistsInvoiceLineData", DicTypeWhere: " and ID='" + lineId + "' " }
    ];

    top.initDataDict({ url: queryAccountItems, dicTypeJson: { Type: "GetDataDictInfo", dicObj: encodeURI(JSON.stringify(data1)) } });

    //var index = lineData.findIndex(a => a.ID === lineId);
    var index = parseInt(top.getDataDict("GetIsExistsInvoiceLineData")[0].DictValue);

    if (index === 0) {
        index = finalLineData.findIndex(a =>a.UTILITY_TYPE === uType);
    }

    //表示添加
    if (index < 0) {
        //获取指定 account 的模板
        allConfig = getTemplates(siteId, uType, vendorId);
        items = allConfig;

    }

    //表示修改
    if (index >= 0) {
        /*    var lineItemObj = lineData[index];
            if (lineItemObj != null) {
                lineItemObj.BALANCE_BF = ex.toFixed(lineItemObj.BALANCE_BF);
                lineItemObj.TOTAL_NET_OF_TAX = ex.toFixed(lineItemObj.TOTAL_NET_OF_TAX);
                lineItemObj.TOTAL_AMOUNT_THIS_PERIOD = ex.toFixed(lineItemObj.TOTAL_AMOUNT_THIS_PERIOD);
                lineItemObj.COST_PER_DAY = ex.toFixed(lineItemObj.COST_PER_DAY);
                lineItemObj.COST_PER_DAY_INCLUDING_TAX = ex.toFixed(lineItemObj.COST_PER_DAY_INCLUDING_TAX);
                lineItemObj.USAGE_PER_DAY = ex.toFixed(lineItemObj.USAGE_PER_DAY);
                lineItemObj.TOTAL_BILLED_AMOUNT = ex.toFixed(lineItemObj.TOTAL_BILLED_AMOUNT);
                lineItemObj.PAY_AMOUNT = ex.toFixed(lineItemObj.PAY_AMOUNT);
                lineItemObj.LATE_FEE = ex.toFixed(lineItemObj.LATE_FEE);
                lineItemObj.TOTAL_USAGE = ex.toFixed(lineItemObj.TOTAL_USAGE);
            }*/

        var finalLineIndex = finalLineData.findIndex(a => a.ID === lineId);

        if (finalLineIndex >= 0) {
            var lineItemObj = finalLineData[finalLineIndex];

            if (lineItemObj != null) {
                lineItemObj.BALANCE_BF = ex.toFixed(lineItemObj.BALANCE_BF);
                lineItemObj.TOTAL_NET_OF_TAX = ex.toFixed(lineItemObj.TOTAL_NET_OF_TAX);
                lineItemObj.TOTAL_AMOUNT_THIS_PERIOD = ex.toFixed(lineItemObj.TOTAL_AMOUNT_THIS_PERIOD);
                lineItemObj.COST_PER_DAY = ex.toFixed(lineItemObj.COST_PER_DAY);
                lineItemObj.COST_PER_DAY_INCLUDING_TAX = ex.toFixed(lineItemObj.COST_PER_DAY_INCLUDING_TAX);
                lineItemObj.USAGE_PER_DAY = ex.toFixed(lineItemObj.USAGE_PER_DAY);
                lineItemObj.TOTAL_BILLED_AMOUNT = ex.toFixed(lineItemObj.TOTAL_BILLED_AMOUNT);
                lineItemObj.PAY_AMOUNT = ex.toFixed(lineItemObj.PAY_AMOUNT);
                lineItemObj.LATE_FEE = ex.toFixed(lineItemObj.LATE_FEE);
                lineItemObj.TOTAL_USAGE = ex.toFixed(lineItemObj.TOTAL_USAGE);
            }
            var lateFeeRealtedInvoice = [];
            var reason = lineItemObj.LATE_FEE_RELATED_INVOICE;
            //console.log("reason:", reason);
            if (reason != null) {
                reason.forEach(function (item) {
                    lateFeeRealtedInvoice.push({ DictKey: item, DictValue: item, Checked: true });
                });
                //console.log("lateFeeRealtedInvoice:", lateFeeRealtedInvoice);
                BindLATE_FEE_RELATED_INVOICE(lateFeeRealtedInvoice);
            }



            //Line Header 赋值
            $("[id$=inv-line]").setWebControls(lineItemObj, ["invoicecol"]);

            items = finalLineData[finalLineIndex]?.LineItems;

            if (items != null && items.length > 0) {
                items.forEach(item => {
                    if (parseFloat(item.COST) > 0) {
                        item.COST = ex.toFixed(item.COST);
                    }
                });
            }
        } else {
            $.ajax({
                url: String.format("{0}?Type=GetInvoiceLineData&invoiceLineId={1}", queryInvoiceLine, lineId),
                type: "get",
                async: false,
                success: function (rs) {
                    var lineItemObj = rs.Data;
                    if (lineItemObj != null && lineItemObj.length > 0) {
                        lineItemObj[0].BALANCE_BF = ex.toFixed(lineItemObj[0].BALANCE_BF);
                        lineItemObj[0].TOTAL_NET_OF_TAX = ex.toFixed(lineItemObj[0].TOTAL_NET_OF_TAX);
                        lineItemObj[0].TOTAL_AMOUNT_THIS_PERIOD = ex.toFixed(lineItemObj[0].TOTAL_AMOUNT_THIS_PERIOD);
                        lineItemObj[0].COST_PER_DAY = ex.toFixed(lineItemObj[0].COST_PER_DAY);
                        lineItemObj[0].COST_PER_DAY_INCLUDING_TAX =
                            ex.toFixed(lineItemObj[0].COST_PER_DAY_INCLUDING_TAX);
                        lineItemObj[0].USAGE_PER_DAY = ex.toFixed(lineItemObj[0].USAGE_PER_DAY);
                        lineItemObj[0].TOTAL_BILLED_AMOUNT = ex.toFixed(lineItemObj[0].TOTAL_BILLED_AMOUNT);
                        lineItemObj[0].PAY_AMOUNT = ex.toFixed(lineItemObj[0].PAY_AMOUNT);
                        lineItemObj[0].LATE_FEE = ex.toFixed(lineItemObj[0].LATE_FEE);
                        lineItemObj[0].TOTAL_USAGE = ex.toFixed(lineItemObj[0].TOTAL_USAGE);


                        console.log("lineItemObj[0]:", lineItemObj[0]);

                        //EX_INVOICE_LINE_REASON
                        //var reason = rs.AdditionalProperties.LineReason;
                        //console.log("reason:", reason);
                        //lineItemObj[0].LATE_FEE_REASON = reason[0].LATE_FEE_REASON;
                        //lineItemObj[0].LATE_FEE_DESCRIPTION = reason[0].LATE_FEE_DESCRIPTION;
                        var lateFeeRealtedInvoice = [];

                        //reason.forEach(item => {
                        //    lateFeeRealtedInvoice.push({ DictKey: item.LATE_FEE_RELATED_INVOICE, DictValue: item.LATE_FEE_RELATED_INVOICE, Checked: true });
                        //});
                        var reason = lineItemObj[0].LATE_FEE_RELATED_INVOICE;
                        if (reason != null) {
                            reason.forEach(function (item) {
                                lateFeeRealtedInvoice.push({ DictKey: item, DictValue: item, Checked: true });
                            });
                            BindLATE_FEE_RELATED_INVOICE(lateFeeRealtedInvoice);
                        }
                        //Line Header 赋值
                        $("[id$=inv-line]").setWebControls(lineItemObj[0], ["invoicecol"]);

                        //items = lineData[index]?.LineItems;
                        items = rs.AdditionalProperties.LineItems;

                        if (items != null && items.length > 0) {
                            items.forEach(item => {
                                if (parseFloat(item.COST) > 0) {
                                    item.COST = ex.toFixed(item.COST);
                                }
                            });
                        }

             
                    }
                }
            });
        }
    }

    //第一次添加时 添加默认行
    if (items.length === 0) {
        items.push({
            CLASS: "",
            LINE_ITEM_DESCRIPTION: "",
            CHARGE_FREQUENCY: "",
            UNITS: "",
            UNIT_TYPE: "",
            UNIT_RATE: "",
            COST: ''
        });
    }

    //获取 class 的选项
    var data = [{ DictType: "GetClassList" }, { DictType: 'GetChargeFrequency' }];

    top.initDataDict({ url: queryLineItemManagementUrl, dicTypeJson: { Type: "GetDataDictInfo", dicObj: encodeURI(JSON.stringify(data)) } });

    //绑定模板
    items.forEach(item => {
        addLineItem(item);
    });

    $("#lineTotal_Usage").keyup(function () {
        var totalUnits = ex.toFloat($(this).val());

        var days = parseInt($("#lineDAYS_COVERED").val());

        var usageperDay = days > 0 ? totalUnits / days : 0;

        $("#lineUSAGE_PER_DAY").val(usageperDay.toFixed(2));
    });

    $("#lineBALANCE_BF").keyup(getDaysCovered);

    $("#lineDAYS_COVERED").keyup(function () {
        var value = $("#lineDAYS_COVERED").val();

        value = value.replace(/^(0+)|[^\d]+/g, '');

        $("#lineDAYS_COVERED").val(value);

        getDaysCovered();
    });


}

function addLineItem(obj) {
    if (!obj) {
        obj = {
            CLASS: "",
            LINE_ITEM_DESCRIPTION: "",
            CHARGE_FREQUENCY: "",
            UNITS: "",
            UNIT_TYPE: "",
            UNIT_RATE: "",
            COST: '',
            LINE_INDEX: 0,
        };
    }

    if (!obj.COST) obj.COST = '';

    var tempId = ex.NewGuid();
    var rowCount = $('.item-info').length + 1;

    if (!obj.LINE_INDEX || obj.LINE_INDEX == 0 || ex.isNullOrWhiteSpace(obj.LINE_INDEX)) {
        obj.LINE_INDEX = rowCount;
    }

    var contactdiv = '<tr class="item-info">' +

        '<td><select id="CLASS' + tempId + '" name="CLASS' + tempId + '" class="CLASS CommonWidth form-control" col="CLASS" ></select></td>' +
        '<td><input value="' + obj.LINE_ITEM_DESCRIPTION + '" type="text" name="LINE_ITEM_DESCRIPTION' + tempId + '" class="CommonWidth LINE_ITEM_DESCRIPTION form-control"  /></td>' +
        '<td><select type="text" id="CHARGE_FREQUENCY' + tempId + '" name="CHARGE_FREQUENCY' + tempId + '" class="CHARGE_FREQUENCY CommonWidth form-control" col="CHARGE_FREQUENCY" >' +
        '    <option value="">Please Select</option>' +
        '    <option>Monthly</option>' +
        '    </select></td>' +
        '<td><input value="' + obj.UNITS + '" type="text" name="UNITS' + tempId + '" class="UNITS CommonWidth form-control UpTwo"  /></td>' +
        '<td><select type="text" id="UNIT_TYPE' + tempId + '" name="UNIT_TYPE' + tempId + '" class="UNIT_TYPE CommonWidth form-control" col="UNIT_TYPE" >' +
        '    <option value="">Please Select</option>' +
        '    <option>therms</option>' +
        '    </select></td>' +
        '<td ><input value="' + obj.UNIT_RATE + '"  type="text" name="UNIT_RATE' + tempId + '" class="UNIT_RATE CommonWidth form-control"  /></td>' +
        '<td ><input value="' + obj.COST + '"  type="text" name="COST' + tempId + '" class="COST CommonWidth form-control UpTwo"  /></td>';
    if (hasLimit) {
        contactdiv += '<td class="act"><input value="' + obj.LINE_INDEX + '"  type="hidden" class="LINE_NUMBER"  col="LINE_NUMBER" /><span class="glyphicon glyphicon-plus classAdd" onclick="addLineItem()"></span> <span class="glyphicon glyphicon-minus deleteContact" onclick="delLineItem(this)"></span></td>';
    }
    contactdiv += '</tr>';
    $('#maintable').append(contactdiv);

    $("select[name='CLASS" + tempId + "']").rules("add", { required: true });

    $("#UNIT_TYPE" + tempId).val(obj.UNIT_TYPE);

    $(".LATE_FEE,.COST,.UNITS,.UNIT_RATE").keyup(function () {
        getDaysCovered();
    });

    $("select[name=CLASS" + tempId + "]").exComboBoxNew({
        data: top.getDataDict("GetClassList"),
        key: "DictKey",
        value: "DictValue",
        class: "form-control",
        default: $.trim(obj.CLASS)
    });

    $("select[name=UNIT_TYPE" + tempId + "]").exComboBoxNew({
        data: top.getDataDict("UnitType"),
        key: "DictKey",
        value: "DictValue",
        class: "form-control",
        default: $.trim(obj.UNIT_TYPE)
    });

    $("select[name=CHARGE_FREQUENCY" + tempId + "]").exComboBoxNew({
        data: top.getDataDict("GetChargeFrequency"),
        key: "DictKey",
        value: "DictValue",
        class: "form-control",
        default: $.trim(obj.CHARGE_FREQUENCY)
    });

    $("select[name=CLASS" + tempId + "]").change(getDaysCovered)

    BindDescriptionInput();

    BindAmtInput();
}

function delLineItem(obj) {
    $(obj).closest("tr").remove();

    getDaysCovered();
}

function getTemplates() {
    var data = [];
    $.ajax({
        url: String.format("{0}?Type=GetAllByType&siteId={1}&uType={2}&vendorId={3}", queryLineItemManagementUrl, siteId, uType, vendorId),
        type: "get",
        async: false,
        success: function (rs) {
            data = rs.Data;
        }
    });

    return data;
}

function BindDescriptionInput() {
    $(".LINE_ITEM_DESCRIPTION").unbind();
    $(".LINE_ITEM_DESCRIPTION").autocomplete({
        serviceUrl: queryLineItemManagementUrl,
        minChars: 1,
        maxHeight: 200,
        dataType: "json",
        zIndex: 49891017,
        paramName: "q",
        matchContains: true,
        width: 400,
        params: {
            Type: "GetAllDescription",
            limit: 30,
        },
        transformResult: function (obj) {
            var returnObj = {};
            if (obj.Tag === 1) {
                returnObj.suggestions = $.map(obj.Data,
                    function (row) {
                        return { value: row, data: row };
                    });
            }
            return returnObj;
        },
        onSelect: function (suggestion) {
            if (suggestion.data != null) {

            }
        },
        onSearchComplete: function (query) {
        }
    });
}

//金额输入框最多两位小数
function BindAmtInput() {
    $('.UpTwo').keyup(function () {
        var txt = $(this).val();
        var va = ex.regInputAmt(txt);
        $(this).val(va);

    }).blur(function () {
        this.value = ex.formattedAmount(this.value);
    });
}

function getFromLine() {
    var str = '';
    var meter = $("#METER").val();
    var data = top.getDataDict("GetMeters");

    data.forEach(item => {
        if (item.DictValue === meter) {
            str = item.DictValueTwo;
        }
    });

    return str;
}

function onSave() {
    var logContent = "";
    try {
        logContent += logId + ":::1";

        if (!$("#inv-line").validate().form()) {
            ex.msgError("Please enter all required fields");
            return;
        }

        var lineObj = $("[id$=inv-line]").getWebControls({}, ["invoicecol"]);

        logContent += ":::2";

        if (ex.isNullOrWhiteSpace(lineEnterDate) || lineEnterDate === "undefined") {
            lineObj.DATE_ENTERED = new Date();
        } else {
            lineObj.DATE_ENTERED = new Date(lineEnterDate.replace('AM', '').replace('PM', ''));
        }

        lineObj.METER = $("#METER").val();
        lineObj.INVOICE_TYPE = getFromLine();

        //if (ex.isNullOrWhiteSpace(lineObj.ID)) {
        //    lineObj.ID = ex.NewGuid();
        //}

        if (ex.isEmptyGuid(lineId)) {
            lineObj.ID = ex.NewGuid();
        } else {
            lineObj.ID = lineId;
        }

        var lineItems = getTableRows();

        logContent += ":::3";

        var total = 0;
        lineItems.map(a => { return a.COST; }).forEach(item => {
            total += ex.toFloat(item);
        });

        logContent += ":::4";

        //本单+上一期欠款
        total += ex.toFloat(lineObj.BALANCE_BF);

        //合并
        var lineRow = {
            ...lineObj,
            UTILITY_TYPE: uType,
            TotalCost: total.toFixed(2),
            LineItems: lineItems
        }

        logContent += ":::5";

        var addFlag = false;

        if (finalLineData.length > 0) {
            logContent += ":::6";

            //这里需要判断 meter 是否重复
            //var index = lineData.findIndex(a => a.METER === lineObj.METER);
            var index = finalLineData.findIndex(a => a.METER === lineObj.METER);

            //存在时修改，不存在则添加
            //index >= 0 ? lineData[index] = lineRow : lineData.push(lineRow);

            if (ex.isNullOrEmpty(lineRow.LATE_FEE)) {
                lineRow.LATE_FEE = 0;
            }

            if (ex.isNullOrEmpty(lineRow.DEMAND_IN_KW)) {
                lineRow.DEMAND_IN_KW = 0;
            }

            index >= 0 ? finalLineData[index] = lineRow : finalLineData.push(lineRow);

            logContent += ":::7";
        } else {
            logContent += ":::8";

            addFlag = true;

            if (ex.isNullOrEmpty(lineRow.LATE_FEE)) {
                lineRow.LATE_FEE = 0;
            }

            if (ex.isNullOrEmpty(lineRow.DEMAND_IN_KW)) {
                lineRow.DEMAND_IN_KW = 0;
            }

            finalLineData.push(lineRow);

            logContent += ":::9";
        }

        //绑定Line Item Details
        //parentOpener.BindLintItemDetails(lineData);

        if (addFlag) {
            logContent += ":::10";
            //ex.AddNormalLog(logContent);
            parentOpener.GetInvoiceLineV3(logContent);
        } else {
            logContent += ":::11";
            //ex.AddNormalLog(logContent);
            parentOpener.RefreshTable(logContent);
        }

        parentOpener.SetSupNo(true);

        isSave = true;

        window.close();
    } catch (err) {
        ex.AddNormalLog("出现错误" + err);
    }
}

function onClose() {
    window.close();
} 

var obj = [];
obj.push({ DictKey: "None", DictValue: "None", Checked: false });
obj.push({ DictKey: "TBD", DictValue: "TBD", Checked: false });
function BindLATE_FEE_RELATED_INVOICE(invoices) {

    $('#txtLATE_FEE_RELATED_INVOICE').multiselect('destroy');

    $("#txtLATE_FEE_RELATED_INVOICE").exComboBoxNewV2({
        data: [],
        key: "DictKey",
        value: "DictValue",
        checked:"Checked",
        class: "form-control",
        showEmpty: false
    });

    $('#txtLATE_FEE_RELATED_INVOICE').multiselect({
        enableFiltering: true,
        enableCaseInsensitiveFiltering: true,
        includeSelectAllOption: true,
        selectAllJustVisible: true,
        nonSelectedText: 'Please Select',
        maxHeight: 300,
        onInitialized: function (select, container) {
            var loading = startLoading();
            var deferred = $.Deferred();
           
            if (invoices != undefined) {
                $.each(invoices, function (index, value) {
                    const isExists = obj.some(a => a.DictKey === value.DictKey);
                    if (isExists == false) {
                        obj.push({ DictKey: value.DictKey, DictValue: value.DictValue, Checked: value.Checked });
                    }
                    else {
                        //obj.map(obj => { obj["Checked"] = true });
                        obj.forEach(item => {
                            if (item.DictKey === value.DictKey) {
                                item.Checked = true;
                            }
                        });
                    }
                });
                console.log("obj:", obj);
                console.log("invoices:", invoices);
            }


            deferred.resolve(obj);
            //console.log("obj:", obj);

            deferred.done(function (obj) {

                closeLoading(loading);
                $('#txtLATE_FEE_RELATED_INVOICE').multiselect('destroy');
                $("#txtLATE_FEE_RELATED_INVOICE").exComboBoxNewV2({
                    // data: obj.AdditionalProperties.invoiceLineReason,
                    data: obj,
                    key: "DictKey",
                    value: "DictValue",
                    checked:"Checked",
                    class: "form-control",
                    showEmpty: false
                });
                $('#txtLATE_FEE_RELATED_INVOICE').multiselect({
                    enableFiltering: true,
                    enableCaseInsensitiveFiltering: true,
                    includeSelectAllOption: true,
                    selectAllJustVisible: true,
                    nonSelectedText: 'Please Select',
                    maxHeight: 300,
                    /*buttonContainer: '<div class="btn-group open" />'*/
                      onChange: function (option, checked, select) {
                          console.log("Option " + option.val() + " is " + (checked ? "checked" : "unchecked"));
                          obj.forEach(item => {
                              if (item.DictKey === option.val()) {
                                  item.Checked = checked;
                              }
                          });
                    }
                    , onSelectAll: function (event) {
                        obj.map(obj => { obj["Checked"] = true });
                        console.log("onSelectAll:", obj);
                    }
                    , onDeselectAll: function (event) {
                        obj.map(obj => { obj["Checked"] = false });
                        console.log("onDeselectAll:", obj);
                    }
                });
            }).fail(function (error) {

                closeLoading(loading);
                ex.msgError(error);
            });
            console.log("obj0:", obj);
            //select.multiselect('select', lateFeeRealtedInvoice);
        }

    });

}

function startLoading() {
    var loading = layer.load(2, { shade: false, time: 5 * 1000 });
    return loading;
}

function closeLoading(loading) {
    if (loading) {
        layer.close(loading);
    }
}
function ClearRelated() {
    obj = [];
    obj.push({ DictKey: "None", DictValue: "None", Checked: false });
    obj.push({ DictKey: "TBD", DictValue: "TBD", Checked: false });
    BindLATE_FEE_RELATED_INVOICE();
}

$("#btnSelect").click(function () {

    ex.openDialog({
        type: 2,
        title: "",
        btn: null,
        content: urlSearchInvoice + "?wherePage=InvoiceLine&accNumber=" + accNumber,
        width: $(window).width() - 100 + 'px',
        height: $(window).height() - 50 + 'px',
        success: function (layero, index) {

        },
        cancel: function () {

        }
    });
}); 

function ChangeParent(id, uniqueReference) {
    var objNew = [];
    objNew.push({ DictKey: uniqueReference, DictValue: uniqueReference, Checked: true });
    BindLATE_FEE_RELATED_INVOICE(objNew);

}

function ChangeParentToInvoiceLine(obj) {
    //console.log("obj:", obj)
    BindLATE_FEE_RELATED_INVOICE(obj);
    // 默认全选
    //$('#txtLATE_FEE_RELATED_INVOICE').multiselect('selectAll', false);
    //$('#txtLATE_FEE_RELATED_INVOICE').multiselect('refresh');

    // 默认全选
    //$('#txtLATE_FEE_RELATED_INVOICE').multiselect('selectAll', true);
}
  
    