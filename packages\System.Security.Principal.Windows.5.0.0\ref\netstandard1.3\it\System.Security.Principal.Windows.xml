﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Principal.Windows</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle">
      <summary>[SecurityCritical] Fornisce un handle sicuro a un token di accesso di thread o processo di Windows.Per altre informazioni, vedere la pagina relativa ai token di accesso.</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.#ctor(System.IntPtr)">
      <summary>[SecurityCritical] Inizializza una nuova istanza della classe <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</summary>
      <param name="handle">Oggetto <see cref="T:System.IntPtr" /> che rappresenta l'handle preesistente da usare.Se si usa <see cref="F:System.IntPtr.Zero" />, viene restituito un handle non valido.</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.InvalidHandle">
      <summary>[SecurityCritical] Restituisce un handle non valido creando un'istanza di un oggetto <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> con <see cref="F:System.IntPtr.Zero" />.</summary>
      <returns>Restituisce un oggetto <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</returns>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.IsInvalid">
      <summary>[SecurityCritical] Ottiene un valore che indica se l'handle non è valido.</summary>
      <returns>true se l'handle non è valido; in caso contrario, false.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityNotMappedException">
      <summary>Rappresenta un'eccezione per un'entità per la quale non è stato possibile eseguire il mapping dell'identità con un'identità nota.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.IdentityNotMappedException" />.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.IdentityNotMappedException" /> utilizzando il messaggio di errore specificato.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.IdentityNotMappedException" /> tramite il messaggio di errore e l'eccezione interna specificati.</summary>
      <param name="message">Messaggio di errore nel quale viene indicato il motivo dell’eccezione</param>
      <param name="inner">Eccezione causa dell'eccezione corrente.Se <paramref name="inner" /> non è null, l'eccezione corrente viene generata in un blocco catch che gestisce l'eccezione interna.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityNotMappedException.UnmappedIdentities">
      <summary>Rappresenta l'insieme delle identità senza mapping per un'eccezione <see cref="T:System.Security.Principal.IdentityNotMappedException" />.</summary>
      <returns>Insieme delle identità senza mapping.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReference">
      <summary>Rappresenta un'identità ed è la classe base delle classi <see cref="T:System.Security.Principal.NTAccount" /> e <see cref="T:System.Security.Principal.SecurityIdentifier" />.Questa class non fornisce un costruttore pubblico e di conseguenza non può essere ereditata.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Equals(System.Object)">
      <summary>Restituisce un valore che indica se l'oggetto specificato è uguale all'istanza corrente della classe <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>true se <paramref name="o" /> è un oggetto con lo stesso tipo sottostante e valore di questa istanza di <see cref="T:System.Security.Principal.IdentityReference" />; in caso contrario false.</returns>
      <param name="o">Oggetto da confrontare con l'istanza di <see cref="T:System.Security.Principal.IdentityReference" /> corrente o riferimento null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.GetHashCode">
      <summary>Viene utilizzato come funzione hash per <see cref="T:System.Security.Principal.IdentityReference" />.<see cref="M:System.Security.Principal.IdentityReference.GetHashCode" /> può essere utilizzato in algoritmi di hash e strutture di dati, ad esempio una tabella hash.</summary>
      <returns>Codice hash per questo oggetto <see cref="T:System.Security.Principal.IdentityReference" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.IsValidTargetType(System.Type)">
      <summary>Restituisce un valore che indica se il tipo specificato è un tipo di conversione valido per la classe <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>true se <paramref name="targetType" /> è un tipo di conversione valido per la classe <see cref="T:System.Security.Principal.IdentityReference" />; in caso contrariofalse.</returns>
      <param name="targetType">Tipo di cui viene verificata la validità per la conversione da <see cref="T:System.Security.Principal.IdentityReference" />.I seguenti tipi di destinazione sono validi:<see cref="T:System.Security.Principal.NTAccount" /><see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Equality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Confronta due oggetti <see cref="T:System.Security.Principal.IdentityReference" /> per determinarne l'uguaglianza.I due oggetti vengono considerati uguali se la loro rappresentazione del nome canonico corrisponde a quella restituita dalla proprietà <see cref="P:System.Security.Principal.IdentityReference.Value" /> o se sono entrambi null.</summary>
      <returns>true se <paramref name="left" /> e <paramref name="right" /> sono uguali; in caso contrario, false.</returns>
      <param name="left">Operando di sinistra <see cref="T:System.Security.Principal.IdentityReference" /> da utilizzare per il confronto di uguaglianza.Questo parametro può essere null.</param>
      <param name="right">Operando di destra <see cref="T:System.Security.Principal.IdentityReference" /> da utilizzare per il confronto di uguaglianza.Questo parametro può essere null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Inequality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Confronta due oggetti <see cref="T:System.Security.Principal.IdentityReference" /> per determinarne la disuguaglianza.I due oggetti non vengono considerati uguali se hanno rappresentazioni del nome canonico diverse da quella restituita dalla proprietà <see cref="P:System.Security.Principal.IdentityReference.Value" /> o se solo uno degli oggetti è null.</summary>
      <returns>true se <paramref name="left" /> e <paramref name="right" /> non sono uguali; in caso contrario, false.</returns>
      <param name="left">Operando di sinistra <see cref="T:System.Security.Principal.IdentityReference" /> da utilizzare per il confronto di disuguaglianza.Questo parametro può essere null.</param>
      <param name="right">Operando di destra <see cref="T:System.Security.Principal.IdentityReference" /> da utilizzare per il confronto di disuguaglianza.Questo parametro può essere null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.ToString">
      <summary>Restituisce la rappresentazione di stringa dell'identità rappresentata dall'oggetto <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Identità in formato stringa.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Translate(System.Type)">
      <summary>Converte il nome di account rappresentato dall'oggetto <see cref="T:System.Security.Principal.IdentityReference" /> in un altro tipo derivato da <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Identità convertita.</returns>
      <param name="targetType">Tipo di destinazione per la conversione da <see cref="T:System.Security.Principal.IdentityReference" />. </param>
    </member>
    <member name="P:System.Security.Principal.IdentityReference.Value">
      <summary>Ottiene il valore stringa dell'identità rappresentata dall'oggetto <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Valore stringa dell'identità rappresentata dall'oggetto <see cref="T:System.Security.Principal.IdentityReference" />.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReferenceCollection">
      <summary>Rappresenta una raccolta di oggetti <see cref="T:System.Security.Principal.IdentityReference" /> e fornisce una soluzione per convertire gruppi di oggetti derivati da <see cref="T:System.Security.Principal.IdentityReference" /> in tipi derivati da <see cref="T:System.Security.Principal.IdentityReference" />. </summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> con zero elementi nella raccolta.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor(System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> usando la dimensione iniziale specificata.</summary>
      <param name="capacity">Numero iniziale di elementi contenuti nella raccolta.Il valore di <paramref name="capacity" /> è soltanto un'indicazione e non è necessariamente il numero massimo di elementi creati.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Add(System.Security.Principal.IdentityReference)">
      <summary>Aggiunge un oggetto <see cref="T:System.Security.Principal.IdentityReference" /> alla raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <param name="identity">Oggetto <see cref="T:System.Security.Principal.IdentityReference" /> da aggiungere alla raccolta.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Clear">
      <summary>Cancella tutti gli oggetti <see cref="T:System.Security.Principal.IdentityReference" /> dalla raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Contains(System.Security.Principal.IdentityReference)">
      <summary>Indica se la raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> contiene l'oggetto <see cref="T:System.Security.Principal.IdentityReference" /> specificato.</summary>
      <returns>true se la raccolta contiene l'oggetto specificato.</returns>
      <param name="identity">Oggetto <see cref="T:System.Security.Principal.IdentityReference" /> da verificare.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.CopyTo(System.Security.Principal.IdentityReference[],System.Int32)">
      <summary>Copia la raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> in una matrice <see cref="T:System.Security.Principal.IdentityReferenceCollection" />, a partire dall'indice specificato.</summary>
      <param name="array">Oggetto di una matrice <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> in cui copiare la raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</param>
      <param name="offset">Indice in base zero nel parametro <paramref name="array" /> in cui viene copiata la raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Count">
      <summary>Ottiene il numero di elementi nella raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Numero di oggetti <see cref="T:System.Security.Principal.IdentityReference" /> nella raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.GetEnumerator">
      <summary>Ottiene un enumeratore che può essere usato per eseguire l'iterazione nella raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Enumeratore per la raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Item(System.Int32)">
      <summary>Ottiene o imposta il nodo in corrispondenza dell'indice specificato dalla raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Oggetto <see cref="T:System.Security.Principal.IdentityReference" /> della raccolta in corrispondenza dell'indice specificato.Se <paramref name="index" /> è maggiore o uguale al numero di nodi della raccolta, il valore restituito è null.</returns>
      <param name="index">Indice in base zero della raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Remove(System.Security.Principal.IdentityReference)">
      <summary>Rimuove l'oggetto <see cref="T:System.Security.Principal.IdentityReference" /> specificato dalla raccolta.</summary>
      <returns>true se l'oggetto specificato è stato rimosso dalla raccolta.</returns>
      <param name="identity">Oggetto <see cref="T:System.Security.Principal.IdentityReference" /> da rimuovere.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.System#Collections#Generic#ICollection{T}#IsReadOnly"></member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Ottiene un enumeratore che può essere usato per eseguire l'iterazione nella raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Enumeratore per la raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type)">
      <summary>Converte gli oggetti nella raccolta nel tipo specificato.La chiamata a questo metodo equivale a chiamare <see cref="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)" /> con il secondo parametro impostato su false, per indicare che non verranno generate eccezioni per gli elementi la cui conversione non riesce.</summary>
      <returns>Raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> che rappresenta il contenuto convertito della raccolta originale.</returns>
      <param name="targetType">Tipo in cui vengono convertiti gli elementi della raccolta.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)">
      <summary>Converte gli oggetti della raccolta nel tipo specificato e usa la tolleranza di errore specificata per gestire o ignorare gli errori associati a un tipo senza un mapping di conversione.</summary>
      <returns>Raccolta <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> che rappresenta il contenuto convertito della raccolta originale.</returns>
      <param name="targetType">Tipo in cui vengono convertiti gli elementi della raccolta.</param>
      <param name="forceSuccess">Valore booleano che determina come vengono gestiti gli errori di conversione.Se <paramref name="forceSuccess" /> è true, gli errori di conversione causati dalla mancanza di un mapping per la traduzione impediscono la riuscita della conversione e generano eccezioni.Se <paramref name="forceSuccess" /> è false, i tipi che non sono stati convertiti per la mancanza di un mapping per la traduzione vengono copiati senza essere convertiti nella raccolta restituita.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.NTAccount">
      <summary>Rappresenta un account utente o gruppo.</summary>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.NTAccount" /> tramite il nome specificato.</summary>
      <param name="name">Nome utilizzato per creare l'oggetto <see cref="T:System.Security.Principal.NTAccount" />.Questo parametro non può essere null o una stringa vuota.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="name" /> è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="name" /> è una stringa vuota.- oppure -<paramref name="name" /> è troppo lungo.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.NTAccount" /> tramite il nome di dominio e di account specificati. </summary>
      <param name="domainName">Nome del dominio.Questo parametro può essere null o una stringa vuota.I nomi di dominio con valore null vengono trattati come stringhe vuote.</param>
      <param name="accountName">Nome dell'account.Questo parametro non può essere null o una stringa vuota.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="accountName" /> è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="accountName" /> è una stringa vuota.- oppure -<paramref name="accountName" /> è troppo lungo.- oppure -<paramref name="domainName" /> è troppo lungo.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Equals(System.Object)">
      <summary>Restituisce un valore che indica se l'oggetto <see cref="T:System.Security.Principal.NTAccount" /> corrente è uguale a un oggetto specificato.</summary>
      <returns>true se <paramref name="o" /> è un oggetto con lo stesso tipo sottostante e valore di questo oggetto <see cref="T:System.Security.Principal.NTAccount" />; in caso contrario false.</returns>
      <param name="o">Oggetto da confrontare con questo oggetto <see cref="T:System.Security.Principal.NTAccount" /> o null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.GetHashCode">
      <summary>Viene utilizzato come funzione hash per l'oggetto corrente <see cref="T:System.Security.Principal.NTAccount" />.Il metodo <see cref="M:System.Security.Principal.NTAccount.GetHashCode" /> è adatto per apporre un numero hash agli algoritmi e alle strutture dei dati, ad esempio una tabella hash.</summary>
      <returns>Valore hash per l'oggetto corrente <see cref="T:System.Security.Principal.NTAccount" />.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)">
      <summary>Restituisce un valore che indica se il tipo specificato è un tipo di conversione valido per la classe <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>true se <paramref name="targetType" /> è un tipo di conversione valido per la classe <see cref="T:System.Security.Principal.NTAccount" />; in caso contrariofalse.</returns>
      <param name="targetType">Tipo di cui viene verificata la validità per la conversione da <see cref="T:System.Security.Principal.NTAccount" />.I seguenti tipi di destinazione sono validi:- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Equality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Confronta due oggetti <see cref="T:System.Security.Principal.NTAccount" /> per determinarne l'uguaglianza.I due oggetti vengono considerati uguali se la loro rappresentazione del nome canonico corrisponde a quella restituita dalla proprietà <see cref="P:System.Security.Principal.NTAccount.Value" /> o se sono entrambi null.</summary>
      <returns>true se <paramref name="left" /> e <paramref name="right" /> sono uguali; in caso contrario, false.</returns>
      <param name="left">Operando di sinistra da utilizzare per il confronto di uguaglianza.Questo parametro può essere null.</param>
      <param name="right">Operando di destra da utilizzare per il confronto di uguaglianza.Questo parametro può essere null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Inequality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Confronta due oggetti <see cref="T:System.Security.Principal.NTAccount" /> per determinarne la disuguaglianza.I due oggetti non vengono considerati uguali se hanno rappresentazioni del nome canonico diverse da quella restituita dalla proprietà <see cref="P:System.Security.Principal.NTAccount.Value" /> o se solo uno degli oggetti è null.</summary>
      <returns>true se <paramref name="left" /> e <paramref name="right" /> non sono uguali; in caso contrario, false.</returns>
      <param name="left">Operando di sinistra da utilizzare per il confronto di disuguaglianza.Questo parametro può essere null.</param>
      <param name="right">Operando di destra da utilizzare per il confronto di disuguaglianza.Questo parametro può essere null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.ToString">
      <summary>Restituisce il nome in formato Dominio\Account dell'account rappresentato dall'oggetto <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>Nome dell'account in formato Dominio\Account.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Translate(System.Type)">
      <summary>Converte il nome di account rappresentato dall'oggetto <see cref="T:System.Security.Principal.NTAccount" /> in un altro tipo derivato da <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Identità convertita.</returns>
      <param name="targetType">Tipo di destinazione per la conversione da <see cref="T:System.Security.Principal.NTAccount" />.Il tipo di destinazione deve essere un tipo considerato valido dal metodo <see cref="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />non è un tipo <see cref="T:System.Security.Principal.IdentityReference" />.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Impossibile convertire parte e o tutti i riferimenti di identità.</exception>
      <exception cref="T:System.SystemException">Il nome dell'account di origine è troppo lungo.- oppure -È stato restituito un codice di errore Win32.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.NTAccount.Value">
      <summary>Restituisce una rappresentazione sotto forma di stringa in caratteri maiuscoli dell'oggetto <see cref="T:System.Security.Principal.NTAccount" /> corrente.</summary>
      <returns>Rappresentazione sotto forma di stringa in caratteri maiuscoli dell'oggetto <see cref="T:System.Security.Principal.NTAccount" /> corrente.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.SecurityIdentifier">
      <summary>Rappresenta un identificatore di sicurezza (SID) e fornisce le operazioni di marshalling e confronto per i SID.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Byte[],System.Int32)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> tramite una rappresentazione binaria specificata di un SID.</summary>
      <param name="binaryForm">Matrice di byte che rappresenta il SID.</param>
      <param name="offset">Offset di byte da utilizzare come indice iniziale in <paramref name="binaryForm" />. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.IntPtr)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> tramite un intero che rappresenta il formato binario di un identificatore di sicurezza (SID).</summary>
      <param name="binaryForm">Intero che rappresenta il formato binario di un identificatore di sicurezza (SID).</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Security.Principal.WellKnownSidType,System.Security.Principal.SecurityIdentifier)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> tramite il tipo di SID noto specificato e il SID del dominio.</summary>
      <param name="sidType">Uno dei valori dell'enumerazione.Questo valore deve essere diverso da <see cref="F:System.Security.Principal.WellKnownSidType.LogonIdsSid" />.</param>
      <param name="domainSid">SID del dominio.Questo valore è obbligatorio per i valori seguenti di <see cref="T:System.Security.Principal.WellKnownSidType" />.Questo parametro viene ignorato se contiene qualsiasi altro valore <see cref="T:System.Security.Principal.WellKnownSidType" />.- <see cref="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountGuestSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountComputersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountControllersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> utilizzando il SID specificato in formato SDDL (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Stringa SDDL relativa al SID utilizzato per creare l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.AccountDomainSid">
      <summary>Restituisce la parte del SID del dominio degli account dal SID rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> se il SID rappresenta un SID di account Windows.Se il SID non rappresenta un SID di account Windows, questa proprietà restituisce <see cref="T:System.ArgumentNullException" />.</summary>
      <returns>La parte del SID del dominio degli account rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> se il SID rappresenta un SID di account Windows; in caso contrario, restituisce <see cref="T:System.ArgumentNullException" />.</returns>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.BinaryLength">
      <summary>Restituisce la lunghezza in byte del SID rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Lunghezza in byte del SID rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.CompareTo(System.Security.Principal.SecurityIdentifier)">
      <summary>Confronta l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> corrente con l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato.</summary>
      <returns>Numero con segno che indica i valori relativi di questa istanza e di  <paramref name="sid" />.Valore restituito Descrizione Minore di zero Questa istanza è minore di <paramref name="sid" />. Zero Questa istanza è uguale al parametro <paramref name="sid" />. Maggiore di zero L'istanza è maggiore di <paramref name="sid" />. </returns>
      <param name="sid">Oggetto da confrontare con l'oggetto corrente.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Object)">
      <summary>Restituisce un valore che indica se l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> corrente è uguale a un oggetto specificato.</summary>
      <returns>true se <paramref name="o" /> è un oggetto con lo stesso tipo sottostante e valore di questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />; in caso contrario false.</returns>
      <param name="o">Oggetto da confrontare con questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> o null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Security.Principal.SecurityIdentifier)">
      <summary>Indica se l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> specificato è uguale all'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> corrente.</summary>
      <returns>true se il valore di <paramref name="sid" /> è uguale al valore dell'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> corrente.</returns>
      <param name="sid">Oggetto da confrontare con l'oggetto corrente.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Copia la rappresentazione binaria del SID specificato rappresentato dalla classe <see cref="T:System.Security.Principal.SecurityIdentifier" /> in una matrice di byte.</summary>
      <param name="binaryForm">Matrice di byte che riceverà il SID copiato.</param>
      <param name="offset">Offset di byte da utilizzare come indice iniziale in <paramref name="binaryForm" />. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetHashCode">
      <summary>Viene utilizzato come funzione hash per l'oggetto corrente <see cref="T:System.Security.Principal.SecurityIdentifier" />.Il metodo <see cref="M:System.Security.Principal.SecurityIdentifier.GetHashCode" /> è adatto per apporre un numero hash agli algoritmi e alle strutture dei dati, ad esempio una tabella hash.</summary>
      <returns>Valore hash per l'oggetto corrente <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsAccountSid">
      <summary>Restituisce un valore che indica se il SID rappresentato da questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> è un SID di account Windows valido.</summary>
      <returns>true se il SID rappresentato da questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> è un SID di account Windows valido; in caso contrario, false.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsEqualDomainSid(System.Security.Principal.SecurityIdentifier)">
      <summary>Restituisce un valore che indica se il SID rappresentato da questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> appartiene allo stesso dominio del SID specificato.</summary>
      <returns>true se il SID rappresentato da questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> appartiene allo stesso dominio del SID <paramref name="sid" />; in caso contrario, false.</returns>
      <param name="sid">SID da confrontare con questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)">
      <summary>Restituisce un valore che indica se il tipo specificato è un tipo di conversione valido per la classe <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>true se <paramref name="targetType" /> è un tipo di conversione valido per la classe <see cref="T:System.Security.Principal.SecurityIdentifier" />; in caso contrariofalse.</returns>
      <param name="targetType">Tipo di cui viene verificata la validità per la conversione da <see cref="T:System.Security.Principal.SecurityIdentifier" />.I seguenti tipi di destinazione sono validi:- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsWellKnown(System.Security.Principal.WellKnownSidType)">
      <summary>Restituisce un valore che indica se l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> corrisponde al tipo del SID noto specificato. </summary>
      <returns>true se <paramref name="type" /> è il tipo di SID dell'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />; in caso contrario, false.</returns>
      <param name="type">Valore da confrontare con l'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MaxBinaryLength">
      <summary>Restituisce la dimensione massima in byte della rappresentazione binaria del SID.</summary>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MinBinaryLength">
      <summary>Restituisce la dimensione minima in byte della rappresentazione binaria del SID.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Equality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Confronta due oggetti <see cref="T:System.Security.Principal.SecurityIdentifier" /> per determinarne l'uguaglianza.I due oggetti vengono considerati uguali se la loro rappresentazione del nome canonico corrisponde con quella restituita dalla proprietà <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> o se sono entrambi null.</summary>
      <returns>true se <paramref name="left" /> e <paramref name="right" /> sono uguali; in caso contrario, false.</returns>
      <param name="left">Operando di sinistra da utilizzare per il confronto di uguaglianza.Questo parametro può essere null.</param>
      <param name="right">Operando di destra da utilizzare per il confronto di uguaglianza.Questo parametro può essere null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Inequality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Confronta due oggetti <see cref="T:System.Security.Principal.SecurityIdentifier" /> per determinarne la disuguaglianza.I due oggetti non vengono considerati uguali se hanno rappresentazioni del nome canonico diverse da quella restituita dalla proprietà <see cref="P:System.Security.Principal.SecurityIdentifier.Value" /> o se solo uno degli oggetti è null.</summary>
      <returns>true se <paramref name="left" /> e <paramref name="right" /> non sono uguali; in caso contrario, false.</returns>
      <param name="left">Operando di sinistra da utilizzare per il confronto di disuguaglianza.Questo parametro può essere null.</param>
      <param name="right">Operando di destra da utilizzare per il confronto di disuguaglianza.Questo parametro può essere null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.ToString">
      <summary>Restituisce il SID, in formato SDDL, per l'account rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.Esempio di formato SDDL: S-1-5-9.</summary>
      <returns>SID in formato SDDL per l'account rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Translate(System.Type)">
      <summary>Converte il nome di account rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> in un altro tipo derivato da <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Identità convertita.</returns>
      <param name="targetType">Tipo di destinazione per la conversione da <see cref="T:System.Security.Principal.SecurityIdentifier" />.Il tipo di destinazione deve essere un tipo considerato valido dal metodo <see cref="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="targetType " />è null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " />non è un tipo <see cref="T:System.Security.Principal.IdentityReference" />.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Impossibile convertire parte e o tutti i riferimenti di identità.</exception>
      <exception cref="T:System.SystemException">È stato restituito un codice di errore Win32.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.Value">
      <summary>Restituisce una stringa SDDL (Security Descriptor Definition Language) in caratteri maiuscoli per il SID rappresentato da questo oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Stringa SDDL in caratteri maiuscoli per il SID rappresentato dall'oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.TokenAccessLevels">
      <summary>Definisce i privilegi dell'account utente associato al token di accesso. </summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustDefault">
      <summary>L'utente può modificare il proprietario predefinito, il gruppo primario o l'elenco di controllo di accesso discrezionale (DACL) del token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustGroups">
      <summary>L'utente può modificare gli attributi dei gruppi nel token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges">
      <summary>L'utente può attivare o disabilitare i privilegi nel token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustSessionId">
      <summary>L'utente può modificare l'identificatore di sessione del token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AllAccess">
      <summary>L'utente dispone di qualsiasi tipo di accesso possibile al token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AssignPrimary">
      <summary>L'utente può associare un token primario a un processo.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Duplicate">
      <summary>L'utente può duplicare il token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Impersonate">
      <summary>L'utente può rappresentare un client.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.MaximumAllowed">
      <summary>Il valore massimo che è possibile assegnare per l'enumerazione <see cref="T:System.Security.Principal.TokenAccessLevels" />.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Query">
      <summary>L'utente può eseguire query nel token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.QuerySource">
      <summary>L'utente può eseguire query nell'origine del token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Read">
      <summary>L'utente dispone di diritti di lettura standard e del privilegio <see cref="F:System.Security.Principal.TokenAccessLevels.Query" /> per il token.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Write">
      <summary>L'utente dispone di diritti di scrittura standard e dei privilegi <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges,F:System.Security.Principal.TokenAccessLevels.AdjustGroups" /> e <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustDefault" /> per il token.</summary>
    </member>
    <member name="T:System.Security.Principal.WellKnownSidType">
      <summary>Definisce un insieme di SID comunemente utilizzati.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid">
      <summary>Indica un SID che corrisponde al gruppo degli amministratori degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid">
      <summary>Indica un SID che corrisponde al gruppo degli amministratori dei certificati.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountComputersSid">
      <summary>Indica un SID che corrisponde al gruppo di computer degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountControllersSid">
      <summary>Indica un SID che corrisponde al gruppo di controller degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid">
      <summary>Indica un SID che corrisponde al gruppo degli amministratori del dominio degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid">
      <summary>Indica un SID che corrisponde al gruppo dei guest del dominio degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid">
      <summary>Indica un SID che corrisponde al gruppo degli utenti del dominio degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid">
      <summary>Indica un SID che corrisponde al gruppo degli amministratori dell'organizzazione.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountGuestSid">
      <summary>Indica un SID che corrisponde al gruppo dei guest degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid">
      <summary>Indica un SID che corrisponde al gruppo di destinazione Kerberos degli account.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid">
      <summary>Indica un SID che corrisponde al gruppo degli amministratori dei criteri.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid">
      <summary>Indica un SID che corrisponde all'account server RAS e IAS.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid">
      <summary>Indica un SID che corrisponde al gruppo degli amministratori dello schema.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AnonymousSid">
      <summary>Indica un SID per l'account anonimo.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AuthenticatedUserSid">
      <summary>Indica un SID per un utente autenticato.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BatchSid">
      <summary>Indica un SID per un processo batch.Questo SID viene aggiunto al processo di un token quando si connette come processo per batch.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAccountOperatorsSid">
      <summary>Indica un SID che corrisponde all'account Account Operators.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAdministratorsSid">
      <summary>Indica un SID che corrisponde all'account dell'amministratore.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAuthorizationAccessSid">
      <summary>Indica un SID che corrisponde al gruppo di accesso autorizzazione Windows.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinBackupOperatorsSid">
      <summary>Indica un SID che corrisponde al gruppo Backup Operators.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinDomainSid">
      <summary>Indica un SID che corrisponde all'account di dominio.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinGuestsSid">
      <summary>Indica un SID che corrisponde all'account guest.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinIncomingForestTrustBuildersSid">
      <summary>Indica un SID che consente all'utente di creare trust tra insiemi di strutture in ingresso.Viene aggiunto al token degli utenti membri del gruppo predefinito Incoming Forest Trust Builders nel dominio radice dell'insieme di strutture.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinNetworkConfigurationOperatorsSid">
      <summary>Indica un SID che corrisponde al gruppo degli operatori di rete.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceLoggingUsersSid">
      <summary>Indica un SID che corrisponde al gruppo degli utenti ai quali è consentito l'accesso remoto per monitorare il computer.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceMonitoringUsersSid">
      <summary>Indica un SID che corrisponde al gruppo degli utenti ai quali è consentito l'accesso remoto per pianificare la registrazione dei contatori di prestazioni sul computer.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPowerUsersSid">
      <summary>Indica un SID che corrisponde al gruppo Power Users.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPreWindows2000CompatibleAccessSid">
      <summary>Indica un SID che corrisponde agli account compatibili con versioni precedenti a Windows 2000.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPrintOperatorsSid">
      <summary>Indica un SID che corrisponde al gruppo Print Operators.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinRemoteDesktopUsersSid">
      <summary>Indica un SID che corrisponde al gruppo Utenti desktop remoto.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinReplicatorSid">
      <summary>Indica un SID che corrisponde all'account Replicator.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinSystemOperatorsSid">
      <summary>Indica un SID che corrisponde al gruppo System Operators.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinUsersSid">
      <summary>Indica un SID che corrisponde agli account utente predefiniti.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupServerSid">
      <summary>Indica un SID server Gruppo creatore.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupSid">
      <summary>Indica un SID che corrisponde al Gruppo creatore di un oggetto.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerServerSid">
      <summary>Indica un SID server Creator Owner.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerSid">
      <summary>Indica un SID che corrisponde al proprietario o al creatore di un oggetto.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DialupSid">
      <summary>Indica un SID per un account di accesso remoto.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DigestAuthenticationSid">
      <summary>Indica un SID presente quando il pacchetto di autenticazione digest Microsoft ha autenticato il client.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.EnterpriseControllersSid">
      <summary>Indica un SID per un controller dell'organizzazione.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.InteractiveSid">
      <summary>Indica un SID per un account interattivo.Questo SID viene aggiunto al processo di un token quando si connette in modo interattivo.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalServiceSid">
      <summary>Indica un SID che corrisponde a un servizio locale.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSid">
      <summary>Indica un SID locale.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSystemSid">
      <summary>Indica un SID che corrisponde al sistema locale.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LogonIdsSid">
      <summary>Indica un SID che corrisponde agli ID di accesso.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.MaxDefined">
      <summary>Indica il SID massimo definito nell'enumerazione <see cref="T:System.Security.Principal.WellKnownSidType" />.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkServiceSid">
      <summary>Indica un SID che corrisponde a un servizio di rete.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkSid">
      <summary>Indica un SID per un account di rete.Questo SID viene aggiunto al processo di un token quando si connette tramite una rete.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NTAuthoritySid">
      <summary>Indica un SID per l'autorità di Windows NT.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NtlmAuthenticationSid">
      <summary>Indica un SID presente quando il pacchetto di autenticazione NTLM Microsoft ha autenticato il client.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NullSid">
      <summary>Indica un SID null.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid">
      <summary>Indica un SID presente quando l'utente è stato autenticato in un insieme di strutture con l'opzione di autenticazione selettiva attivata.Se questo SID è presente, <see cref="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid" /> non può essere presente.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ProxySid">
      <summary>Indica un SID proxy.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RemoteLogonIdSid">
      <summary>Indica un SID che corrisponde ad accessi remoti.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RestrictedCodeSid">
      <summary>Indica un SID per il codice con restrizioni.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SChannelAuthenticationSid">
      <summary>Indica un SID presente quando il pacchetto di autenticazione a canale protetto (SSL/TLS) ha autenticato il client.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SelfSid">
      <summary>Indica un SID per l'oggetto corrente.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ServiceSid">
      <summary>Indica un SID per un servizio.Questo SID viene aggiunto al processo di un token quando si connette come servizio.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.TerminalServerSid">
      <summary>Indica un SID che corrisponde a un account di Terminal Server.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid">
      <summary>Indica un SID presente quando l'utente è stato autenticato dall'interno dell'insieme di strutture o tramite un trust in cui l'opzione di autenticazione selettiva non è attivata.Se questo SID è presente, <see cref="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid" /> non può essere presente.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WinBuiltinTerminalServerLicenseServersSid">
      <summary>Indica un SID presente in un server che può rilasciare licenze di Terminal Server.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WorldSid">
      <summary>Indica un SID che corrisponde a tutti.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsBuiltInRole">
      <summary>Specifica i ruoli comuni da utilizzare con <see cref="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.String)" />.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.AccountOperator">
      <summary>Gli operatori di account gestiscono gli account utente su un computer o dominio.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Administrator">
      <summary>Gli utenti del gruppo Administrators hanno accesso completo e senza restrizioni al computer o dominio.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.BackupOperator">
      <summary>Al gruppo Backup operators è consentito ignorare le restrizioni di sicurezza per l'esecuzione delle sole operazioni di backup e ripristino di file.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Guest">
      <summary>Il gruppo Guests è vincolato da maggiori restrizioni rispetto al gruppo Users.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PowerUser">
      <summary>Gli utenti esperti dispongono di quasi tutte le autorizzazioni amministrative con alcune restrizioni eGli utenti del gruppo Power users possono perciò eseguire applicazioni legacy, oltre ad applicazioni certificate.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PrintOperator">
      <summary>Gli operatori di stampa possono controllare una stampante.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Replicator">
      <summary>Il gruppo Replicators supporta la replica di file in un dominio.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.SystemOperator">
      <summary>Il gruppo System operators gestisce un computer specifico.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.User">
      <summary>Gli utenti possono eseguire le applicazioni certificate, ma non quelle legacy,Gli utenti di questo gruppo, pertanto, possono eseguire applicazioni certificate, ma non la maggior parte delle applicazioni legacy.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsIdentity">
      <summary>Rappresenta un utente Windows.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.WindowsIdentity" /> per l'utente rappresentato dal token di account Windows specificato.</summary>
      <param name="userToken">Token di account dell'utente per conto del quale è in esecuzione il codice. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr,System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.WindowsIdentity" /> per l'utente rappresentato dal token di account Windows e dal tipo di autenticazione specificati.</summary>
      <param name="userToken">Token di account dell'utente per conto del quale è in esecuzione il codice. </param>
      <param name="type">(Uso puramente informativo). Tipo di autenticazione usata per identificare l'utente.Per altre informazioni, vedere la sezione Note.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.AccessToken">
      <summary>[SecurityCritical] Ottiene l'oggetto <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> per questa istanza di <see cref="T:System.Security.Principal.WindowsIdentity" />. </summary>
      <returns>Restituisce un valore <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose">
      <summary>Rilascia tutte le risorse usate dall'oggetto <see cref="T:System.Security.Principal.WindowsIdentity" />. </summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite usate da <see cref="T:System.Security.Principal.WindowsIdentity" /> e, facoltativamente, le risorse gestite. </summary>
      <param name="disposing">true per rilasciare sia le risorse gestite sia quelle non gestite; false per rilasciare solo le risorse non gestite. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetAnonymous">
      <summary>Restituisce un oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> che è possibile usare nel codice come valore di sentinel per rappresentare un utente anonimo.Il valore della proprietà non rappresenta l'identità anonima predefinita usata dal sistema operativo Windows.</summary>
      <returns>Oggetto che rappresenta un utente anonimo.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent">
      <summary>Restituisce un oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> che rappresenta l'utente Windows corrente.</summary>
      <returns>Oggetto che rappresenta l'utente corrente.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Boolean)">
      <summary>Restituisce un oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> che rappresenta l'identità Windows del thread o del processo, a seconda del valore del parametro <paramref name="ifImpersonating" />.</summary>
      <returns>Oggetto che rappresenta un utente Windows.</returns>
      <param name="ifImpersonating">true per la restituzione dell'oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> solo se il thread è attualmente una rappresentazione; false per la restituzione dell'oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> del thread se è una rappresentazione o dell'oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> del processo se il thread non è attualmente una rappresentazione.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Security.Principal.TokenAccessLevels)">
      <summary>Restituisce un oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> che rappresenta l'utente Windows corrente, usando il livello di accesso del token specificato.</summary>
      <returns>Oggetto che rappresenta l'utente corrente.</returns>
      <param name="desiredAccess">Combinazione bit per bit dei valori di enumerazione. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Groups">
      <summary>Ottiene i gruppi ai quali appartiene l'utente Windows corrente.</summary>
      <returns>Oggetto che rappresenta i gruppi ai quali appartiene l'utente Windows corrente.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.ImpersonationLevel">
      <summary>Ottiene il livello di rappresentazione dell'utente.</summary>
      <returns>Uno dei valori di enumerazione che specifica il livello di rappresentazione. </returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsAnonymous">
      <summary>Ottiene un valore che indica se l'account utente è identificato dal sistema come account anonimo.</summary>
      <returns>true se l'account utente è un account anonimo; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsGuest">
      <summary>Ottiene un valore che indica se l'account utente è identificato dal sistema come account <see cref="F:System.Security.Principal.WindowsAccountType.Guest" />.</summary>
      <returns>true se l'account utente è un account <see cref="F:System.Security.Principal.WindowsAccountType.Guest" />; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsSystem">
      <summary>Ottiene un valore che indica se l'account utente è identificato dal sistema come account <see cref="F:System.Security.Principal.WindowsAccountType.System" />.</summary>
      <returns>true se l'account utente è un account <see cref="F:System.Security.Principal.WindowsAccountType.System" />; in caso contrario, false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Owner">
      <summary>Ottiene l'ID di sicurezza (SID) del proprietario del token.</summary>
      <returns>Oggetto del proprietario del token.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)">
      <summary>Esegue l'azione specificata come identità Windows rappresentata.Anziché usare una chiamata al metodo rappresentato ed eseguire la funzione in <see cref="T:System.Security.Principal.WindowsImpersonationContext" />, è possibile usare <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> e fornire la funzione direttamente come parametro.</summary>
      <param name="safeAccessTokenHandle">Oggetto SafeAccessTokenHandle dell'identità Windows rappresentata.</param>
      <param name="action">System.Action da eseguire. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated``1(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Func{``0})">
      <summary>Esegue la funzione specificata come identità Windows rappresentata.Anziché usare una chiamata al metodo rappresentato ed eseguire la funzione in <see cref="T:System.Security.Principal.WindowsImpersonationContext" />, è possibile usare <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> e fornire la funzione direttamente come parametro.</summary>
      <returns>Restituisce il risultato della funzione.</returns>
      <param name="safeAccessTokenHandle">Oggetto SafeAccessTokenHandle dell'identità Windows rappresentata.</param>
      <param name="func">System.Func da eseguire.</param>
      <typeparam name="T">Tipo di oggetto usato e restituito dalla funzione.</typeparam>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.User">
      <summary>Ottiene l'ID di sicurezza (SID) dell'utente.</summary>
      <returns>Oggetto dell'utente.</returns>
    </member>
    <member name="T:System.Security.Principal.WindowsPrincipal">
      <summary>Consente al codice di verificare se un utente Windows appartiene a un gruppo Windows.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.#ctor(System.Security.Principal.WindowsIdentity)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Security.Principal.WindowsPrincipal" /> utilizzando l'oggetto <see cref="T:System.Security.Principal.WindowsIdentity" /> specificato.</summary>
      <param name="ntIdentity">Oggetto da cui costruire la nuova istanza di <see cref="T:System.Security.Principal.WindowsPrincipal" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="ntIdentity" /> è null. </exception>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Int32)">
      <summary>Determina se l'oggetto Principal corrente appartiene al gruppo di utenti Windows con l'identificatore relativo (RID) specificato.</summary>
      <returns>true se l'entità corrente è un membro del gruppo di utenti Windows specificato, ossia, è assegnato a un ruolo specifico; in caso contrario, false.</returns>
      <param name="rid">RID del gruppo di utenti Windows nel quale controllare lo stato di appartenenza dell'oggetto Principal. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.SecurityIdentifier)">
      <summary>Determina se l'entità corrente appartiene al gruppo di utenti Windows con l'identificatore di sicurezza (SID) specificato.</summary>
      <returns>true se l'oggetto Principal corrente è un membro del gruppo di utenti Windows specificato; in caso contrario, false.</returns>
      <param name="sid">Oggetto <see cref="T:System.Security.Principal.SecurityIdentifier" /> che identifica in modo univoco un gruppo di utenti Windows.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="sid" /> è null.</exception>
      <exception cref="T:System.Security.SecurityException">Windows ha restituito un errore Win32.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.WindowsBuiltInRole)">
      <summary>Determina se l'entità corrente appartiene al gruppo di utenti Windows con il nome specificato <see cref="T:System.Security.Principal.WindowsBuiltInRole" />.</summary>
      <returns>true se l'oggetto Principal corrente è un membro del gruppo di utenti Windows specificato; in caso contrario, false.</returns>
      <param name="role">Uno dei valori di <see cref="T:System.Security.Principal.WindowsBuiltInRole" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="role" /> non è un valore <see cref="T:System.Security.Principal.WindowsBuiltInRole" /> valido.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
  </members>
</doc>