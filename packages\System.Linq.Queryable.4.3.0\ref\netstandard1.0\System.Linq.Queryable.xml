﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Queryable</name>
  </assembly>
  <members>
    <member name="T:System.Linq.EnumerableExecutor">
      <summary>Represents an expression tree and provides functionality to execute the expression tree after rewriting it.</summary>
    </member>
    <member name="M:System.Linq.EnumerableExecutor.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Linq.EnumerableExecutor" /> class.</summary>
    </member>
    <member name="T:System.Linq.EnumerableExecutor`1">
      <summary>Represents an expression tree and provides functionality to execute the expression tree after rewriting it.</summary>
      <typeparam name="T">The data type of the value that results from executing the expression tree.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableExecutor`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>Initializes a new instance of the <see cref="T:System.Linq.EnumerableExecutor`1" /> class.</summary>
      <param name="expression">An expression tree to associate with the new instance.</param>
    </member>
    <member name="T:System.Linq.EnumerableQuery">
      <summary>Represents an <see cref="T:System.Collections.IEnumerable" /> as an <see cref="T:System.Linq.EnumerableQuery" /> data source. </summary>
    </member>
    <member name="M:System.Linq.EnumerableQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Linq.EnumerableQuery" /> class.</summary>
    </member>
    <member name="T:System.Linq.EnumerableQuery`1">
      <summary>Represents an <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection as an <see cref="T:System.Linq.IQueryable`1" /> data source.</summary>
      <typeparam name="T">The type of the data in the collection.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initializes a new instance of the <see cref="T:System.Linq.EnumerableQuery`1" /> class and associates it with an <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection.</summary>
      <param name="enumerable">A collection to associate with the new instance.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>Initializes a new instance of the <see cref="T:System.Linq.EnumerableQuery`1" /> class and associates the instance with an expression tree.</summary>
      <param name="expression">An expression tree to associate with the new instance.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Returns an enumerator that can iterate through the associated <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection, or, if it is null, through the collection that results from rewriting the associated expression tree as a query on an <see cref="T:System.Collections.Generic.IEnumerable`1" /> data source and executing it.</summary>
      <returns>An enumerator that can be used to iterate through the associated data source.</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an enumerator that can iterate through the associated <see cref="T:System.Collections.Generic.IEnumerable`1" /> collection, or, if it is null, through the collection that results from rewriting the associated expression tree as a query on an <see cref="T:System.Collections.Generic.IEnumerable`1" /> data source and executing it.</summary>
      <returns>An enumerator that can be used to iterate through the associated data source.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#ElementType">
      <summary>Gets the type of the data in the collection that this instance represents.</summary>
      <returns>The type of the data in the collection that this instance represents.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Expression">
      <summary>Gets the expression tree that is associated with or that represents this instance.</summary>
      <returns>The expression tree that is associated with or that represents this instance.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Provider">
      <summary>Gets the query provider that is associated with this instance.</summary>
      <returns>The query provider that is associated with this instance.</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>Constructs a new <see cref="T:System.Linq.EnumerableQuery`1" /> object and associates it with a specified expression tree that represents an <see cref="T:System.Linq.IQueryable`1" /> collection of data.</summary>
      <returns>An EnumerableQuery object that is associated with <paramref name="expression" />.</returns>
      <param name="expression">An expression tree to execute.</param>
      <typeparam name="S">The type of the data in the collection that <paramref name="expression" /> represents.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery(System.Linq.Expressions.Expression)">
      <summary>Constructs a new <see cref="T:System.Linq.EnumerableQuery`1" /> object and associates it with a specified expression tree that represents an <see cref="T:System.Linq.IQueryable" /> collection of data.</summary>
      <returns>An <see cref="T:System.Linq.EnumerableQuery`1" /> object that is associated with <paramref name="expression" />.</returns>
      <param name="expression">An expression tree that represents an <see cref="T:System.Linq.IQueryable" /> collection of data.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute``1(System.Linq.Expressions.Expression)">
      <summary>Executes an expression after rewriting it to call <see cref="T:System.Linq.Enumerable" /> methods instead of <see cref="T:System.Linq.Queryable" /> methods on any enumerable data sources that cannot be queried by <see cref="T:System.Linq.Queryable" /> methods.</summary>
      <returns>The value that results from executing <paramref name="expression" />.</returns>
      <param name="expression">An expression tree to execute.</param>
      <typeparam name="S">The type of the data in the collection that <paramref name="expression" /> represents.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute(System.Linq.Expressions.Expression)">
      <summary>Executes an expression after rewriting it to call <see cref="T:System.Linq.Enumerable" /> methods instead of <see cref="T:System.Linq.Queryable" /> methods on any enumerable data sources that cannot be queried by <see cref="T:System.Linq.Queryable" /> methods.</summary>
      <returns>The value that results from executing <paramref name="expression" />.</returns>
      <param name="expression">An expression tree to execute.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.ToString">
      <summary>Returns a textual representation of the enumerable collection or, if it is null, of the expression tree that is associated with this instance.</summary>
      <returns>A textual representation of the enumerable collection or, if it is null, of the expression tree that is associated with this instance.</returns>
    </member>
    <member name="T:System.Linq.Queryable">
      <summary>Provides a set of static (Shared in Visual Basic) methods for querying data structures that implement <see cref="T:System.Linq.IQueryable`1" />.</summary>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0,``0}})">
      <summary>Applies an accumulator function over a sequence.</summary>
      <returns>The final accumulator value.</returns>
      <param name="source">A sequence to aggregate over.</param>
      <param name="func">An accumulator function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``2(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}})">
      <summary>Applies an accumulator function over a sequence. The specified seed value is used as the initial accumulator value.</summary>
      <returns>The final accumulator value.</returns>
      <param name="source">A sequence to aggregate over.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">An accumulator function to invoke on each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``3(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,``2}})">
      <summary>Applies an accumulator function over a sequence. The specified seed value is used as the initial accumulator value, and the specified function is used to select the result value.</summary>
      <returns>The transformed final accumulator value.</returns>
      <param name="source">A sequence to aggregate over.</param>
      <param name="seed">The initial accumulator value.</param>
      <param name="func">An accumulator function to invoke on each element.</param>
      <param name="selector">A function to transform the final accumulator value into the result value.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">The type of the accumulator value.</typeparam>
      <typeparam name="TResult">The type of the resulting value.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="func" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.All``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Determines whether all the elements of a sequence satisfy a condition.</summary>
      <returns>true if every element of the source sequence passes the test in the specified predicate, or if the sequence is empty; otherwise, false.</returns>
      <param name="source">A sequence whose elements to test for a condition.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0})">
      <summary>Determines whether a sequence contains any elements.</summary>
      <returns>true if the source sequence contains any elements; otherwise, false.</returns>
      <param name="source">A sequence to check for being empty.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Determines whether any element of a sequence satisfies a condition.</summary>
      <returns>true if any elements in the source sequence pass the test in the specified predicate; otherwise, false.</returns>
      <param name="source">A sequence whose elements to test for a condition.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Converts a generic <see cref="T:System.Collections.Generic.IEnumerable`1" /> to a generic <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that represents the input sequence.</returns>
      <param name="source">A sequence to convert.</param>
      <typeparam name="TElement">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable(System.Collections.IEnumerable)">
      <summary>Converts an <see cref="T:System.Collections.IEnumerable" /> to an <see cref="T:System.Linq.IQueryable" />.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable" /> that represents the input sequence.</returns>
      <param name="source">A sequence to convert.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="source" /> does not implement <see cref="T:System.Collections.Generic.IEnumerable`1" /> for some <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Decimal})">
      <summary>Computes the average of a sequence of <see cref="T:System.Decimal" /> values.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of <see cref="T:System.Decimal" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Double})">
      <summary>Computes the average of a sequence of <see cref="T:System.Double" /> values.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of <see cref="T:System.Double" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int32})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int32" /> values.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of <see cref="T:System.Int32" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int64})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int64" /> values.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of <see cref="T:System.Int64" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Decimal" /> values.</summary>
      <returns>The average of the sequence of values, or null if the source sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Decimal" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Double" /> values.</summary>
      <returns>The average of the sequence of values, or null if the source sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Double" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int32" /> values.</summary>
      <returns>The average of the sequence of values, or null if the source sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Int32" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int64" /> values.</summary>
      <returns>The average of the sequence of values, or null if the source sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Int64" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Single" /> values.</summary>
      <returns>The average of the sequence of values, or null if the source sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Single" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Single})">
      <summary>Computes the average of a sequence of <see cref="T:System.Single" /> values.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of <see cref="T:System.Single" /> values to calculate the average of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>Computes the average of a sequence of <see cref="T:System.Decimal" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of values that are used to calculate an average.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>Computes the average of a sequence of <see cref="T:System.Double" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int32" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>Computes the average of a sequence of <see cref="T:System.Int64" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Decimal" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values, or null if the <paramref name="source" /> sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Double" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values, or null if the <paramref name="source" /> sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int32" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values, or null if the <paramref name="source" /> sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Int64" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values, or null if the <paramref name="source" /> sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>Computes the average of a sequence of nullable <see cref="T:System.Single" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values, or null if the <paramref name="source" /> sequence is empty or contains only null values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>Computes the average of a sequence of <see cref="T:System.Single" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The average of the sequence of values.</returns>
      <param name="source">A sequence of values to calculate the average of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> contains no elements.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Cast``1(System.Linq.IQueryable)">
      <summary>Converts the elements of an <see cref="T:System.Linq.IQueryable" /> to the specified type.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains each element of the source sequence converted to the specified type.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable" /> that contains the elements to be converted.</param>
      <typeparam name="TResult">The type to convert the elements of <paramref name="source" /> to.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidCastException">An element in the sequence cannot be cast to type <paramref name="TResult" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Concat``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Concatenates two sequences.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains the concatenated elements of the two input sequences.</returns>
      <param name="source1">The first sequence to concatenate.</param>
      <param name="source2">The sequence to concatenate to the first sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0)">
      <summary>Determines whether a sequence contains a specified element by using the default equality comparer.</summary>
      <returns>true if the input sequence contains an element that has the specified value; otherwise, false.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> in which to locate <paramref name="item" />.</param>
      <param name="item">The object to locate in the sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determines whether a sequence contains a specified element by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>true if the input sequence contains an element that has the specified value; otherwise, false.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> in which to locate <paramref name="item" />.</param>
      <param name="item">The object to locate in the sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0})">
      <summary>Returns the number of elements in a sequence.</summary>
      <returns>The number of elements in the input sequence.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable`1" /> that contains the elements to be counted.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The number of elements in <paramref name="source" /> is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns the number of elements in the specified sequence that satisfies a condition.</summary>
      <returns>The number of elements in the sequence that satisfies the condition in the predicate function.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> that contains the elements to be counted.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
      <exception cref="T:System.OverflowException">The number of elements in <paramref name="source" /> is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0})">
      <summary>Returns the elements of the specified sequence or the type parameter's default value in a singleton collection if the sequence is empty.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains default(<paramref name="TSource" />) if <paramref name="source" /> is empty; otherwise, <paramref name="source" />.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable`1" /> to return a default value for if empty.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0},``0)">
      <summary>Returns the elements of the specified sequence or the specified value in a singleton collection if the sequence is empty.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains <paramref name="defaultValue" /> if <paramref name="source" /> is empty; otherwise, <paramref name="source" />.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable`1" /> to return the specified value for if empty.</param>
      <param name="defaultValue">The value to return if the sequence is empty.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0})">
      <summary>Returns distinct elements from a sequence by using the default equality comparer to compare values.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains distinct elements from <paramref name="source" />.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable`1" /> to remove duplicates from.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Returns distinct elements from a sequence by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains distinct elements from <paramref name="source" />.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable`1" /> to remove duplicates from.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAt``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Returns the element at a specified index in a sequence.</summary>
      <returns>The element at the specified position in <paramref name="source" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return an element from.</param>
      <param name="index">The zero-based index of the element to retrieve.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAtOrDefault``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Returns the element at a specified index in a sequence or a default value if the index is out of range.</summary>
      <returns>default(<paramref name="TSource" />) if <paramref name="index" /> is outside the bounds of <paramref name="source" />; otherwise, the element at the specified position in <paramref name="source" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return an element from.</param>
      <param name="index">The zero-based index of the element to retrieve.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Produces the set difference of two sequences by using the default equality comparer to compare values.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains the set difference of the two sequences.</returns>
      <param name="source1">An <see cref="T:System.Linq.IQueryable`1" /> whose elements that are not also in <paramref name="source2" /> will be returned.</param>
      <param name="source2">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements that also occur in the first sequence will not appear in the returned sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set difference of two sequences by using the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains the set difference of the two sequences.</returns>
      <param name="source1">An <see cref="T:System.Linq.IQueryable`1" /> whose elements that are not also in <paramref name="source2" /> will be returned.</param>
      <param name="source2">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements that also occur in the first sequence will not appear in the returned sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0})">
      <summary>Returns the first element of a sequence.</summary>
      <returns>The first element in <paramref name="source" />.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable`1" /> to return the first element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns the first element of a sequence that satisfies a specified condition.</summary>
      <returns>The first element in <paramref name="source" /> that passes the test in <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">No element satisfies the condition in <paramref name="predicate" />.-or-The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Returns the first element of a sequence, or a default value if the sequence contains no elements.</summary>
      <returns>default(<paramref name="TSource" />) if <paramref name="source" /> is empty; otherwise, the first element in <paramref name="source" />.</returns>
      <param name="source">The <see cref="T:System.Linq.IQueryable`1" /> to return the first element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns the first element of a sequence that satisfies a specified condition or a default value if no such element is found.</summary>
      <returns>default(<paramref name="TSource" />) if <paramref name="source" /> is empty or if no element passes the test specified by <paramref name="predicate" />; otherwise, the first element in <paramref name="source" /> that passes the test specified by <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Groups the elements of a sequence according to a specified key selector function.</summary>
      <returns>An IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt; in C# or IQueryable(Of IGrouping(Of TKey, TSource)) in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> object contains a sequence of objects and a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function and compares the keys by using a specified comparer.</summary>
      <returns>An IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt; in C# or IQueryable(Of IGrouping(Of TKey, TSource)) in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> contains a sequence of objects and a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}})">
      <summary>Groups the elements of a sequence according to a specified key selector function and projects the elements for each group by using a specified function.</summary>
      <returns>An IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt; in C# or IQueryable(Of IGrouping(Of TKey, TElement)) in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> contains a sequence of objects of type <paramref name="TElement" /> and a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence and projects the elements for each group by using a specified function. Key values are compared by using a specified comparer.</summary>
      <returns>An IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt; in C# or IQueryable(Of IGrouping(Of TKey, TElement)) in Visual Basic where each <see cref="T:System.Linq.IGrouping`2" /> contains a sequence of objects of type <paramref name="TElement" /> and a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. The elements of each group are projected by using a specified function.</summary>
      <returns>An T:System.Linq.IQueryable`1 that has a type argument of <paramref name="TResult" /> and where each element represents a projection over a group and its key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. Keys are compared by using a specified comparer and the elements of each group are projected by using a specified function.</summary>
      <returns>An T:System.Linq.IQueryable`1 that has a type argument of <paramref name="TResult" /> and where each element represents a projection over a group and its key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="elementSelector">A function to map each source element to an element in an <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">The type of the elements in each <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="elementSelector" /> or <paramref name="resultSelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key.</summary>
      <returns>An T:System.Linq.IQueryable`1 that has a type argument of <paramref name="TResult" /> and where each element represents a projection over a group and its key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Groups the elements of a sequence according to a specified key selector function and creates a result value from each group and its key. Keys are compared by using a specified comparer.</summary>
      <returns>An T:System.Linq.IQueryable`1 that has a type argument of <paramref name="TResult" /> and where each element represents a projection over a group and its key.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to group.</param>
      <param name="keySelector">A function to extract the key for each element.</param>
      <param name="resultSelector">A function to create a result value from each group.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented in <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">The type of the result value returned by <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="resultSelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}})">
      <summary>Correlates the elements of two sequences based on key equality and groups the results. The default equality comparer is used to compare keys.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements of type <paramref name="TResult" /> obtained by performing a grouped join on two sequences.</returns>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Correlates the elements of two sequences based on key equality and groups the results. A specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> is used to compare keys.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements of type <paramref name="TResult" /> obtained by performing a grouped join on two sequences.</returns>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from an element from the first sequence and a collection of matching elements from the second sequence.</param>
      <param name="comparer">A comparer to hash and compare keys.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Produces the set intersection of two sequences by using the default equality comparer to compare values.</summary>
      <returns>A sequence that contains the set intersection of the two sequences.</returns>
      <param name="source1">A sequence whose distinct elements that also appear in <paramref name="source2" /> are returned.</param>
      <param name="source2">A sequence whose distinct elements that also appear in the first sequence are returned.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set intersection of two sequences by using the specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains the set intersection of the two sequences.</returns>
      <param name="source1">An <see cref="T:System.Linq.IQueryable`1" /> whose distinct elements that also appear in <paramref name="source2" /> are returned.</param>
      <param name="source2">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose distinct elements that also appear in the first sequence are returned.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}})">
      <summary>Correlates the elements of two sequences based on matching keys. The default equality comparer is used to compare keys.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that has elements of type <paramref name="TResult" /> obtained by performing an inner join on two sequences.</returns>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from two matching elements.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Correlates the elements of two sequences based on matching keys. A specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> is used to compare keys.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that has elements of type <paramref name="TResult" /> obtained by performing an inner join on two sequences.</returns>
      <param name="outer">The first sequence to join.</param>
      <param name="inner">The sequence to join to the first sequence.</param>
      <param name="outerKeySelector">A function to extract the join key from each element of the first sequence.</param>
      <param name="innerKeySelector">A function to extract the join key from each element of the second sequence.</param>
      <param name="resultSelector">A function to create a result element from two matching elements.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to hash and compare keys.</param>
      <typeparam name="TOuter">The type of the elements of the first sequence.</typeparam>
      <typeparam name="TInner">The type of the elements of the second sequence.</typeparam>
      <typeparam name="TKey">The type of the keys returned by the key selector functions.</typeparam>
      <typeparam name="TResult">The type of the result elements.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="outer" /> or <paramref name="inner" /> or <paramref name="outerKeySelector" /> or <paramref name="innerKeySelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0})">
      <summary>Returns the last element in a sequence.</summary>
      <returns>The value at the last position in <paramref name="source" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return the last element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns the last element of a sequence that satisfies a specified condition.</summary>
      <returns>The last element in <paramref name="source" /> that passes the test specified by <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">No element satisfies the condition in <paramref name="predicate" />.-or-The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Returns the last element in a sequence, or a default value if the sequence contains no elements.</summary>
      <returns>default(<paramref name="TSource" />) if <paramref name="source" /> is empty; otherwise, the last element in <paramref name="source" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return the last element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns the last element of a sequence that satisfies a condition or a default value if no such element is found.</summary>
      <returns>default(<paramref name="TSource" />) if <paramref name="source" /> is empty or if no elements pass the test in the predicate function; otherwise, the last element of <paramref name="source" /> that passes the test in the predicate function.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return an element from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0})">
      <summary>Returns an <see cref="T:System.Int64" /> that represents the total number of elements in a sequence.</summary>
      <returns>The number of elements in <paramref name="source" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> that contains the elements to be counted.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The number of elements exceeds <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns an <see cref="T:System.Int64" /> that represents the number of elements in a sequence that satisfy a condition.</summary>
      <returns>The number of elements in <paramref name="source" /> that satisfy the condition in the predicate function.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> that contains the elements to be counted.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
      <exception cref="T:System.OverflowException">The number of matching elements exceeds <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``1(System.Linq.IQueryable{``0})">
      <summary>Returns the maximum value in a generic <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <returns>The maximum value in the sequence.</returns>
      <param name="source">A sequence of values to determine the maximum of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Invokes a projection function on each element of a generic <see cref="T:System.Linq.IQueryable`1" /> and returns the maximum resulting value.</summary>
      <returns>The maximum value in the sequence.</returns>
      <param name="source">A sequence of values to determine the maximum of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by the function represented by <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``1(System.Linq.IQueryable{``0})">
      <summary>Returns the minimum value of a generic <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <returns>The minimum value in the sequence.</returns>
      <param name="source">A sequence of values to determine the minimum of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Invokes a projection function on each element of a generic <see cref="T:System.Linq.IQueryable`1" /> and returns the minimum resulting value.</summary>
      <returns>The minimum value in the sequence.</returns>
      <param name="source">A sequence of values to determine the minimum of.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by the function represented by <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OfType``1(System.Linq.IQueryable)">
      <summary>Filters the elements of an <see cref="T:System.Linq.IQueryable" /> based on a specified type.</summary>
      <returns>A collection that contains the elements from <paramref name="source" /> that have type <paramref name="TResult" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable" /> whose elements to filter.</param>
      <typeparam name="TResult">The type to filter the elements of the sequence on.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Sorts the elements of a sequence in ascending order according to a key.</summary>
      <returns>An <see cref="T:System.Linq.IOrderedQueryable`1" /> whose elements are sorted according to a key.</returns>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function that is represented by <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Sorts the elements of a sequence in ascending order by using a specified comparer.</summary>
      <returns>An <see cref="T:System.Linq.IOrderedQueryable`1" /> whose elements are sorted according to a key.</returns>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function that is represented by <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Sorts the elements of a sequence in descending order according to a key.</summary>
      <returns>An <see cref="T:System.Linq.IOrderedQueryable`1" /> whose elements are sorted in descending order according to a key.</returns>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function that is represented by <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Sorts the elements of a sequence in descending order by using a specified comparer.</summary>
      <returns>An <see cref="T:System.Linq.IOrderedQueryable`1" /> whose elements are sorted in descending order according to a key.</returns>
      <param name="source">A sequence of values to order.</param>
      <param name="keySelector">A function to extract a key from an element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function that is represented by <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Reverse``1(System.Linq.IQueryable{``0})">
      <summary>Inverts the order of the elements in a sequence.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> whose elements correspond to those of the input sequence in reverse order.</returns>
      <param name="source">A sequence of values to reverse.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Projects each element of a sequence into a new form.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> whose elements are the result of invoking a projection function on each element of <paramref name="source" />.</returns>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by the function represented by <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,``1}})">
      <summary>Projects each element of a sequence into a new form by incorporating the element's index.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> whose elements are the result of invoking a projection function on each element of <paramref name="source" />.</returns>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the value returned by the function represented by <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" /> and invokes a result selector function on each element therein. The resulting values from each intermediate sequence are combined into a single, one-dimensional sequence and returned.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> whose elements are the result of invoking the one-to-many projection function <paramref name="collectionSelector" /> on each element of <paramref name="source" /> and then mapping each of those sequence elements and their corresponding <paramref name="source" /> element to a result element.</returns>
      <param name="source">A sequence of values to project.</param>
      <param name="collectionSelector">A projection function to apply to each element of the input sequence.</param>
      <param name="resultSelector">A projection function to apply to each element of each intermediate sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">The type of the intermediate elements collected by the function represented by <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the resulting sequence.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="collectionSelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" /> and combines the resulting sequences into one sequence.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> whose elements are the result of invoking a one-to-many projection function on each element of the input sequence.</returns>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the sequence returned by the function represented by <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" /> that incorporates the index of the source element that produced it. A result selector function is invoked on each element of each intermediate sequence, and the resulting values are combined into a single, one-dimensional sequence and returned.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> whose elements are the result of invoking the one-to-many projection function <paramref name="collectionSelector" /> on each element of <paramref name="source" /> and then mapping each of those sequence elements and their corresponding <paramref name="source" /> element to a result element.</returns>
      <param name="source">A sequence of values to project.</param>
      <param name="collectionSelector">A projection function to apply to each element of the input sequence; the second parameter of this function represents the index of the source element.</param>
      <param name="resultSelector">A projection function to apply to each element of each intermediate sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">The type of the intermediate elements collected by the function represented by <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the resulting sequence.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="collectionSelector" /> or <paramref name="resultSelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>Projects each element of a sequence to an <see cref="T:System.Collections.Generic.IEnumerable`1" /> and combines the resulting sequences into one sequence. The index of each source element is used in the projected form of that element.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> whose elements are the result of invoking a one-to-many projection function on each element of the input sequence.</returns>
      <param name="source">A sequence of values to project.</param>
      <param name="selector">A projection function to apply to each element; the second parameter of this function represents the index of the source element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TResult">The type of the elements of the sequence returned by the function represented by <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Determines whether two sequences are equal by using the default equality comparer to compare elements.</summary>
      <returns>true if the two source sequences are of equal length and their corresponding elements compare equal; otherwise, false.</returns>
      <param name="source1">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to compare to those of <paramref name="source2" />.</param>
      <param name="source2">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to compare to those of the first sequence.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Determines whether two sequences are equal by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare elements.</summary>
      <returns>true if the two source sequences are of equal length and their corresponding elements compare equal; otherwise, false.</returns>
      <param name="source1">An <see cref="T:System.Linq.IQueryable`1" /> whose elements to compare to those of <paramref name="source2" />.</param>
      <param name="source2">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> whose elements to compare to those of the first sequence.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to use to compare elements.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0})">
      <summary>Returns the only element of a sequence, and throws an exception if there is not exactly one element in the sequence.</summary>
      <returns>The single element of the input sequence.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return the single element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> has more than one element.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns the only element of a sequence that satisfies a specified condition, and throws an exception if more than one such element exists.</summary>
      <returns>The single element of the input sequence that satisfies the condition in <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return a single element from.</param>
      <param name="predicate">A function to test an element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">No element satisfies the condition in <paramref name="predicate" />.-or-More than one element satisfies the condition in <paramref name="predicate" />.-or-The source sequence is empty.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Returns the only element of a sequence, or a default value if the sequence is empty; this method throws an exception if there is more than one element in the sequence.</summary>
      <returns>The single element of the input sequence, or default(<paramref name="TSource" />) if the sequence contains no elements.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return the single element of.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> has more than one element.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns the only element of a sequence that satisfies a specified condition or a default value if no such element exists; this method throws an exception if more than one element satisfies the condition.</summary>
      <returns>The single element of the input sequence that satisfies the condition in <paramref name="predicate" />, or default(<paramref name="TSource" />) if no such element is found.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return a single element from.</param>
      <param name="predicate">A function to test an element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
      <exception cref="T:System.InvalidOperationException">More than one element satisfies the condition in <paramref name="predicate" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Skip``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Bypasses a specified number of elements in a sequence and then returns the remaining elements.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements that occur after the specified index in the input sequence.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return elements from.</param>
      <param name="count">The number of elements to skip before returning the remaining elements.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Bypasses elements in a sequence as long as a specified condition is true and then returns the remaining elements.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements from <paramref name="source" /> starting at the first element in the linear series that does not pass the test specified by <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return elements from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Bypasses elements in a sequence as long as a specified condition is true and then returns the remaining elements. The element's index is used in the logic of the predicate function.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements from <paramref name="source" /> starting at the first element in the linear series that does not pass the test specified by <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to return elements from.</param>
      <param name="predicate">A function to test each element for a condition; the second parameter of this function represents the index of the source element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Decimal})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Decimal" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of <see cref="T:System.Decimal" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Double})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Double" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of <see cref="T:System.Double" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int32})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Int32" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of <see cref="T:System.Int32" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int64})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Int64" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of <see cref="T:System.Int64" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Decimal" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Decimal" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Double" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Double" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Int32" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Int32" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Int64" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Int64" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>Computes the sum of a sequence of nullable <see cref="T:System.Single" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of nullable <see cref="T:System.Single" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Single})">
      <summary>Computes the sum of a sequence of <see cref="T:System.Single" /> values.</summary>
      <returns>The sum of the values in the sequence.</returns>
      <param name="source">A sequence of <see cref="T:System.Single" /> values to calculate the sum of.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Decimal" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Double" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Int32" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Int64" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Decimal" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Double" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Int32" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Int64" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
      <exception cref="T:System.OverflowException">The sum is larger than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>Computes the sum of the sequence of nullable <see cref="T:System.Single" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>Computes the sum of the sequence of <see cref="T:System.Single" /> values that is obtained by invoking a projection function on each element of the input sequence.</summary>
      <returns>The sum of the projected values.</returns>
      <param name="source">A sequence of values of type <paramref name="TSource" />.</param>
      <param name="selector">A projection function to apply to each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="selector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Take``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Returns a specified number of contiguous elements from the start of a sequence.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains the specified number of elements from the start of <paramref name="source" />.</returns>
      <param name="source">The sequence to return elements from.</param>
      <param name="count">The number of elements to return.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Returns elements from a sequence as long as a specified condition is true.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements from the input sequence occurring before the element at which the test specified by <paramref name="predicate" /> no longer passes.</returns>
      <param name="source">The sequence to return elements from.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Returns elements from a sequence as long as a specified condition is true. The element's index is used in the logic of the predicate function.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements from the input sequence occurring before the element at which the test specified by <paramref name="predicate" /> no longer passes.</returns>
      <param name="source">The sequence to return elements from.</param>
      <param name="predicate">A function to test each element for a condition; the second parameter of the function represents the index of the element in the source sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Performs a subsequent ordering of the elements in a sequence in ascending order according to a key.</summary>
      <returns>An <see cref="T:System.Linq.IOrderedQueryable`1" /> whose elements are sorted according to a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IOrderedQueryable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented by <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Performs a subsequent ordering of the elements in a sequence in ascending order by using a specified comparer.</summary>
      <returns>An <see cref="T:System.Linq.IOrderedQueryable`1" /> whose elements are sorted according to a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IOrderedQueryable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented by <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Performs a subsequent ordering of the elements in a sequence in descending order, according to a key.</summary>
      <returns>An <see cref="T:System.Linq.IOrderedQueryable`1" /> whose elements are sorted in descending order according to a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IOrderedQueryable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key returned by the function represented by <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Performs a subsequent ordering of the elements in a sequence in descending order by using a specified comparer.</summary>
      <returns>A collection whose elements are sorted in descending order according to a key.</returns>
      <param name="source">An <see cref="T:System.Linq.IOrderedQueryable`1" /> that contains elements to sort.</param>
      <param name="keySelector">A function to extract a key from each element.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IComparer`1" /> to compare keys.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <typeparam name="TKey">The type of the key that is returned by the <paramref name="keySelector" /> function.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="keySelector" /> or <paramref name="comparer" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Produces the set union of two sequences by using the default equality comparer.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains the elements from both input sequences, excluding duplicates.</returns>
      <param name="source1">A sequence whose distinct elements form the first set for the union operation.</param>
      <param name="source2">A sequence whose distinct elements form the second set for the union operation.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Produces the set union of two sequences by using a specified <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains the elements from both input sequences, excluding duplicates.</returns>
      <param name="source1">A sequence whose distinct elements form the first set for the union operation.</param>
      <param name="source2">A sequence whose distinct elements form the second set for the union operation.</param>
      <param name="comparer">An <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> to compare values.</param>
      <typeparam name="TSource">The type of the elements of the input sequences.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" /> or <paramref name="source2" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Filters a sequence of values based on a predicate.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements from the input sequence that satisfy the condition specified by <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to filter.</param>
      <param name="predicate">A function to test each element for a condition.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Filters a sequence of values based on a predicate. Each element's index is used in the logic of the predicate function.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains elements from the input sequence that satisfy the condition specified by <paramref name="predicate" />.</returns>
      <param name="source">An <see cref="T:System.Linq.IQueryable`1" /> to filter.</param>
      <param name="predicate">A function to test each element for a condition; the second parameter of the function represents the index of the element in the source sequence.</param>
      <typeparam name="TSource">The type of the elements of <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source" /> or <paramref name="predicate" /> is null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Zip``3(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Merges two sequences by using the specified predicate function.</summary>
      <returns>An <see cref="T:System.Linq.IQueryable`1" /> that contains merged elements of two input sequences.</returns>
      <param name="source1">The first sequence to merge.</param>
      <param name="source2">The second sequence to merge.</param>
      <param name="resultSelector">A function that specifies how to merge the elements from the two sequences.</param>
      <typeparam name="TFirst">The type of the elements of the first input sequence.</typeparam>
      <typeparam name="TSecond">The type of the elements of the second input sequence.</typeparam>
      <typeparam name="TResult">The type of the elements of the result sequence.</typeparam>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="source1" />or <paramref name="source2 " />is null.</exception>
    </member>
  </members>
</doc>