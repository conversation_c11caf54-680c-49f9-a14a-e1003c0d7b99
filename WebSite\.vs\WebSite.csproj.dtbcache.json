{"RootPath": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\WebSite", "ProjectFileName": "WebSite.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Activities\\default.aspx.cs"}, {"SourceFile": "Activities\\edit.aspx.cs"}, {"SourceFile": "Activities\\ListView.ascx.cs"}, {"SourceFile": "Activities\\MyActivities.ascx.cs"}, {"SourceFile": "Activities\\SchedulingGrid.ascx.cs"}, {"SourceFile": "Activities\\view.aspx.cs"}, {"SourceFile": "Administration\\ACLRoles\\AccessView.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\ByUser.aspx.cs"}, {"SourceFile": "Administration\\ACLRoles\\default.aspx.cs"}, {"SourceFile": "Administration\\ACLRoles\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\edit.aspx.cs"}, {"SourceFile": "Administration\\ACLRoles\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\ListView.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\PopupMultiSelect.aspx.cs"}, {"SourceFile": "Administration\\ACLRoles\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\UserRolesView.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\Users.ascx.cs"}, {"SourceFile": "Administration\\ACLRoles\\view.aspx.cs"}, {"SourceFile": "Administration\\AmazonView.ascx.cs"}, {"SourceFile": "Administration\\Backups\\default.aspx.cs"}, {"SourceFile": "Administration\\Backups\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Batch\\Batch.aspx.cs"}, {"SourceFile": "Administration\\Batch\\Batch.aspx.designer.cs"}, {"SourceFile": "Administration\\Batch\\UploadBatchData.ascx.cs"}, {"SourceFile": "Administration\\Batch\\UploadBatchData.ascx.designer.cs"}, {"SourceFile": "Administration\\BugsView.ascx.cs"}, {"SourceFile": "Administration\\ConfigureSettings\\default.aspx.cs"}, {"SourceFile": "Administration\\ConfigureSettings\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ConfigureTabs\\default.aspx.cs"}, {"SourceFile": "Administration\\ConfigureTabs\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Config\\default.aspx.cs"}, {"SourceFile": "Administration\\Config\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\Config\\edit.aspx.cs"}, {"SourceFile": "Administration\\Config\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Config\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Config\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\Config\\view.aspx.cs"}, {"SourceFile": "Administration\\ContractsView.ascx.cs"}, {"SourceFile": "Administration\\Currencies\\default.aspx.cs"}, {"SourceFile": "Administration\\Currencies\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Currencies\\ListView.ascx.cs"}, {"SourceFile": "Administration\\default.aspx.cs"}, {"SourceFile": "Administration\\Dropdown\\default.aspx.cs"}, {"SourceFile": "Administration\\Dropdown\\edit.aspx.cs"}, {"SourceFile": "Administration\\Dropdown\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Dropdown\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Dropdown\\Popup.aspx.cs"}, {"SourceFile": "Administration\\Dropdown\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\DynamicButtons\\default.aspx.cs"}, {"SourceFile": "Administration\\DynamicButtons\\edit.aspx.cs"}, {"SourceFile": "Administration\\DynamicButtons\\EditView.ascx.cs"}, {"SourceFile": "Administration\\DynamicButtons\\ListView.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\default.aspx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\DetailViews\\default.aspx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\DetailViews\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\DetailViews\\NewRecord.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\EditViews\\default.aspx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\EditViews\\EditView.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\EditViews\\NewRecord.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\GridViews\\default.aspx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\GridViews\\ListView.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\GridViews\\NewRecord.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\ListView.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\_code\\DynamicLayoutView.cs"}, {"SourceFile": "Administration\\DynamicLayout\\_code\\NewRecord.cs"}, {"SourceFile": "Administration\\DynamicLayout\\_controls\\LayoutButtons.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\_controls\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\EditCustomFields\\default.aspx.cs"}, {"SourceFile": "Administration\\EditCustomFields\\edit.aspx.cs"}, {"SourceFile": "Administration\\EditCustomFields\\EditView.ascx.cs"}, {"SourceFile": "Administration\\EditCustomFields\\ListView.ascx.cs"}, {"SourceFile": "Administration\\EditCustomFields\\NewRecord.ascx.cs"}, {"SourceFile": "Administration\\EditCustomFields\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\EmailMan\\config.aspx.cs"}, {"SourceFile": "Administration\\EmailMan\\ConfigView.ascx.cs"}, {"SourceFile": "Administration\\EmailMan\\default.aspx.cs"}, {"SourceFile": "Administration\\EmailMan\\edit.aspx.cs"}, {"SourceFile": "Administration\\EmailMan\\EditView.ascx.cs"}, {"SourceFile": "Administration\\EmailMan\\ListView.ascx.cs"}, {"SourceFile": "Administration\\EmailMan\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\EmailMan\\Preview.aspx.cs"}, {"SourceFile": "Administration\\EmailMan\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\EmailsView.ascx.cs"}, {"SourceFile": "Administration\\ExConfig\\DictType.aspx.cs"}, {"SourceFile": "Administration\\ExConfig\\Default.aspx.cs"}, {"SourceFile": "Administration\\ExConfig\\Edit.aspx.cs"}, {"SourceFile": "Administration\\ExConfig\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ExConfig\\DictType.ascx.cs"}, {"SourceFile": "Administration\\ExConfig\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Export\\default.aspx.cs"}, {"SourceFile": "Administration\\Export\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Export\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\ForumsView.ascx.cs"}, {"SourceFile": "Administration\\ForumTopics\\default.aspx.cs"}, {"SourceFile": "Administration\\ForumTopics\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\ForumTopics\\edit.aspx.cs"}, {"SourceFile": "Administration\\ForumTopics\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ForumTopics\\ListView.ascx.cs"}, {"SourceFile": "Administration\\ForumTopics\\view.aspx.cs"}, {"SourceFile": "Administration\\Import\\default.aspx.cs"}, {"SourceFile": "Administration\\Import\\ListView.ascx.cs"}, {"SourceFile": "Administration\\InboundEmail\\default.aspx.cs"}, {"SourceFile": "Administration\\InboundEmail\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\InboundEmail\\edit.aspx.cs"}, {"SourceFile": "Administration\\InboundEmail\\EditView.ascx.cs"}, {"SourceFile": "Administration\\InboundEmail\\ListView.ascx.cs"}, {"SourceFile": "Administration\\InboundEmail\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\InboundEmail\\Mailbox.ascx.cs"}, {"SourceFile": "Administration\\InboundEmail\\view.aspx.cs"}, {"SourceFile": "Administration\\Languages\\edit.aspx.cs"}, {"SourceFile": "Administration\\Languages\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Languages\\default.aspx.cs"}, {"SourceFile": "Administration\\Languages\\ListView.ascx.cs"}, {"SourceFile": "Administration\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Manufacturers\\default.aspx.cs"}, {"SourceFile": "Administration\\Manufacturers\\edit.aspx.cs"}, {"SourceFile": "Administration\\Manufacturers\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Manufacturers\\ListView.ascx.cs"}, {"SourceFile": "Administration\\NetworkView.ascx.cs"}, {"SourceFile": "Administration\\PaymentGateway\\default.aspx.cs"}, {"SourceFile": "Administration\\PaymentGateway\\edit.aspx.cs"}, {"SourceFile": "Administration\\PaymentGateway\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ProductCategories\\default.aspx.cs"}, {"SourceFile": "Administration\\ProductCategories\\edit.aspx.cs"}, {"SourceFile": "Administration\\ProductCategories\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ProductCategories\\ListView.ascx.cs"}, {"SourceFile": "Administration\\ProductCategories\\Popup.aspx.cs"}, {"SourceFile": "Administration\\ProductCategories\\SearchPopup.ascx.cs"}, {"SourceFile": "Administration\\ProductsView.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\default.aspx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\edit.aspx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\import.aspx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\ImportDefaultsView.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\ListView.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\Notes.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\Popup.aspx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\RelatedProducts.ascx.cs"}, {"SourceFile": "Administration\\ProductTemplates\\view.aspx.cs"}, {"SourceFile": "Administration\\ProductTypes\\default.aspx.cs"}, {"SourceFile": "Administration\\ProductTypes\\edit.aspx.cs"}, {"SourceFile": "Administration\\ProductTypes\\EditView.ascx.cs"}, {"SourceFile": "Administration\\ProductTypes\\ListView.ascx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\Relationships\\default.aspx.cs"}, {"SourceFile": "Administration\\DynamicLayout\\Relationships\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Releases\\default.aspx.cs"}, {"SourceFile": "Administration\\Releases\\edit.aspx.cs"}, {"SourceFile": "Administration\\Releases\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Releases\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Releases\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\RenameTabs\\default.aspx.cs"}, {"SourceFile": "Administration\\RenameTabs\\ListView.ascx.cs"}, {"SourceFile": "Administration\\RenameTabs\\Popup.aspx.cs"}, {"SourceFile": "Administration\\RenameTabs\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\REPLACE_IMAGES\\import.aspx.cs"}, {"SourceFile": "Administration\\REPLACE_IMAGES\\ImportView.ascx.cs"}, {"SourceFile": "Administration\\Roles\\default.aspx.cs"}, {"SourceFile": "Administration\\Roles\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\Roles\\edit.aspx.cs"}, {"SourceFile": "Administration\\Roles\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Roles\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Roles\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\Roles\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\Roles\\Users.ascx.cs"}, {"SourceFile": "Administration\\Roles\\view.aspx.cs"}, {"SourceFile": "Administration\\Schedulers\\default.aspx.cs"}, {"SourceFile": "Administration\\Schedulers\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\Schedulers\\edit.aspx.cs"}, {"SourceFile": "Administration\\Schedulers\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Schedulers\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Schedulers\\view.aspx.cs"}, {"SourceFile": "Administration\\Shippers\\default.aspx.cs"}, {"SourceFile": "Administration\\Shippers\\edit.aspx.cs"}, {"SourceFile": "Administration\\Shippers\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Shippers\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Shortcuts\\default.aspx.cs"}, {"SourceFile": "Administration\\Shortcuts\\edit.aspx.cs"}, {"SourceFile": "Administration\\Shortcuts\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Shortcuts\\ListView.ascx.cs"}, {"SourceFile": "Administration\\StudioView.ascx.cs"}, {"SourceFile": "Administration\\SyncSchema\\default.aspx.cs"}, {"SourceFile": "Administration\\SyncSchema\\SpecifyDatabases.ascx.cs"}, {"SourceFile": "Administration\\SyncSchema\\SyncControl.cs"}, {"SourceFile": "Administration\\SyncSchema\\VerifyColumns.ascx.cs"}, {"SourceFile": "Administration\\SyncSchema\\VerifyFunctions.ascx.cs"}, {"SourceFile": "Administration\\SyncSchema\\VerifyProcedures.ascx.cs"}, {"SourceFile": "Administration\\SyncSchema\\VerifyTables.ascx.cs"}, {"SourceFile": "Administration\\SyncSchema\\VerifyViews.ascx.cs"}, {"SourceFile": "Administration\\SyncSchema\\WizardButtons.ascx.cs"}, {"SourceFile": "Administration\\SystemLog\\default.aspx.cs"}, {"SourceFile": "Administration\\SystemLog\\ListView.ascx.cs"}, {"SourceFile": "Administration\\SystemView.ascx.cs"}, {"SourceFile": "Administration\\TaxRates\\default.aspx.cs"}, {"SourceFile": "Administration\\TaxRates\\edit.aspx.cs"}, {"SourceFile": "Administration\\TaxRates\\EditView.ascx.cs"}, {"SourceFile": "Administration\\TaxRates\\ListView.ascx.cs"}, {"SourceFile": "Administration\\UploadSetting\\Default.aspx.cs"}, {"SourceFile": "Administration\\UploadSetting\\DefaultView.ascx.cs"}, {"SourceFile": "App_MasterPages\\Default\\DefaultViewIframe.master.cs"}, {"SourceFile": "App_MasterPages\\Default\\DefaultViewNoShortcuts.master.cs"}, {"SourceFile": "App_MasterPages\\Default\\Shortcuts_PO.ascx.cs"}, {"SourceFile": "App_Start\\WebApiConfig.cs"}, {"SourceFile": "AP_ProcessImages\\DefaultView.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\DeletedImages.aspx.cs"}, {"SourceFile": "AP_ProcessImages\\UpdateImageQueue.aspx.cs"}, {"SourceFile": "Administration\\UpdateImageQueue\\Default.aspx.cs"}, {"SourceFile": "Administration\\UpdateImageQueue\\DefaultView.ascx.cs"}, {"SourceFile": "Administration\\Teams\\CreateGLCodes.aspx.cs"}, {"SourceFile": "Administration\\Teams\\CreateGLCodes.aspx.designer.cs"}, {"SourceFile": "Administration\\Teams\\CreateGLCodesView.ascx.cs"}, {"SourceFile": "Administration\\Teams\\CreateGLCodesView.ascx.designer.cs"}, {"SourceFile": "Administration\\Teams\\default.aspx.cs"}, {"SourceFile": "Administration\\Teams\\DepartmentHandler.ashx.cs"}, {"SourceFile": "Administration\\Teams\\DeptView.aspx.cs"}, {"SourceFile": "Administration\\Teams\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\Teams\\edit.aspx.cs"}, {"SourceFile": "Administration\\Teams\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Teams\\GLCodeList.aspx.cs"}, {"SourceFile": "Administration\\Teams\\GLCodeList.aspx.designer.cs"}, {"SourceFile": "Administration\\Teams\\GLCodeListView.ascx.cs"}, {"SourceFile": "Administration\\Teams\\GLCodeListView.ascx.designer.cs"}, {"SourceFile": "Administration\\Teams\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Teams\\LocationsHandler.ashx.cs"}, {"SourceFile": "Administration\\Teams\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\Teams\\Popup.aspx.cs"}, {"SourceFile": "Administration\\Teams\\PopupMultiSelect.aspx.cs"}, {"SourceFile": "Administration\\Teams\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\Teams\\SelectAllGLCode.aspx.cs"}, {"SourceFile": "Administration\\Teams\\SelectAllGLCode.aspx.designer.cs"}, {"SourceFile": "Administration\\Teams\\Users.ascx.cs"}, {"SourceFile": "Administration\\Teams\\view.aspx.cs"}, {"SourceFile": "Administration\\Terminology\\default.aspx.cs"}, {"SourceFile": "Administration\\Terminology\\DetailView.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\edit.aspx.cs"}, {"SourceFile": "Administration\\Terminology\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\import.aspx.cs"}, {"SourceFile": "Administration\\Terminology\\ImportDefaultsView.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\Import\\LanguagePacks.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\Import\\default.aspx.cs"}, {"SourceFile": "Administration\\Terminology\\Import\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\ListView.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\MassUpdate.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\NewRecord.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\SearchBasic.ascx.cs"}, {"SourceFile": "Administration\\Terminology\\view.aspx.cs"}, {"SourceFile": "Administration\\TGS_CRMAdmin.aspx.cs"}, {"SourceFile": "Administration\\TGS_CRMAdminListView.ascx.cs"}, {"SourceFile": "Administration\\Updater\\default.aspx.cs"}, {"SourceFile": "Administration\\Updater\\EditView.ascx.cs"}, {"SourceFile": "Administration\\Upgrade\\default.aspx.cs"}, {"SourceFile": "Administration\\Upgrade\\ListView.ascx.cs"}, {"SourceFile": "Administration\\UserLogins\\default.aspx.cs"}, {"SourceFile": "Administration\\UserLogins\\ListView.ascx.cs"}, {"SourceFile": "Administration\\UsersView.ascx.cs"}, {"SourceFile": "Administration\\WorkflowView.ascx.cs"}, {"SourceFile": "App_MasterPages\\Default\\TabMenu.ascx.cs"}, {"SourceFile": "AP_Applications\\default.aspx.cs"}, {"SourceFile": "AP_Applications\\DetailView.ascx.cs"}, {"SourceFile": "AP_Applications\\edit.aspx.cs"}, {"SourceFile": "AP_Applications\\EditView.ascx.cs"}, {"SourceFile": "AP_Applications\\ListView.ascx.cs"}, {"SourceFile": "AP_Applications\\MassUpdate.ascx.cs"}, {"SourceFile": "AP_Applications\\view.aspx.cs"}, {"SourceFile": "Calendar\\CalendarControl.cs"}, {"SourceFile": "Calendar\\CalendarHeader.ascx.cs"}, {"SourceFile": "Calendar\\DayGrid.ascx.cs"}, {"SourceFile": "Calendar\\DayRow.ascx.cs"}, {"SourceFile": "Calendar\\default.aspx.cs"}, {"SourceFile": "Calendar\\ListView.ascx.cs"}, {"SourceFile": "Calendar\\Month.aspx.cs"}, {"SourceFile": "Calendar\\MonthRow.ascx.cs"}, {"SourceFile": "Calendar\\MonthView.ascx.cs"}, {"SourceFile": "Calendar\\MyCalendar.ascx.cs"}, {"SourceFile": "Calendar\\NewRecord.ascx.cs"}, {"SourceFile": "Calendar\\Popup.aspx.cs"}, {"SourceFile": "Calendar\\Shared.aspx.cs"}, {"SourceFile": "Calendar\\SharedCell.ascx.cs"}, {"SourceFile": "Calendar\\SharedGrid.ascx.cs"}, {"SourceFile": "Calendar\\SharedView.ascx.cs"}, {"SourceFile": "Calendar\\Tasks.ascx.cs"}, {"SourceFile": "Calendar\\Week.aspx.cs"}, {"SourceFile": "Calendar\\WeekGrid.ascx.cs"}, {"SourceFile": "Calendar\\WeekRow.ascx.cs"}, {"SourceFile": "Calendar\\WeekView.ascx.cs"}, {"SourceFile": "Calendar\\Year.aspx.cs"}, {"SourceFile": "Calendar\\YearGrid.ascx.cs"}, {"SourceFile": "Calendar\\YearView.ascx.cs"}, {"SourceFile": "CommonHelper\\ComHandler.ashx.cs"}, {"SourceFile": "CubpAPI\\GetTokenController.cs"}, {"SourceFile": "CubpAPI\\Models\\CommonToken.cs"}, {"SourceFile": "CubpAPI\\Models\\MessageStatus.cs"}, {"SourceFile": "CubpAPI\\Models\\RequestType.cs"}, {"SourceFile": "CubpAPI\\Models\\ResponseType.cs"}, {"SourceFile": "CubpAPI\\Models\\TokenInfo.cs"}, {"SourceFile": "CubpAPI\\RequestStartController.cs"}, {"SourceFile": "EmailDataSource\\DataTable\\default.aspx.cs"}, {"SourceFile": "EmailDataSource\\DataTable\\DetailView.ascx.cs"}, {"SourceFile": "EmailDataSource\\DataTable\\edit.aspx.cs"}, {"SourceFile": "EmailDataSource\\DataTable\\EditView.ascx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\GetData.ashx.cs"}, {"SourceFile": "EmailDataSource\\DataTable\\ListView.ascx.cs"}, {"SourceFile": "EmailDataSource\\DataTable\\view.aspx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\default.aspx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\DetailView.ascx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\edit.aspx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\EditView.ascx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\ListView.ascx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\MassUpdate.ascx.cs"}, {"SourceFile": "EmailDataSource\\EmailTemplate\\view.aspx.cs"}, {"SourceFile": "Emails\\Attachment.aspx.cs"}, {"SourceFile": "Emails\\EmailLogDetailView.ascx.cs"}, {"SourceFile": "Emails\\EmailLog.aspx.cs"}, {"SourceFile": "Emails\\InvoiceDetailView.ascx.cs"}, {"SourceFile": "Emails\\Invoice.aspx.cs"}, {"SourceFile": "Emails\\EmailLogListView.ascx.cs"}, {"SourceFile": "Emails\\InvoiceListView.ascx.cs"}, {"SourceFile": "Emails\\InvoiceView.aspx.cs"}, {"SourceFile": "Emails\\EmailLogPopup.aspx.cs"}, {"SourceFile": "Emails\\EmailLogDetail.aspx.cs"}, {"SourceFile": "Emails\\SearchView.ascx.cs"}, {"SourceFile": "Users\\UserSiteView.ascx.cs"}, {"SourceFile": "Users\\UserSiteView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\FileDownload.aspx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\FileDownload.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\ResetPassword.aspx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\ResetPassword.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchGLCode.aspx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchGLCode.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchGLCodeCommon.ascx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchGLCodeCommon.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendorAccount.aspx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendorAccount.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendorAccountCommon.ascx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendorAccountCommon.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendorCommon.ascx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendorCommon.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendor.aspx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchVendor.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchSiteCommon.ascx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchSiteCommon.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchSite.aspx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchSite.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchInvoice.aspx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchInvoice.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchInvoiceCommon.ascx.cs"}, {"SourceFile": "UtilityBillPay\\CommonPage\\SearchInvoiceCommon.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ApImageList.cs"}, {"SourceFile": "UtilityBillPay\\Common\\CallMethodByAOP.cs"}, {"SourceFile": "UtilityBillPay\\Common\\AzureBlob.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ConfigName.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ConfigType.cs"}, {"SourceFile": "UtilityBillPay\\Common\\DateTimeExpansion.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ExactechApprovalButton.cs"}, {"SourceFile": "UtilityBillPay\\Common\\GzipFilter.cs"}, {"SourceFile": "UtilityBillPay\\Common\\SharpZipLibHepler.cs"}, {"SourceFile": "UtilityBillPay\\Common\\TimingAspect.cs"}, {"SourceFile": "UtilityBillPay\\Common\\PdfHelper.cs"}, {"SourceFile": "UtilityBillPay\\Common\\PriorityTypeEnum.cs"}, {"SourceFile": "UtilityBillPay\\Common\\RequisitionType.cs"}, {"SourceFile": "UtilityBillPay\\Common\\StringHelper.cs"}, {"SourceFile": "UtilityBillPay\\Common\\SystemLogRecord.cs"}, {"SourceFile": "UtilityBillPay\\Common\\Utils.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ValidatorHelper.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ViewName.cs"}, {"SourceFile": "UtilityBillPay\\Common\\DataTableBuilder.cs"}, {"SourceFile": "UtilityBillPay\\Common\\DataTableHelper.cs"}, {"SourceFile": "UtilityBillPay\\Common\\DictType.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ExactechRole.cs"}, {"SourceFile": "UtilityBillPay\\Common\\ExactechStatus.cs"}, {"SourceFile": "UtilityBillPay\\Common\\Extensions.cs"}, {"SourceFile": "UtilityBillPay\\Common\\String.cs"}, {"SourceFile": "UtilityBillPay\\Common\\TextHelper.cs"}, {"SourceFile": "UtilityBillPay\\Common\\Workflow\\ExpressionParser.cs"}, {"SourceFile": "UtilityBillPay\\Common\\Workflow\\XMLHelper.cs"}, {"SourceFile": "UtilityBillPay\\Common\\Workflow\\XPDLDefinition.cs"}, {"SourceFile": "UtilityBillPay\\Common\\AppHelper.cs"}, {"SourceFile": "UtilityBillPay\\Dashboard\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Dashboard\\DefaultView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\AccountItemPageReq.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\AccountsPayableReportSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\BasePageReq.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\BaseTableDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\BillSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\CCMailInfo.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\CheckRequestLoginDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\CUBP_AP_File_Report_DTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\CUBP_Back_Up_DTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\DailyProductionReportDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\DownloadFileRsp.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ExConfigInput.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ExportTxtDto.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ImportInvoiceDto.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ImportResult.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ImportRsp.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\InvoiceInitReq.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\InvoiceInitRes.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\InvoiceLineSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\InvoicePdfSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\InvoiceSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ReportSuppliePaymentDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ReportBillAmountDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ExportSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ReportSearchDTOV3.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\RequestLoginSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\SiteSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\SiteVendorAccountInvoicesSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\TaskAttachmentSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\TaskSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\UtilityVendorAccountSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ReportSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\PrevDataRsp.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\SaveLineItemsDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\ExportTxtSearchDto.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\VendorAccountItemCountReq.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\LineItemManagementSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\VendorSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\DTO\\VendorSiteSearchDTO.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\InvoiceHistory.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\InvoiceTaskAttachment.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\AddAttachment.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\RequestLogin.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\UtilityLogin.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\UtilityTypeClass.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\VendorSite.ashx.cs"}, {"SourceFile": "UtilityBillPay\\Home\\SpendByMonth.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Home\\SpendByMonth.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceHistory\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceHistory\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceHistory\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceHistory\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\AddAttachment.aspx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\AddAttachment.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ConsumptionGridInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\CustomContentInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\DailyProductionReportInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ExportInvoiceLineInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ExportLogDetailInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ExportLogInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ImageInvoiceInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceAddTaskCommentInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceAttachmentInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceHistoryInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceLineReasonInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceLineSummaryInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceTaskAttachmentInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceTaskNormalInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\RecordTimeLogInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\SystemLogInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\Terminology.cs"}, {"SourceFile": "UtilityBillPay\\Models\\UtilityLoginInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\UtilityTypeClassInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceTaskCommentInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\RequestLoginInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\VendorAccountImportInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\VendorSiteInfo.cs"}, {"SourceFile": "UtilityBillPay\\PendingRequest\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\PendingRequest\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\PendingRequest\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\PendingRequest\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisDetail.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisDetail.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisListView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisReportDetail.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisReportDetail.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ElectricityDemandAnalysisReport.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SupplierPayment.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SupplierPayment.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SupplierPaymentReportListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SupplierPaymentReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\RawInvoiceReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\RawInvoiceReportDatatView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\RawInvoiceReportListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\TaskListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\TaskListView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\AccountsPayableListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\AccountsPayableListView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\TaskReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\TaskReport.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ConsumptionListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ConsumptionListView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\AccountsPayableReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\AccountsPayableReport.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SpendByMonthListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SpendByMonthListView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\DailyProductionListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\DailyProductionListView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ConsumptionReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ConsumptionReport.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SpendByMonthReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\SpendByMonthReport.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Report\\DailyProductionReport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\DailyProductionReport.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\ForgotPassword.aspx.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\ForgotPassword.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\RequestLogin.aspx.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\RequestLogin.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\RequestLoginView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\RequestLogin\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Site\\EditForAccountInvoices.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Site\\EditViewForAccountInvoices.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Site\\ListView - 复制.ascx.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\ImportServiceSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceAttachmentSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceHistorySql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceTaskAttachmentSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\RequestLoginSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\SystemLogSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\UtilityLoginSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\UtilityTypeClassSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\VendorSiteSql.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\Attachment.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\Attachment.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\Comments.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\Comments.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityBillPayTask\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\ExFile\\UploadDialog.aspx.cs"}, {"SourceFile": "UtilityBillPay\\ExFile\\UploadDialog.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Export\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Export\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\ActionHistory.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\CommonData.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\Config.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\DownloadFileHandler.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\ExFile.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\ExportCubpTxt.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\Index.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\InvoiceIndexing.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\InvoiceLine.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\LineItemManagement.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\PaymentImport.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\PermissionManagement.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\Report.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\Site.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\Test.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\UploadAjax.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\Users.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\UserSite.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\UtilityVendorAccount.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\UtilityVendorAccountItems.ashx.cs"}, {"SourceFile": "UtilityBillPay\\GeneralHandle\\Vendor.ashx.cs"}, {"SourceFile": "UtilityBillPay\\Home\\default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Home\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\AddException.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\AddException.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\AddPoLine.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\AddPoLine.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\Comments.aspx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\Comments.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\LineItemView.aspx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\LineItem.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\LineItem.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\SearchVendor.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\SearchVendor.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\GLCodeInfoEntry.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\GLCodeInfoEntry.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\ImageView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\ImageView.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\IndexingImageNav.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\IndexingImageNav.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\IndexingView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\InvoiceActionHistory.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\InvoiceActionHistory.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\InvoiceInfoEntry.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\InvoiceInfoEntry.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\popUpEdit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\popUpEdit.aspx.designer.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\SubmitApprovalDiv.ascx.cs"}, {"SourceFile": "UtilityBillPay\\InvoiceIndexing\\SubmitApprovalDiv.ascx.designer.cs"}, {"SourceFile": "UtilityBillPay\\Invoices\\default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Invoices\\edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Invoices\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Invoices\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\LineItemManagement\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\LineItemManagement\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\LineItemManagement\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\LineItemManagement\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ApImagesInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\BaseAshx.cs"}, {"SourceFile": "UtilityBillPay\\Models\\CityStateInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\CommonIdInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ConditionInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ConfigInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\DataDictInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\EmailTemplatesInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ExFileInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ExportView.cs"}, {"SourceFile": "UtilityBillPay\\Models\\ExportCubpTxtInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceActionHistoryInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceActionHistoryView.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceLineInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceLineItemInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceTaskCommentSessionInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceTaskInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceTaskSessionInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\LineItemManagementInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\PaymentImportInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\SendEmailInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\InvoiceLineAccountItemInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\SiteInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\SystemAdminFunctionInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\SystemRoleFunctionInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\TransitionInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\UsersInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\UserSiteInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\UtilityVendorAccountInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\UtilityVendorAccountItemsInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\VendorInfo.cs"}, {"SourceFile": "UtilityBillPay\\Models\\WfProcessInfo.cs"}, {"SourceFile": "UtilityBillPay\\PaymentManagement\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\PaymentManagement\\PaymentImportView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\PaymentManagement\\PaymentImport.aspx.cs"}, {"SourceFile": "UtilityBillPay\\PaymentManagement\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\PermissionManagement\\default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\PermissionManagement\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Report\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Site\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Site\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Site\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Site\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\ActionHistorySql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\ConfigSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\DbContext.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\ExportCubpTxtLogSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\FileSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceTaskSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\PaymentImportSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\ReportSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\ExportCubpTxtSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\IAbstractFactory.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\CommonDicSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\EmailSourceData.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\EmailSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceActionHistorySql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceIndexingSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceLineItemSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\InvoiceLineSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\LineItemManagementSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\PermissionManagementSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\SaveSearchSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\SiteSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\SiteVendorSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\SqlObject.cs"}, {"SourceFile": "UtilityBillPay\\Models\\TData.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\UserSiteSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\UsersSql.cs"}, {"SourceFile": "AP_ProcessImages\\InvoiceInfoEntry.ascx.cs"}, {"SourceFile": "Audit\\default.aspx.cs"}, {"SourceFile": "Audit\\Popup.aspx.cs"}, {"SourceFile": "Home\\PO_DetailView.ascx.cs"}, {"SourceFile": "Home\\NonPO_DetailView.ascx.cs"}, {"SourceFile": "Notes\\Attachment.aspx.cs"}, {"SourceFile": "Notes\\default.aspx.cs"}, {"SourceFile": "Notes\\DetailView.ascx.cs"}, {"SourceFile": "Notes\\edit.aspx.cs"}, {"SourceFile": "Notes\\EditView.ascx.cs"}, {"SourceFile": "Notes\\import.aspx.cs"}, {"SourceFile": "Notes\\ImportDefaultsView.ascx.cs"}, {"SourceFile": "Notes\\ListView.ascx.cs"}, {"SourceFile": "Notes\\MassUpdate.ascx.cs"}, {"SourceFile": "Notes\\NewRecord.ascx.cs"}, {"SourceFile": "Notes\\view.aspx.cs"}, {"SourceFile": "Password.aspx.cs"}, {"SourceFile": "AP_ProcessImages\\StatisticsListView.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\default.aspx.cs"}, {"SourceFile": "AP_ProcessImages\\DeletedListView.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\ImageUndelete.aspx.cs"}, {"SourceFile": "AP_ProcessImages\\ImageUpload.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\ImageUpload.ascx.designer.cs"}, {"SourceFile": "AP_ProcessImages\\ImageView.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\ImageView.ascx.designer.cs"}, {"SourceFile": "AP_ProcessImages\\ImageUndeleteNav.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\ImageUndeleteNav.ascx.designer.cs"}, {"SourceFile": "AP_ProcessImages\\InvoiceSearchListView.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\MassUpdate.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\PendingImages.aspx.cs"}, {"SourceFile": "AP_ProcessImages\\ImageUndeleteView.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\PendingListView.ascx.cs"}, {"SourceFile": "AP_ProcessImages\\ShowImage.aspx.cs"}, {"SourceFile": "AP_ProcessImages\\ShowImage.aspx.designer.cs"}, {"SourceFile": "AP_ProcessImages\\Statistics.aspx.cs"}, {"SourceFile": "Startup.cs"}, {"SourceFile": "Users\\UsersEditView.ascx.cs"}, {"SourceFile": "Users\\GetUserApprovalLevel.ashx.cs"}, {"SourceFile": "Users\\UsersDelegateView.ascx.cs"}, {"SourceFile": "Users\\UsersListView.ascx.cs"}, {"SourceFile": "Users\\PopupMultiSelectDistributorForUser.aspx.cs"}, {"SourceFile": "Users\\PopupMultiSelectUser.aspx.cs"}, {"SourceFile": "Users\\PopupSelectMultiAccessDistributor.aspx.cs"}, {"SourceFile": "Users\\PopupSelectMultiAccessDistributor.aspx.designer.cs"}, {"SourceFile": "Users\\getDistributorCount.ashx.cs"}, {"SourceFile": "Users\\PopupMultiSelectDistributor.aspx.cs"}, {"SourceFile": "Users\\Distributors.ascx.cs"}, {"SourceFile": "Users\\import.aspx.cs"}, {"SourceFile": "Users\\ImportDefaultsView.ascx.cs"}, {"SourceFile": "Users\\Logins.ascx.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\UtilityVendorAccountItemsSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\UtilityVendorAccountSql.cs"}, {"SourceFile": "UtilityBillPay\\SqlData\\VendorSql.cs"}, {"SourceFile": "UtilityBillPay\\UtilityLogin\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityLogin\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityLogin\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityLogin\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccountItems\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccountItems\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccountItems\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccountItems\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccount\\ReportView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccount\\Report.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccount\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccount\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccount\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\UtilityVendorAccount\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Vendor\\Default.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Vendor\\Edit.aspx.cs"}, {"SourceFile": "UtilityBillPay\\Vendor\\EditView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Vendor\\ListView.ascx.cs"}, {"SourceFile": "UtilityBillPay\\Views\\AccountIndexingView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\AccountsPayableReportView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ApImageView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ApplicationBatchView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\BillView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ElectricityDemandAnalysisView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\InvoiceAddTaskCommentView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\InvoiceHistoryView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\InvoiceView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\InvoiceViewV2.cs"}, {"SourceFile": "UtilityBillPay\\Views\\LineItemManagementView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportSupplieAggregated.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportSupplieIndividual.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportSupplieSummary.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportBillAmount.cs"}, {"SourceFile": "UtilityBillPay\\Views\\AccountReportView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportViewV3.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportViewV2Export.cs"}, {"SourceFile": "UtilityBillPay\\Views\\ReportViewV2.cs"}, {"SourceFile": "UtilityBillPay\\Views\\SiteVendorAccountInvoicesView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\SiteVendorAccountView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\SiteView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\TaskView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\UtilityVendorAccountView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\VendorShotView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\VendorSiteView.cs"}, {"SourceFile": "UtilityBillPay\\Views\\VendorView.cs"}, {"SourceFile": "_code\\ApiTimer.cs"}, {"SourceFile": "_code\\CreateImportTableVariableForCsv.cs"}, {"SourceFile": "_code\\EmailHelper\\EmailSender.cs"}, {"SourceFile": "_code\\EmailHelper\\EmailSetting.cs"}, {"SourceFile": "_code\\helper\\Binder.cs"}, {"SourceFile": "_code\\helper\\PdfToMerger.cs"}, {"SourceFile": "_code\\InvoiceUtils.cs"}, {"SourceFile": "_code\\ModuleForPdfUrlRewriting.cs"}, {"SourceFile": "_code\\mono\\ContentDisposition.cs"}, {"SourceFile": "_code\\SplendidCharge.cs"}, {"SourceFile": "_code\\InlineScript.cs"}, {"SourceFile": "_code\\Pop3\\Pop3MailClient.cs"}, {"SourceFile": "_code\\Pop3\\Pop3MimeClient.cs"}, {"SourceFile": "_code\\Pop3\\QuotedPrintable.cs"}, {"SourceFile": "_code\\Pop3\\RxMailMessage.cs"}, {"SourceFile": "_code\\SplendidPagePassword.cs"}, {"SourceFile": "_code\\SplendidPersonalizationProvider.cs"}, {"SourceFile": "_code\\SystemLogRecord.cs"}, {"SourceFile": "_code\\TGSAP\\ACLRole.cs"}, {"SourceFile": "_code\\TGSAP\\ActionType.cs"}, {"SourceFile": "_code\\TGSAP\\APBatchConfig.cs"}, {"SourceFile": "_code\\TGSAP\\APDelegates.cs"}, {"SourceFile": "_code\\TGSAP\\APImageList.cs"}, {"SourceFile": "_code\\TGSAP\\APImagePriority.cs"}, {"SourceFile": "_code\\TGSAP\\APInvoiceImageList.cs"}, {"SourceFile": "_code\\TGSAP\\APInvoiceList.cs"}, {"SourceFile": "_code\\TGSAP\\APProcessDashboardType.cs"}, {"SourceFile": "_code\\TGSAP\\APPROVAL_LEVEL.cs"}, {"SourceFile": "_code\\TGSAP\\APSqlProcs.cs"}, {"SourceFile": "_code\\TGSAP\\GLCodeItem.cs"}, {"SourceFile": "_code\\TGSAP\\InvoiceEmailUtils.cs"}, {"SourceFile": "_code\\TGSAP\\InvoiceStatus.cs"}, {"SourceFile": "_code\\TGSAP\\Json.cs"}, {"SourceFile": "_code\\TGSAP\\PDFMerger.cs"}, {"SourceFile": "_code\\TGSAP\\PoList.cs"}, {"SourceFile": "_code\\TGSAP\\PublicEntity.cs"}, {"SourceFile": "_code\\TGSAP\\SearchResult.cs"}, {"SourceFile": "_code\\TGSAP\\SR.cs"}, {"SourceFile": "_code\\TGSAP\\StringComparer.cs"}, {"SourceFile": "_code\\TGSAP\\TiffToPdfConverter.cs"}, {"SourceFile": "_code\\TGSAP\\ToJsonHelper.cs"}, {"SourceFile": "_code\\Url_GetQueryString.cs"}, {"SourceFile": "_code\\WorkflowInit.cs"}, {"SourceFile": "_code\\WorkflowUtils.cs"}, {"SourceFile": "_code\\SchedulerUtils.cs"}, {"SourceFile": "_controls\\ChartControl.ascx.cs"}, {"SourceFile": "_controls\\ChartControl.ascx.designer.cs"}, {"SourceFile": "_controls\\MaskPopup.ascx.cs"}, {"SourceFile": "_controls\\MaskPopup.ascx.designer.cs"}, {"SourceFile": "_controls\\Shortcuts_PO.ascx.cs"}, {"SourceFile": "_controls\\ShipTo.ascx.cs"}, {"SourceFile": "_controls\\ErrorMessages.ascx.cs"}, {"SourceFile": "_controls\\DynamicButtons.ascx.cs"}, {"SourceFile": "_controls\\TimePicker.ascx.cs"}, {"SourceFile": "_controls\\DetailNavigation.ascx.cs"}, {"SourceFile": "App_MasterPages\\Default\\DefaultView.master.cs"}, {"SourceFile": "App_MasterPages\\Default\\ModuleHeader.ascx.cs"}, {"SourceFile": "App_MasterPages\\Default\\PopupView.master.cs"}, {"SourceFile": "App_MasterPages\\Default\\HeaderLeft.ascx.cs"}, {"SourceFile": "App_MasterPages\\Default\\Shortcuts.ascx.cs"}, {"SourceFile": "EmailTemplates\\PopupEdit.aspx.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "default.aspx.cs"}, {"SourceFile": "Emails\\InboundView.ascx.cs"}, {"SourceFile": "Emails\\inbound.aspx.cs"}, {"SourceFile": "Emails\\default.aspx.cs"}, {"SourceFile": "Emails\\DetailView.ascx.cs"}, {"SourceFile": "Emails\\Drafts.aspx.cs"}, {"SourceFile": "Emails\\edit.aspx.cs"}, {"SourceFile": "Emails\\EditView.ascx.cs"}, {"SourceFile": "Emails\\ListView.ascx.cs"}, {"SourceFile": "Emails\\MassUpdate.ascx.cs"}, {"SourceFile": "Emails\\PopupEmailAddresses.aspx.cs"}, {"SourceFile": "Emails\\Users.ascx.cs"}, {"SourceFile": "Emails\\view.aspx.cs"}, {"SourceFile": "Emails\\_controls\\EmailButtons.ascx.cs"}, {"SourceFile": "EmailTemplates\\default.aspx.cs"}, {"SourceFile": "EmailTemplates\\DetailView.ascx.cs"}, {"SourceFile": "EmailTemplates\\edit.aspx.cs"}, {"SourceFile": "EmailTemplates\\EditView.ascx.cs"}, {"SourceFile": "EmailTemplates\\ListView.ascx.cs"}, {"SourceFile": "EmailTemplates\\MassUpdate.ascx.cs"}, {"SourceFile": "EmailTemplates\\Popup.aspx.cs"}, {"SourceFile": "EmailTemplates\\view.aspx.cs"}, {"SourceFile": "Global.asax.cs"}, {"SourceFile": "Help\\default.aspx.cs"}, {"SourceFile": "Help\\DetailView.ascx.cs"}, {"SourceFile": "Help\\edit.aspx.cs"}, {"SourceFile": "Help\\EditView.ascx.cs"}, {"SourceFile": "Help\\ListView.ascx.cs"}, {"SourceFile": "Help\\view.aspx.cs"}, {"SourceFile": "Home\\About.aspx.cs"}, {"SourceFile": "Home\\AboutSugarCRM.ascx.cs"}, {"SourceFile": "Home\\TrainingPortal.aspx.cs"}, {"SourceFile": "Home\\default.aspx.cs"}, {"SourceFile": "Home\\ServerError.aspx.cs"}, {"SourceFile": "Home\\UnifiedSearch.aspx.cs"}, {"SourceFile": "Images\\Image.aspx.cs"}, {"SourceFile": "Import\\ImportView.ascx.cs"}, {"SourceFile": "Parents\\edit.aspx.cs"}, {"SourceFile": "Parents\\view.aspx.cs"}, {"SourceFile": "AssemblyInfo.cs"}, {"SourceFile": "Users\\SetTimezone.aspx.cs"}, {"SourceFile": "_devtools\\ClassUsage.aspx.cs"}, {"SourceFile": "_devtools\\TranslateHelp.aspx.cs"}, {"SourceFile": "_devtools\\Translate.aspx.cs"}, {"SourceFile": "_devtools\\TerminologyUsage.aspx.cs"}, {"SourceFile": "_devtools\\DumpPHP.aspx.cs"}, {"SourceFile": "_code\\EmailUtils.cs"}, {"SourceFile": "_devtools\\Precompile.aspx.cs"}, {"SourceFile": "_devtools\\SoapUserSessions.aspx.cs"}, {"SourceFile": "_devtools\\WindowsCountries.aspx.cs"}, {"SourceFile": "_code\\MassUpdate.cs"}, {"SourceFile": "_controls\\SearchView.ascx.cs"}, {"SourceFile": "_controls\\SearchButtons.ascx.cs"}, {"SourceFile": "_controls\\HeaderLeft.ascx.cs"}, {"SourceFile": "_controls\\EditLineItemsView.ascx.cs"}, {"SourceFile": "SystemCheck.aspx.cs"}, {"SourceFile": "Users\\default.aspx.cs"}, {"SourceFile": "Users\\DetailView.ascx.cs"}, {"SourceFile": "Users\\edit.aspx.cs"}, {"SourceFile": "Users\\EditMyAccount.aspx.cs"}, {"SourceFile": "Users\\EditView.ascx.cs"}, {"SourceFile": "Users\\ListView.ascx.cs"}, {"SourceFile": "Users\\Login.aspx.cs"}, {"SourceFile": "Users\\LoginView.ascx.cs"}, {"SourceFile": "Users\\Logout.aspx.cs"}, {"SourceFile": "Users\\MyAccount.aspx.cs"}, {"SourceFile": "Users\\Password.aspx.cs"}, {"SourceFile": "Users\\Popup.aspx.cs"}, {"SourceFile": "Users\\PopupMultiSelect.aspx.cs"}, {"SourceFile": "Users\\Roles.ascx.cs"}, {"SourceFile": "Users\\view.aspx.cs"}, {"SourceFile": "vcal_server.aspx.cs"}, {"SourceFile": "_code\\ACLGrid.cs"}, {"SourceFile": "_devtools\\ApplicationVars.aspx.cs"}, {"SourceFile": "_code\\CsvDataReader.cs"}, {"SourceFile": "_code\\Excel\\ExcelDataReaderCore.cs"}, {"SourceFile": "_code\\Excel\\ExcelDataReader.cs"}, {"SourceFile": "_code\\NpgsqlClientFactory.cs"}, {"SourceFile": "_code\\OracleSystemDataFactory.cs"}, {"SourceFile": "_code\\SplendidExport.cs"}, {"SourceFile": "_devtools\\TerminologyHelp.aspx.cs"}, {"SourceFile": "_code\\VisualBasic.cs"}, {"SourceFile": "_code\\Crm.cs"}, {"SourceFile": "_code\\ReportFilterGrid.cs"}, {"SourceFile": "_code\\RdlUtil.cs"}, {"SourceFile": "_code\\Currency.cs"}, {"SourceFile": "_code\\CustomValidators.cs"}, {"SourceFile": "_devtools\\DataDictionary\\default.aspx.cs"}, {"SourceFile": "_devtools\\DataDictionary\\DetailViews.aspx.cs"}, {"SourceFile": "_devtools\\DataDictionary\\EditViews.aspx.cs"}, {"SourceFile": "_devtools\\DataDictionary\\GridViews.aspx.cs"}, {"SourceFile": "_devtools\\DataDictionary\\Utils.cs"}, {"SourceFile": "_code\\DB2ClientFactory.cs"}, {"SourceFile": "_code\\DbProviderFactories.cs"}, {"SourceFile": "_code\\DbProviderFactory.cs"}, {"SourceFile": "_devtools\\DumpUserPreferences.aspx.cs"}, {"SourceFile": "_code\\DynamicControl.cs"}, {"SourceFile": "_devtools\\ExportAll.aspx.cs"}, {"SourceFile": "_devtools\\ImportMySQL.aspx.cs"}, {"SourceFile": "_code\\KeySortDropDownList.cs"}, {"SourceFile": "_code\\L10n.cs"}, {"SourceFile": "_devtools\\Lang.aspx.cs"}, {"SourceFile": "_code\\LanguagePackImport.cs"}, {"SourceFile": "_devtools\\Lang_Resources.aspx.cs"}, {"SourceFile": "_code\\MySQLClientFactory.cs"}, {"SourceFile": "_code\\OracleClientFactory.cs"}, {"SourceFile": "_devtools\\Procedures.aspx.cs"}, {"SourceFile": "_code\\SearchBuilder.cs"}, {"SourceFile": "_code\\SearchControl.cs"}, {"SourceFile": "_code\\Security.cs"}, {"SourceFile": "_code\\SplendidCache.cs"}, {"SourceFile": "_code\\SplendidControl.cs"}, {"SourceFile": "_code\\SplendidDefaults.cs"}, {"SourceFile": "_code\\SplendidDynamic.cs"}, {"SourceFile": "_code\\SplendidError.cs"}, {"SourceFile": "_code\\SplendidGrid.cs"}, {"SourceFile": "_code\\SplendidImport.cs"}, {"SourceFile": "_code\\SplendidInit.cs"}, {"SourceFile": "_code\\SplendidPage.cs"}, {"SourceFile": "_code\\Sql.cs"}, {"SourceFile": "_code\\SQLAnywhereClientFactory.cs"}, {"SourceFile": "_code\\SQLAnywhereDataAdapter.cs"}, {"SourceFile": "_code\\SqlClientFactory.cs"}, {"SourceFile": "_code\\SqlProcs.cs"}, {"SourceFile": "_code\\SybaseClientFactory.cs"}, {"SourceFile": "_devtools\\Terminology.aspx.cs"}, {"SourceFile": "_code\\TimeZone.cs"}, {"SourceFile": "_devtools\\TimeZoneTests.aspx.cs"}, {"SourceFile": "_code\\Utils.cs"}, {"SourceFile": "_code\\vCalendarHandler.cs"}, {"SourceFile": "_devtools\\WindowsLanguages.aspx.cs"}, {"SourceFile": "_devtools\\WindowsTimeZones.aspx.cs"}, {"SourceFile": "_code\\XmlUtil.cs"}, {"SourceFile": "_controls\\ChartDatePicker.ascx.cs"}, {"SourceFile": "_controls\\ChartHeader.ascx.cs"}, {"SourceFile": "_controls\\CheckAll.ascx.cs"}, {"SourceFile": "_controls\\Chooser.ascx.cs"}, {"SourceFile": "_controls\\TeamAssignedMassUpdate.ascx.cs"}, {"SourceFile": "_controls\\Copyright.ascx.cs"}, {"SourceFile": "_controls\\ExportHeader.ascx.cs"}, {"SourceFile": "_controls\\ParentPopupScripts.ascx.cs"}, {"SourceFile": "_controls\\DatePicker.ascx.cs"}, {"SourceFile": "_controls\\DateTimeEdit.ascx.cs"}, {"SourceFile": "_controls\\DateTimePicker.ascx.cs"}, {"SourceFile": "_controls\\DetailButtons.ascx.cs"}, {"SourceFile": "_controls\\DumpSQL.ascx.cs"}, {"SourceFile": "_controls\\EditButtons.ascx.cs"}, {"SourceFile": "_controls\\LastViewed.ascx.cs"}, {"SourceFile": "_controls\\ListHeader.ascx.cs"}, {"SourceFile": "_controls\\MetaHeader.ascx.cs"}, {"SourceFile": "_controls\\ModuleHeader.ascx.cs"}, {"SourceFile": "_controls\\Shortcuts.ascx.cs"}, {"SourceFile": "_controls\\TeamAssignedPopupScripts.ascx.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\AjaxControlToolkit\\1.0.11119.0\\AjaxControlToolkit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\AspNetPager\\*******\\AspNetPager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\BouncyCastle.1.8.9\\lib\\BouncyCastle.Crypto.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\Common.Logging\\3.3.1.0\\Common.Logging.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\Common.Logging\\3.3.1.0\\Common.Logging.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\CommonHelper\\bin\\Debug\\CommonHelper.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\CommonHelper\\bin\\Debug\\CommonHelper.dll"}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\FredCK.FCKeditorV2\\2.5.2912.21007\\FredCK.FCKeditorV2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools\\bin\\Debug\\Hczb.Tools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools\\bin\\Debug\\Hczb.Tools.dll"}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\SharpZipLib.1.3.3\\lib\\net45\\ICSharpCode.SharpZipLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\IdentityModel.3.10.10\\lib\\net452\\IdentityModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\iTextSharp.********\\lib\\itextsharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\JWT.1.3.3\\lib\\3.5\\JWT.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\MailKit.2.5.0\\lib\\net46\\MailKit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Data.Edm.5.8.2\\lib\\net40\\Microsoft.Data.Edm.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Data.OData.5.8.2\\lib\\net40\\Microsoft.Data.OData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Data.Services.Client.5.8.2\\lib\\net40\\Microsoft.Data.Services.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.IdentityModel.JsonWebTokens.6.7.1\\lib\\net461\\Microsoft.IdentityModel.JsonWebTokens.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.IdentityModel.Logging.6.7.1\\lib\\net461\\Microsoft.IdentityModel.Logging.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.IdentityModel.Protocols.5.3.0\\lib\\net461\\Microsoft.IdentityModel.Protocols.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.IdentityModel.Protocols.OpenIdConnect.5.3.0\\lib\\net461\\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.IdentityModel.Tokens.6.7.1\\lib\\net461\\Microsoft.IdentityModel.Tokens.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\Common7\\IDE\\PublicAssemblies\\Microsoft.mshtml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Owin.4.1.1\\lib\\net45\\Microsoft.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Owin.Host.SystemWeb.4.1.1\\lib\\net45\\Microsoft.Owin.Host.SystemWeb.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Owin.Security.Cookies.4.1.1\\lib\\net45\\Microsoft.Owin.Security.Cookies.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Owin.Security.4.1.1\\lib\\net45\\Microsoft.Owin.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Owin.Security.Jwt.4.1.1\\lib\\net45\\Microsoft.Owin.Security.Jwt.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Owin.Security.OAuth.4.1.1\\lib\\net45\\Microsoft.Owin.Security.OAuth.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.Owin.Security.OpenIdConnect.4.1.1\\lib\\net45\\Microsoft.Owin.Security.OpenIdConnect.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\Microsoft.ReportViewer\\10.0.40219.1\\Microsoft.ReportViewer.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\Microsoft.ReportViewer\\10.0.40219.1\\Microsoft.ReportViewer.WebForms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.VisualBasic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\WindowsAzure.Storage.8.5.0\\lib\\net45\\Microsoft.WindowsAzure.Storage.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\MimeKit.2.15.0\\lib\\net48\\MimeKit.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Newtonsoft.Json.11.0.1\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\NPOI\\2.5.6.0\\NPOI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\NPOI\\2.5.6.0\\NPOI.OOXML.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\NPOI\\2.5.6.0\\NPOI.OpenXml4Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\NPOI\\2.5.6.0\\NPOI.OpenXmlFormats.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Okta.AspNet.Abstractions.3.2.2\\lib\\net452\\Okta.AspNet.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Okta.AspNet.1.8.2\\lib\\net452\\Okta.AspNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Owin.1.0\\lib\\net40\\Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\SqlSugar.5.1.4.107\\lib\\SqlSugar.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.EnterpriseServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.IdentityModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\System.IdentityModel.Tokens.Jwt.6.7.1\\lib\\net461\\System.IdentityModel.Tokens.Jwt.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\System.Linq.Dynamic.1.0.8\\lib\\net40\\System.Linq.Dynamic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.AspNet.WebApi.Client.5.2.7\\lib\\net45\\System.Net.Http.Formatting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.WebRequest.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Runtime.InteropServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Runtime.Serialization.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\System.Spatial.5.8.2\\lib\\net40\\System.Spatial.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\System.Text.Encodings.Web.4.5.0\\lib\\netstandard2.0\\System.Text.Encodings.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Threading.Thread.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.ApplicationServices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.DynamicData.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Entity.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.AspNet.WebApi.Core.5.2.7\\lib\\net45\\System.Web.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\packages\\Microsoft.AspNet.WebApi.WebHost.5.2.7\\lib\\net45\\System.Web.Http.WebHost.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Services.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\YunLanProjectCode\\YunLan Common Code\\Hczb.Code\\Hczb.Tools.ThirdDll\\Utilities\\1.0.3721.19537\\Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "C:\\YunLanProjectCode\\CUBP\\CUBP Demo\\WebSite\\bin\\SplendidCRM.dll", "OutputItemRelativePath": "SplendidCRM.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}