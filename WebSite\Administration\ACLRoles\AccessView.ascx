<%@ Control CodeBehind="AccessView.ascx.cs" Language="c#" AutoEventWireup="false" Inherits="SplendidCRM.Administration.ACLRoles.AccessView" %>
<script runat="server">
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
</script>
<script type="text/javascript">
function toggleDisplay(sID)
{
	var fld = document.getElementById(sID);
	fld.style.display = (fld.style.display == 'none') ? 'inline' : 'none';
	var fldLink = document.getElementById(sID + 'link');
	if ( fldLink != null )
	{
		// 02/28/2008 Paul.  The linked field is the opposite of the main. 
		fldLink.style.display = (fld.style.display == 'none') ? 'inline' : 'none';
	}
}
</script>
<div id="divListView">
	<asp:Panel CssClass="button-panel" Visible="<%# !PrintView %>" runat="server">
		<asp:Label ID="lblError" CssClass="error" EnableViewState="false" Runat="server" />
	</asp:Panel>
	
    <div class="border-sty">
	<SplendidCRM:ACLGrid id="grdACL" Width="100%" CssClass="tabDetailView"
		CellPadding="0" CellSpacing="1" border="0"
		AllowPaging="false" AllowSorting="false" 
		AutoGenerateColumns="false" EnableACLEditing="false"
		EnableViewState="true" runat="server">
		<ItemStyle            CssClass="tabDetailViewDF" />
		<AlternatingItemStyle CssClass="tabDetailViewDF" />
		<HeaderStyle          CssClass="tabDetailViewDL" />
	</SplendidCRM:ACLGrid></div>
</div>
