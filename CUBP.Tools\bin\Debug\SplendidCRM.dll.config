﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections />
	<connectionStrings>
		<add name="DefaultConnection" connectionString="Data Source=.\SQLEXPRESS;AttachDbFilename=|DataDirectory|\aspnet-WebApplication1-**************.mdf;Initial Catalog=aspnet-WebApplication1-**************;Integrated Security=True;User Instance=True" providerName="System.Data.SqlClient" />
		<add name="StorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=cumminsstorage;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
		<!--<add name="StorageConnectionString" connectionString="DefaultEndpointsProtocol=https;AccountName=crmservice;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />-->
	</connectionStrings>
	<system.webServer>
		<!--<rewrite>
			<rules>
				<rule name="Force HTTPS" enabled="true">
					<match url="(.*)" ignoreCase="false" />
					<conditions>
						<add input="{HTTPS}" pattern="off" />
					</conditions>
					<action type="Redirect" url="https://{HTTP_HOST}/{R:1}" appendQueryString="true" redirectType="Permanent" />
				</rule>
				<rule name="Rule2" enabled="true">
					<match url="(.*)" ignoreCase="false" />
					<conditions>
						<add input="{HTTP_HOST}" pattern="^cummins.azurewebsites.net$" />
					</conditions>
					<action type="Redirect" url="http://cumminsap.telamonglobal.com/{R:1}" appendQueryString="true" redirectType="Permanent" />
				</rule>
			</rules>
		</rewrite>
		<handlers>
			<add name="AXD-ISAPI-4.0_32bit" path="*.axd" verb="GET,HEAD,POST,DEBUG" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0"/>
		</handlers>-->

		<handlers>
			<add name="ReportViewerWebControl" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<remove name="OPTIONSVerbHandler" />
			<remove name="TRACEVerbHandler" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
		<modules>
			<add name="ModuleForPdfUrlRewriting" type="SplendidCRM._code.ModuleForPdfUrlRewriting,SplendidCRM" />
		</modules>
	</system.webServer>
	<location path="CubpAPI">
		<system.web>
			<authorization>
				<allow users="*"/>
			</authorization>
		</system.web>
	</location>
	<appSettings>
		<add key="SplendidProvider" value="System.Data.SqlClient" />

		<add key="SplendidSQLServer" value="Password=*******;User ID=Cubp;Initial Catalog=CUBP;Data Source=192.168.1.6;Packet Size=4096" />
		<!--<add key="SplendidSQLServer" value="Password=********;User ID=YunLan;Initial Catalog=Utility_Bill_Pay_Demo;Data Source=103.93.76.244;Packet Size=4096" />-->

		<add key="ap_token" value="A00DF193-6C57-4EF5-82AE-12E1BACED5D9" />
		<add key="ap_remote_login_url" value="http://192.168.1.94/CWCRemoteLogin/Default.aspx" />
		<add key="ap_user_password_update_date" value="5/17/2016" />
		<add key="ap_user_password_update_date_days" value="90" />

		<!--Venodr导入邮件通知-->
		<add key="ServiceErrorManagers" value="<EMAIL>" />
		<add key="SentToClientsWhenCompleted" value="<EMAIL>" />
		<add key="ReqWorkflowFileServer" value="D:\YanProject\Exactech Project\Exactech Project Demo\Web Site\Exactech\Xml\RequisitionWorkFlowV2.xml" />
		<!-- 1. Replace these values with your Okta configuration -->

		<!--Hczb-->
		<add key="okta:ClientId" value="0oa2dydhqhb82vL5X5d7" />
		<add key="okta:ClientSecret" value="CongG0If0F6gApaocqb0hD5Azl0pEH5hQbiYB11B" />
		<add key="okta:OktaDomain" value="https://dev-40830610.okta.com" />

		<!--Exactech-->
		<!--<add key="okta:ClientId" value="0oatujdtzgb9hiBfD0x7" />
		<add key="okta:ClientSecret" value="m6ITYJYncOX_NSJBnTf_t7xxvXRNPq11GB_5Y7oP" />
		<add key="okta:OktaDomain" value="https://exactech.okta.com" /-->

		<add key="okta:AuthorizationServerId" value="default" />
		<!-- 2. Update the Okta application with these values -->
		<add key="okta:RedirectUri" value="https://localhost:44316/default.aspx" />
		<add key="okta:PostLogoutRedirectUri" value="https://localhost:44316/Users/<USER>" />

		<!--Token过期时间、24小时-->
		<add key="TokenExpired" value="24"/>
	</appSettings>
	<!--
    有关 web.config 更改的说明，请参见 http://go.microsoft.com/fwlink/?LinkId=235367。

    可在 <httpRuntime> 标记上设置以下特性。
      <system.Web>
        <httpRuntime targetFramework="4.5" />
      </system.Web>
  -->
	<system.web>
		<!--  DYNAMIC DEBUG COMPILATION
      Set compilation debug="true" to enable ASPX debugging.  Otherwise, setting this value to
      false will improve runtime performance of this application. 
      Set compilation debug="true" to insert debugging symbols (.pdb information)
      into the compiled page. Because this creates a larger file that executes
      more slowly, you should set this value to true only when debugging and to
      false at all other times. For more information, refer to the documentation about
      debugging ASP.NET files.
    -->
		<compilation defaultLanguage="c#" debug="true" targetFramework="4.8" />
		<!--  CUSTOM ERROR MESSAGES
      Set customErrors mode="On" or "RemoteOnly" to enable custom error messages, "Off" to disable. 
      Add <error> tags for each of the errors you want to handle.

      "On" Always display custom (friendly) messages.
      "Off" Always display detailed ASP.NET error information.
      "RemoteOnly" Display custom (friendly) messages only to users not running 
      on the local Web server. This setting is recommended for security purposes, so 
      that you do not display application detail information to remote clients.
    -->
		<customErrors mode="Off" />
		<!--  AUTHENTICATION 
      This section sets the authentication policies of the application. Possible modes are "Windows", 
      "Forms", "Passport" and "None"

      "None" No authentication is performed. 
      "Windows" IIS performs authentication (Basic, Digest, or Integrated Windows) according to 
      its settings for the application. Anonymous access must be disabled in IIS. 
      "Forms" You provide a custom form (Web page) for users to enter their credentials, and then 
      you authenticate them in your application. A user credential token is stored in a cookie.
      "Passport" Authentication is performed via a centralized authentication service provided
      by Microsoft that offers a single logon and core profile services for member sites.
    -->
		<authentication mode="Forms">
			<forms name=".CWC" loginUrl="Users/login.aspx" />
		</authentication>
		<!--  AUTHORIZATION 
      This section sets the authorization policies of the application. You can allow or deny access
      to application resources by user or role. Wildcards: "*" mean everyone, "?" means anonymous 
      (unauthenticated) users.
    -->
		<authorization>
			<allow users="*" />
			<deny users="?" />
			<!-- Allow all users -->
			<!--  <allow     users="[comma separated list of users]"
                roles="[comma separated list of roles]"/>
          <deny      users="[comma separated list of users]"
                roles="[comma separated list of roles]"/>
        -->
		</authorization>
		<!--  APPLICATION-LEVEL TRACE LOGGING
      Application-level tracing enables trace log output for every page within an application. 
      Set trace enabled="true" to enable application trace logging.  If pageOutput="true", the
      trace information will be displayed at the bottom of each page.  Otherwise, you can view the 
      application trace log by browsing the "trace.axd" page from your web application
      root. 
    -->
		<trace enabled="false" requestLimit="10" pageOutput="false" traceMode="SortByTime" localOnly="true" />
		<!--  SESSION STATE SETTINGS
      By default ASP.NET uses cookies to identify which requests belong to a particular session. 
      If cookies are not available, a session can be tracked by adding a session identifier to the URL. 
      To disable cookies, set sessionState cookieless="true".
      mode="Off"
      mode="InProc"
      mode="StateServer"
      mode="SQLServer"
    -->

		<!--<sessionState mode="InProc" stateConnectionString="tcpip=127.0.0.1:42424" sqlConnectionString="data source=127.0.0.1;Trusted_Connection=yes" cookieless="false" timeout="60" />-->
		<sessionState mode="StateServer" stateConnectionString="tcpip=127.0.0.1:42424" sqlConnectionString="data source=127.0.0.1;Trusted_Connection=yes" cookieless="false" timeout="60" />

		<!--  GLOBALIZATION
      This section sets the globalization settings of the application. 
    -->
		<globalization requestEncoding="utf-8" responseEncoding="utf-8" />
		<!-- System.Web.HttpException: Maximum request length exceeded.
      Increase to 100M.
    -->
		<!-- 06/12/2008 Paul.  Add executionTimeout (seconds) so that it will be easier to increase. -->
		<httpRuntime maxRequestLength="100000" executionTimeout="6000" requestValidationMode="2.0" />
		<!-- 07/17/2006 Paul.  Disable Event Validation as it is causing a problem in the Configure Tabs area. (A .NET 2.0 issue) -->
		<!-- 07/07/2007 Paul.  Disable Request Validation is it is causing more problems with the use of HTML in description fields. -->
		<!-- 02/20/2008 Paul.  Add the namespace so that it will compile. -->
		<pages enableSessionState="true" enableEventValidation="false" validateRequest="false" controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID" maintainScrollPositionOnPostBack="true">
			<controls>
				<!-- 06/25/2007 Paul.  Add SplendidCRM controls here to ease the transition to Web Site style of project. -->
				<add tagPrefix="SplendidCRM" namespace="SplendidCRM" assembly="SplendidCRM" />
				<add tagPrefix="ajaxToolkit" namespace="AjaxControlToolkit" assembly="AjaxControlToolkit" />
			</controls>
			<namespaces>
				<add namespace="SplendidCRM" />
			</namespaces>
		</pages>
		<webParts>
			<personalization defaultProvider="SplendidPersonalizationProvider">
				<providers>
					<add name="SplendidPersonalizationProvider" type="SplendidCRM.SplendidPersonalizationProvider" />
				</providers>
			</personalization>
		</webParts>
		<identity impersonate="false" userName="" password="" />

		<!--<httpModules>
			<add name="GzipFilter" type="SplendidCRM.UtilityBillPay.Common.GzipFilter" />
		</httpModules>-->
	</system.web>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.InteropServices.RuntimeInformation" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="MimeKit" publicKeyToken="bede1c8a46c66814" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.15.0.0" newVersion="2.15.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="BouncyCastle.Crypto" publicKeyToken="0e99375e54769942" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.8.9.0" newVersion="1.8.9.0" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
</configuration>