﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Linq.Queryable</name>
  </assembly>
  <members>
    <member name="T:System.Linq.EnumerableExecutor">
      <summary>Представляет дерево выражения и обеспечивает функциональность для выполнения дерева выражения после его перезаписи.</summary>
    </member>
    <member name="M:System.Linq.EnumerableExecutor.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Linq.EnumerableExecutor" />.</summary>
    </member>
    <member name="T:System.Linq.EnumerableExecutor`1">
      <summary>Представляет дерево выражения и обеспечивает функциональность для выполнения дерева выражения после его перезаписи.</summary>
      <typeparam name="T">Тип данных значения, получаемого в результате выполнения дерева выражения.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableExecutor`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Linq.EnumerableExecutor`1" />.</summary>
      <param name="expression">Дерево выражения, которое должно быть связано с новым экземпляром.</param>
    </member>
    <member name="T:System.Linq.EnumerableQuery">
      <summary>Представляет <see cref="T:System.Collections.IEnumerable" /> в виде источника данных <see cref="T:System.Linq.EnumerableQuery" />. </summary>
    </member>
    <member name="M:System.Linq.EnumerableQuery.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Linq.EnumerableQuery" />.</summary>
    </member>
    <member name="T:System.Linq.EnumerableQuery`1">
      <summary>Представляет коллекцию <see cref="T:System.Collections.Generic.IEnumerable`1" /> в виде источника данных <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <typeparam name="T">Тип данных в коллекции.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Linq.EnumerableQuery`1" /> и связывает его с указанной коллекцией <see cref="T:System.Collections.Generic.IEnumerable`1" />.</summary>
      <param name="enumerable">Коллекция, которую необходимо связать с новым экземпляром.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.#ctor(System.Linq.Expressions.Expression)">
      <summary>Инициализирует новый экземпляр класса<see cref="T:System.Linq.EnumerableQuery`1" /> и связывает его с деревом выражения.</summary>
      <param name="expression">Дерево выражения, которое должно быть связано с новым экземпляром.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Возвращает перечислитель, который позволяет выполнять перебор элементов связанной коллекции <see cref="T:System.Collections.Generic.IEnumerable`1" /> или, если коллекция имеет значение NULL, коллекции, получаемой в результате перезаписи связанного дерева выражения в виде запроса к источнику данных <see cref="T:System.Collections.Generic.IEnumerable`1" /> и его выполнения.</summary>
      <returns>Перечислитель, с помощью которого можно осуществлять перебор по связанному источнику данных.</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который позволяет выполнять перебор элементов связанной коллекции <see cref="T:System.Collections.Generic.IEnumerable`1" /> или, если коллекция имеет значение NULL, коллекции, получаемой в результате перезаписи связанного дерева выражения в виде запроса к источнику данных <see cref="T:System.Collections.Generic.IEnumerable`1" /> и его выполнения.</summary>
      <returns>Перечислитель, с помощью которого можно осуществлять перебор по связанному источнику данных.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#ElementType">
      <summary>Получает тип данных в коллекции, представленной данным экземпляром.</summary>
      <returns>Тип данных в коллекции, представленной данным экземпляром.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Expression">
      <summary>Получает дерево выражения, связанное с данным экземпляром или представляющее его.</summary>
      <returns>Дерево выражения, связанное с данным экземпляром или представляющее его.</returns>
    </member>
    <member name="P:System.Linq.EnumerableQuery`1.System#Linq#IQueryable#Provider">
      <summary>Получает объект поставщика запросов, связанного с данным экземпляром.</summary>
      <returns>Поставщик запросов, связанный с данным экземпляром.</returns>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery``1(System.Linq.Expressions.Expression)">
      <summary>Создает новый объект <see cref="T:System.Linq.EnumerableQuery`1" /> и связывает его с указанным деревом выражения, которое представляет коллекцию данных <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <returns>Объект EnumerableQuery, связанный с данным выражением <paramref name="expression" />.</returns>
      <param name="expression">Дерево выражения, которое требуется выполнить.</param>
      <typeparam name="S">Тип данных в коллекции, представленной выражением <paramref name="expression" />.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#CreateQuery(System.Linq.Expressions.Expression)">
      <summary>Создает новый объект <see cref="T:System.Linq.EnumerableQuery`1" /> и связывает его с указанным деревом выражения, которое представляет коллекцию данных <see cref="T:System.Linq.IQueryable" />.</summary>
      <returns>Объект <see cref="T:System.Linq.EnumerableQuery`1" />, связанный с этим выражением <paramref name="expression" />.</returns>
      <param name="expression">Дерево выражения, которое представляет коллекцию данных <see cref="T:System.Linq.IQueryable" />.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute``1(System.Linq.Expressions.Expression)">
      <summary>Выполняет выражение после его перезаписи, чтобы вместо методов <see cref="T:System.Linq.Queryable" /> для все перечислимых источников данных, к которым нельзя создать запрос с помощью методов <see cref="T:System.Linq.Queryable" />, вызывались методы <see cref="T:System.Linq.Enumerable" />.</summary>
      <returns>Значение, получаемое в результате выполнения <paramref name="expression" />.</returns>
      <param name="expression">Дерево выражения, которое требуется выполнить.</param>
      <typeparam name="S">Тип данных в коллекции, представленной выражением <paramref name="expression" />.</typeparam>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.System#Linq#IQueryProvider#Execute(System.Linq.Expressions.Expression)">
      <summary>Выполняет выражение после его перезаписи, чтобы вместо методов <see cref="T:System.Linq.Queryable" /> для все перечислимых источников данных, к которым нельзя создать запрос с помощью методов <see cref="T:System.Linq.Queryable" />, вызывались методы <see cref="T:System.Linq.Enumerable" />.</summary>
      <returns>Значение, получаемое в результате выполнения <paramref name="expression" />.</returns>
      <param name="expression">Дерево выражения, которое требуется выполнить.</param>
    </member>
    <member name="M:System.Linq.EnumerableQuery`1.ToString">
      <summary>Возвращает текстовое представление перечислимой коллекции или, если она имеет значение NULL, дерева выражения, связанного с данным экземпляром.</summary>
      <returns>Текстовое представление перечислимой коллекции или, если она имеет значение NULL, дерева выражения, связанного с данным экземпляром.</returns>
    </member>
    <member name="T:System.Linq.Queryable">
      <summary>Предоставляет набор методов типа static (Shared в Visual Basic) для выполнения запросов к структурам данных, реализующим объект <see cref="T:System.Linq.IQueryable`1" />.</summary>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``0,``0}})">
      <summary>Применяет к последовательности агрегатную функцию.</summary>
      <returns>Конечное агрегатное значение.</returns>
      <param name="source">Последовательность, для которой выполняется статистическая операция.</param>
      <param name="func">Агрегатная функция, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="func" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``2(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}})">
      <summary>Применяет к последовательности агрегатную функцию.Указанное начальное значение используется в качестве исходного значения агрегатной операции.</summary>
      <returns>Конечное агрегатное значение.</returns>
      <param name="source">Последовательность, для которой выполняется статистическая операция.</param>
      <param name="seed">Начальное агрегатное значение.</param>
      <param name="func">Агрегатная функция, вызываемая для каждого элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">Тип агрегатного значения.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="func" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Aggregate``3(System.Linq.IQueryable{``0},``1,System.Linq.Expressions.Expression{System.Func{``1,``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,``2}})">
      <summary>Применяет к последовательности агрегатную функцию.Указанное начальное значение служит исходным значением для агрегатной операции, а указанная функция используется для выбора результирующего значения.</summary>
      <returns>Преобразованное конечное агрегатное значение.</returns>
      <param name="source">Последовательность, для которой выполняется статистическая операция.</param>
      <param name="seed">Начальное агрегатное значение.</param>
      <param name="func">Агрегатная функция, вызываемая для каждого элемента.</param>
      <param name="selector">Функция, преобразующая конечное агрегатное значение в результирующее значение.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TAccumulate">Тип агрегатного значения.</typeparam>
      <typeparam name="TResult">Тип результирующего значения.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="func" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.All``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Проверяет, все ли элементы последовательности удовлетворяют условию.</summary>
      <returns>true, если каждый элемент исходной последовательности проходит проверку, определяемую указанным предикатом, или если последовательность пуста; в противном случае — false.</returns>
      <param name="source">Последовательность, элементы которой проверяются на соответствие условию.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0})">
      <summary>Проверяет, содержит ли последовательность какие-либо элементы.</summary>
      <returns>true, если исходная последовательность содержит какие-либо элементы, в противном случае — false.</returns>
      <param name="source">Последовательность, проверяемая на наличие элементов.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Any``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Проверяет, удовлетворяет ли какой-либо элемент последовательности заданному условию.</summary>
      <returns>true, если какие-либо элементы исходной последовательности проходят проверку, определяемую указанным предикатом; в противном случае — false.</returns>
      <param name="source">Последовательность, элементы которой проверяются на соответствие условию.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable``1(System.Collections.Generic.IEnumerable{``0})">
      <summary>Преобразовывает универсальный объект <see cref="T:System.Collections.Generic.IEnumerable`1" /> в универсальный объект <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, представляющий входную последовательность.</returns>
      <param name="source">Последовательность, подлежащая преобразованию.</param>
      <typeparam name="TElement">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.AsQueryable(System.Collections.IEnumerable)">
      <summary>Преобразовывает коллекцию <see cref="T:System.Collections.IEnumerable" /> в объект <see cref="T:System.Linq.IQueryable" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable" />, представляющий входную последовательность.</returns>
      <param name="source">Последовательность, подлежащая преобразованию.</param>
      <exception cref="T:System.ArgumentException">Последовательность <paramref name="source" /> не реализует объект <see cref="T:System.Collections.Generic.IEnumerable`1" /> для некоторых типов <paramref name="T" />.</exception>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Decimal})">
      <summary>Вычисляет среднее последовательности значений типа <see cref="T:System.Decimal" />.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Decimal" />, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Double})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Double" />.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Double" />, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int32})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Int32" />.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int32" />, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Int64})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Int64" />.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int64" />, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Decimal" /> обнуляемого типа.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если исходная последовательность пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Decimal" /> обнуляемого типа, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Double" /> обнуляемого типа.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если исходная последовательность пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Double" /> обнуляемого типа, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Int32" /> обнуляемого типа.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если исходная последовательность пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int32" /> обнуляемого типа, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Int64" /> обнуляемого типа.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если исходная последовательность пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int64" /> обнуляемого типа, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Single" /> обнуляемого типа.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если исходная последовательность пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Single" /> обнуляемого типа, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average(System.Linq.IQueryable{System.Single})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Single" />.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Single" />, для которых вычисляется среднее.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Decimal" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений, используемых для вычисления среднего.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Double" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Int32" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Int64" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Decimal" /> обнуляемого типа, которая получается в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если последовательность <paramref name="source" /> пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Double" /> обнуляемого типа, которая получается в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если последовательность <paramref name="source" /> пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Int32" /> обнуляемого типа, которая получается в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если последовательность <paramref name="source" /> пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Int64" /> обнуляемого типа, которая получается в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если последовательность <paramref name="source" /> пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>Вычисляет среднее для последовательности значений <see cref="T:System.Single" /> обнуляемого типа, которая получается в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее арифметическое значений последовательности, или null, если последовательность <paramref name="source" /> пуста либо содержит только значения null.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Average``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>Вычисляет среднее для последовательности значений типа <see cref="T:System.Single" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Среднее для последовательности значений.</returns>
      <param name="source">Последовательность значений, для которых вычисляется среднее.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Последовательность <paramref name="source" /> не содержит элементов.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Cast``1(System.Linq.IQueryable)">
      <summary>Преобразовывает элементы объекта <see cref="T:System.Linq.IQueryable" /> в заданный тип.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, который содержит все элементы исходной последовательности, преобразованные в заданный тип.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable" />, содержащий преобразуемые элементы.</param>
      <typeparam name="TResult">Тип, в который преобразуются элементы объекта <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidCastException">Элемент последовательности не может быть приведен к типу <paramref name="TResult" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Concat``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Объединяет две последовательности.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий объединенные элементы двух входных последовательностей.</returns>
      <param name="source1">Первая из объединяемых последовательностей.</param>
      <param name="source2">Последовательность, объединяемая с первой последовательностью.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0)">
      <summary>Определяет, содержится ли указанный элемент в последовательности, используя компаратор проверки на равенство по умолчанию.</summary>
      <returns>true, если входная последовательность содержит элемент с указанным значением, в противном случае — false.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, в котором требуется найти элемент <paramref name="item" />.</param>
      <param name="item">Объект, который требуется найти в последовательности.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Contains``1(System.Linq.IQueryable{``0},``0,System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Определяет, содержит ли последовательность заданный элемент, используя указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>true, если входная последовательность содержит элемент с указанным значением, в противном случае — false.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, в котором требуется найти элемент <paramref name="item" />.</param>
      <param name="item">Объект, который требуется найти в последовательности.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения значений.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает количество элементов в последовательности.</summary>
      <returns>Число элементов во входной последовательности.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы, которые требуется подсчитать.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Число элементов в последовательности <paramref name="source" /> больше, чем <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Count``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает количество элементов указанной последовательности, удовлетворяющих определенному условию.</summary>
      <returns>Число элементов последовательности, удовлетворяющих условию функции предиката.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы, которые требуется подсчитать.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
      <exception cref="T:System.OverflowException">Число элементов в последовательности <paramref name="source" /> больше, чем <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает элементы указанной последовательности или одноэлементную коллекцию, содержащую значение параметра типа по умолчанию, если последовательность пуста.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий значение default(<paramref name="TSource" />), если последовательность <paramref name="source" /> пуста; в противном случае возвращается <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, для которого возвращается значение по умолчанию, если последовательность пуста.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.DefaultIfEmpty``1(System.Linq.IQueryable{``0},``0)">
      <summary>Возвращает элементы указанной последовательности или одноэлементную коллекцию, содержащую указанное значение, если последовательность пуста.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий значение <paramref name="defaultValue" />, если последовательность <paramref name="source" /> пуста; в противном случае возвращается <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, для которого возвращается указанное значение, если последовательность пуста.</param>
      <param name="defaultValue">Значение, возвращаемое в случае пустой последовательности.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает различающиеся элементы последовательности, используя для сравнения значений компаратор проверки на равенство по умолчанию.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий различающиеся элементы из последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется удалить дубликаты.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Distinct``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Возвращает различающиеся элементы последовательности, используя для сравнения значений указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий различающиеся элементы из последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется удалить дубликаты.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения значений.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAt``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Возвращает элемент по указанному индексу в последовательности.</summary>
      <returns>Элемент, находящийся в указанной позиции в последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элемент.</param>
      <param name="index">Отсчитываемый от нуля индекс извлекаемого элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Значение параметра <paramref name="index" /> меньше нуля.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ElementAtOrDefault``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Возвращает элемент по указанному индексу в последовательности или значение по умолчанию, если индекс вне допустимого диапазона.</summary>
      <returns>default(<paramref name="TSource" />), если позиция <paramref name="index" /> находится вне последовательности <paramref name="source" />; в противном случае — элемент, находящийся в указанной позиции в последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элемент.</param>
      <param name="index">Отсчитываемый от нуля индекс извлекаемого элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Находит разность множеств, представленных двумя последовательностями, используя для сравнения значений компаратор проверки на равенство по умолчанию.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, являющийся разностью двух последовательностей как множеств.</returns>
      <param name="source1">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется извлечь элементы, отсутствующие в последовательности <paramref name="source2" />.</param>
      <param name="source2">Последовательность <see cref="T:System.Collections.Generic.IEnumerable`1" />, элементы которой, входящие также в первую последовательность, не будут включены в возвращаемую последовательность.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Except``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Находит разность множеств, представленных двумя последовательностями, используя для сравнения значений указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, являющийся разностью двух последовательностей как множеств.</returns>
      <param name="source1">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется извлечь элементы, отсутствующие в последовательности <paramref name="source2" />.</param>
      <param name="source2">Последовательность <see cref="T:System.Collections.Generic.IEnumerable`1" />, элементы которой, входящие также в первую последовательность, не будут включены в возвращаемую последовательность.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения значений.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает первый элемент последовательности.</summary>
      <returns>Первый элемент последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, первый элемент которого требуется возвратить.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Исходная последовательность пуста.</exception>
    </member>
    <member name="M:System.Linq.Queryable.First``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает первый элемент последовательности, удовлетворяющий указанному условию.</summary>
      <returns>Первый элемент последовательности <paramref name="source" />, прошедший проверку с помощью предиката <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элемент.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Ни один элемент не удовлетворяет условию предиката <paramref name="predicate" />.– или –Исходная последовательность пуста.</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает первый элемент последовательности или значение по умолчанию, если последовательность не содержит элементов.</summary>
      <returns>default(<paramref name="TSource" />), если последовательность <paramref name="source" /> пуста, в противном случае — первый элемент последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, первый элемент которого требуется возвратить.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.FirstOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает первый элемент последовательности, удовлетворяющий указанному условию, или значение по умолчанию, если ни одного такого элемента не найдено.</summary>
      <returns>default(<paramref name="TSource" />), если последовательность <paramref name="source" /> пуста или если ни один ее элемент не прошел проверку, определенную предикатом <paramref name="predicate" />; в противном случае — первый элемент последовательности <paramref name="source" />, прошедший проверку, определенную предикатом <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элемент.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Группирует элементы последовательности в соответствии с заданной функцией селектора ключа.</summary>
      <returns>Объект IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt; в C# или IQueryable(Of IGrouping(Of TKey, TSource)) в Visual Basic, где каждый объект <see cref="T:System.Linq.IGrouping`2" /> содержит последовательность объектов и ключ.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="keySelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Группирует элементы последовательности в соответствии с заданной функцией селектора ключа и сравнивает ключи с помощью указанного компаратора.</summary>
      <returns>Объект IQueryable&lt;IGrouping&lt;TKey, TSource&gt;&gt; в C# или IQueryable(Of IGrouping(Of TKey, TSource)) в Visual Basic, где каждый объект <see cref="T:System.Linq.IGrouping`2" /> содержит последовательность объектов и ключ.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}})">
      <summary>Группирует элементы последовательности в соответствии с заданной функцией селектора ключа и проецирует элементы каждой группы с помощью указанной функции.</summary>
      <returns>Объект IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt; в C# или IQueryable(Of IGrouping(Of TKey, TElement)) в Visual Basic, где каждый объект <see cref="T:System.Linq.IGrouping`2" /> содержит последовательность объектов типа <paramref name="TElement" /> и ключ.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <param name="elementSelector">Функция, сопоставляющая каждый исходный элемент с элементом объекта <see cref="T:System.Linq.IGrouping`2" />.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Тип элементов каждого объекта <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" /> или <paramref name="elementSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Группирует элементы последовательности и проецирует элементы каждой группы с помощью указанной функции.Значения ключей сравниваются с использованием заданного компаратора.</summary>
      <returns>Объект IQueryable&lt;IGrouping&lt;TKey, TElement&gt;&gt; в C# или IQueryable(Of IGrouping(Of TKey, TElement)) в Visual Basic, где каждый объект <see cref="T:System.Linq.IGrouping`2" /> содержит последовательность объектов типа <paramref name="TElement" /> и ключ.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <param name="elementSelector">Функция, сопоставляющая каждый исходный элемент с элементом объекта <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Тип элементов каждого объекта <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" />, <paramref name="elementSelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}})">
      <summary>Группирует элементы последовательности в соответствии с заданной функцией селектора ключа и создает результирующее значение для каждой группы и ее ключа.Элементы каждой группы проецируются с помощью указанной функции.</summary>
      <returns>Объект T:System.Linq.IQueryable`1 с аргументом типа <paramref name="TResult" />, каждый элемент которого представляет проекцию группы и ее ключа.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <param name="elementSelector">Функция, сопоставляющая каждый исходный элемент с элементом объекта <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">Функция для создания результирующего значения для каждой группы.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Тип элементов каждого объекта <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">Тип результирующего значения, возвращаемого функцией <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" />, <paramref name="elementSelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``4(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``2},``3}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Группирует элементы последовательности в соответствии с заданной функцией селектора ключа и создает результирующее значение для каждой группы и ее ключа.Ключи сравниваются с помощью указанного компаратора, элементы каждой группы проецируются с помощью указанной функции.</summary>
      <returns>Объект T:System.Linq.IQueryable`1 с аргументом типа <paramref name="TResult" />, каждый элемент которого представляет проекцию группы и ее ключа.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <param name="elementSelector">Функция, сопоставляющая каждый исходный элемент с элементом объекта <see cref="T:System.Linq.IGrouping`2" />.</param>
      <param name="resultSelector">Функция для создания результирующего значения для каждой группы.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <typeparam name="TElement">Тип элементов каждого объекта <see cref="T:System.Linq.IGrouping`2" />.</typeparam>
      <typeparam name="TResult">Тип результирующего значения, возвращаемого функцией <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" />, <paramref name="elementSelector" />, <paramref name="resultSelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}})">
      <summary>Группирует элементы последовательности в соответствии с заданной функцией селектора ключа и создает результирующее значение для каждой группы и ее ключа.</summary>
      <returns>Объект T:System.Linq.IQueryable`1 с аргументом типа <paramref name="TResult" />, каждый элемент которого представляет проекцию группы и ее ключа.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <param name="resultSelector">Функция для создания результирующего значения для каждой группы.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">Тип результирующего значения, возвращаемого функцией <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupBy``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Linq.Expressions.Expression{System.Func{``1,System.Collections.Generic.IEnumerable{``0},``2}},System.Collections.Generic.IEqualityComparer{``1})">
      <summary>Группирует элементы последовательности в соответствии с заданной функцией селектора ключа и создает результирующее значение для каждой группы и ее ключа.Ключи сравниваются с использованием заданного компаратора.</summary>
      <returns>Объект T:System.Linq.IQueryable`1 с аргументом типа <paramref name="TResult" />, каждый элемент которого представляет проекцию группы и ее ключа.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого следует сгруппировать.</param>
      <param name="keySelector">Функция, извлекающая ключ для каждого элемента.</param>
      <param name="resultSelector">Функция для создания результирующего значения для каждой группы.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <typeparam name="TResult">Тип результирующего значения, возвращаемого функцией <paramref name="resultSelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" />, <paramref name="resultSelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}})">
      <summary>Устанавливает корреляцию между элементами двух последовательностей на основе равенства ключей и группирует результаты.Для сравнения ключей используется компаратор проверки на равенство по умолчанию.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, который содержит элементы типа <paramref name="TResult" />, полученные в результате соединения двух последовательностей с группировкой.</returns>
      <param name="outer">Первая последовательность для соединения.</param>
      <param name="inner">Последовательность, соединяемая с первой последовательностью.</param>
      <param name="outerKeySelector">Функция, извлекающая ключ соединения из каждого элемента первой последовательности.</param>
      <param name="innerKeySelector">Функция, извлекающая ключ соединения из каждого элемента второй последовательности.</param>
      <param name="resultSelector">Функция, создающая результирующий элемент для элемента первой последовательности и коллекции соответствующих элементов второй последовательности.</param>
      <typeparam name="TOuter">Тип элементов первой последовательности.</typeparam>
      <typeparam name="TInner">Тип элементов второй последовательности.</typeparam>
      <typeparam name="TKey">Тип ключей, возвращаемых функциями селектора ключа.</typeparam>
      <typeparam name="TResult">Тип результирующих элементов.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="outer" />, <paramref name="inner" />, <paramref name="outerKeySelector" />, <paramref name="innerKeySelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.GroupJoin``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1},``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Устанавливает корреляцию между элементами двух последовательностей на основе равенства ключей и группирует результаты.Для сравнения ключей используется указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, который содержит элементы типа <paramref name="TResult" />, полученные в результате соединения двух последовательностей с группировкой.</returns>
      <param name="outer">Первая последовательность для соединения.</param>
      <param name="inner">Последовательность, соединяемая с первой последовательностью.</param>
      <param name="outerKeySelector">Функция, извлекающая ключ соединения из каждого элемента первой последовательности.</param>
      <param name="innerKeySelector">Функция, извлекающая ключ соединения из каждого элемента второй последовательности.</param>
      <param name="resultSelector">Функция, создающая результирующий элемент для элемента первой последовательности и коллекции соответствующих элементов второй последовательности.</param>
      <param name="comparer">Компаратор, используемый для хэширования и сравнения ключей.</param>
      <typeparam name="TOuter">Тип элементов первой последовательности.</typeparam>
      <typeparam name="TInner">Тип элементов второй последовательности.</typeparam>
      <typeparam name="TKey">Тип ключей, возвращаемых функциями селектора ключа.</typeparam>
      <typeparam name="TResult">Тип результирующих элементов.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="outer" />, <paramref name="inner" />, <paramref name="outerKeySelector" />, <paramref name="innerKeySelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Находит пересечение множеств, представленных двумя последовательностями, используя для сравнения значений компаратор проверки на равенство по умолчанию.</summary>
      <returns>Последовательность, представляющая собой пересечение двух заданных последовательностей как множеств.</returns>
      <param name="source1">Последовательность, из которой возвращаются различающиеся элементы, входящие также в <paramref name="source2" />.</param>
      <param name="source2">Последовательность, из которой возвращаются различающиеся элементы, входящие также в первую последовательность.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Intersect``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Находит пересечение множеств, представленных двумя последовательностями, используя для сравнения значений указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, являющийся пересечением двух последовательностей как множеств.</returns>
      <param name="source1">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется извлечь различающиеся элементы, входящие также в последовательность <paramref name="source2" />.</param>
      <param name="source2">Объект <see cref="T:System.Collections.Generic.IEnumerable`1" />, из которого извлекаются различающиеся элементы, входящие также в первую последовательность.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения значений.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}})">
      <summary>Устанавливает корреляцию между элементами двух последовательностей на основе сопоставления ключей.Для сравнения ключей используется компаратор проверки на равенство по умолчанию.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, который содержит элементы типа <paramref name="TResult" />, полученные в результате внутреннего соединения двух последовательностей.</returns>
      <param name="outer">Первая последовательность для соединения.</param>
      <param name="inner">Последовательность, соединяемая с первой последовательностью.</param>
      <param name="outerKeySelector">Функция, извлекающая ключ соединения из каждого элемента первой последовательности.</param>
      <param name="innerKeySelector">Функция, извлекающая ключ соединения из каждого элемента второй последовательности.</param>
      <param name="resultSelector">Функция для создания результирующего элемента для пары соответствующих элементов.</param>
      <typeparam name="TOuter">Тип элементов первой последовательности.</typeparam>
      <typeparam name="TInner">Тип элементов второй последовательности.</typeparam>
      <typeparam name="TKey">Тип ключей, возвращаемых функциями селектора ключа.</typeparam>
      <typeparam name="TResult">Тип результирующих элементов.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="outer" />, <paramref name="inner" />, <paramref name="outerKeySelector" />, <paramref name="innerKeySelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Join``4(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``2}},System.Linq.Expressions.Expression{System.Func{``1,``2}},System.Linq.Expressions.Expression{System.Func{``0,``1,``3}},System.Collections.Generic.IEqualityComparer{``2})">
      <summary>Устанавливает корреляцию между элементами двух последовательностей на основе сопоставления ключей.Для сравнения ключей используется указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, который содержит элементы типа <paramref name="TResult" />, полученные в результате внутреннего соединения двух последовательностей.</returns>
      <param name="outer">Первая последовательность для соединения.</param>
      <param name="inner">Последовательность, соединяемая с первой последовательностью.</param>
      <param name="outerKeySelector">Функция, извлекающая ключ соединения из каждого элемента первой последовательности.</param>
      <param name="innerKeySelector">Функция, извлекающая ключ соединения из каждого элемента второй последовательности.</param>
      <param name="resultSelector">Функция для создания результирующего элемента для пары соответствующих элементов.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для хэширования и сравнения ключей.</param>
      <typeparam name="TOuter">Тип элементов первой последовательности.</typeparam>
      <typeparam name="TInner">Тип элементов второй последовательности.</typeparam>
      <typeparam name="TKey">Тип ключей, возвращаемых функциями селектора ключа.</typeparam>
      <typeparam name="TResult">Тип результирующих элементов.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="outer" />, <paramref name="inner" />, <paramref name="outerKeySelector" />, <paramref name="innerKeySelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает последний элемент последовательности.</summary>
      <returns>Значение, находящееся в последней позиции последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, последний элемент которого требуется возвратить.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">Исходная последовательность пуста.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Last``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает последний элемент последовательности, удовлетворяющий указанному условию.</summary>
      <returns>Последний элемент последовательности <paramref name="source" />, прошедший проверку, заданную предикатом <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элемент.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Ни один элемент не удовлетворяет условию предиката <paramref name="predicate" />.– или –Исходная последовательность пуста.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает последний элемент последовательности или значение по умолчанию, если последовательность не содержит элементов.</summary>
      <returns>default(<paramref name="TSource" />), если последовательность <paramref name="source" /> пуста, в противном случае — последний элемент последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, последний элемент которого требуется возвратить.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LastOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает последний элемент последовательности, удовлетворяющий указанному условию, или значение по умолчанию, если ни одного такого элемента не найдено.</summary>
      <returns>default(<paramref name="TSource" />), если последовательность <paramref name="source" /> пуста или ни один ее элемент не прошел проверку функцией предиката, в противном случае — последний элемент последовательности <paramref name="source" />, прошедший проверку функцией предиката.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элемент.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает значение типа <see cref="T:System.Int64" />, представляющее общее число элементов в последовательности.</summary>
      <returns>Число элементов в последовательности <paramref name="source" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы, которые требуется подсчитать.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Число элементов больше, чем <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.LongCount``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает значение типа <see cref="T:System.Int64" />, представляющее число элементов последовательности, удовлетворяющих заданному условию.</summary>
      <returns>Число элементов последовательности <paramref name="source" />, удовлетворяющих условию функции предиката.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы, которые требуется подсчитать.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
      <exception cref="T:System.OverflowException">Число найденных элементов больше, чем <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает максимальное значение для универсального интерфейса <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <returns>Максимальное значение в последовательности.</returns>
      <param name="source">Последовательность значений, для которой определяется максимум.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Max``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Вызывает функцию проекции для каждого элемента универсального интерфейса <see cref="T:System.Linq.IQueryable`1" /> и возвращает максимальное результирующее значение.</summary>
      <returns>Максимальное значение в последовательности.</returns>
      <param name="source">Последовательность значений, для которой определяется максимум.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Тип значения, возвращаемого функцией, заданной параметром <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает минимальное значение для универсального интерфейса <see cref="T:System.Linq.IQueryable`1" />.</summary>
      <returns>Минимальное значение в последовательности.</returns>
      <param name="source">Последовательность значений, для которой определяется минимум.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Min``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Вызывает функцию проекции для каждого элемента универсального интерфейса <see cref="T:System.Linq.IQueryable`1" /> и возвращает минимальное результирующее значение.</summary>
      <returns>Минимальное значение в последовательности.</returns>
      <param name="source">Последовательность значений, для которой определяется минимум.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Тип значения, возвращаемого функцией, заданной параметром <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OfType``1(System.Linq.IQueryable)">
      <summary>Выполняет фильтрацию элементов объекта <see cref="T:System.Linq.IQueryable" /> по заданному типу.</summary>
      <returns>Коллекция элементов последовательности <paramref name="source" />, имеющих тип <paramref name="TResult" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable" />, элементы которого следует фильтровать.</param>
      <typeparam name="TResult">Тип, по которому фильтруются элементы последовательности.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Сортирует элементы последовательности в порядке возрастания ключа.</summary>
      <returns>Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, элементы которого отсортированы по ключу.</returns>
      <param name="source">Последовательность значений, которые следует упорядочить.</param>
      <param name="keySelector">Функция, извлекающая ключ из элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="keySelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderBy``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Сортирует элементы последовательности в порядке возрастания с использованием указанного компаратора.</summary>
      <returns>Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, элементы которого отсортированы по ключу.</returns>
      <param name="source">Последовательность значений, которые следует упорядочить.</param>
      <param name="keySelector">Функция, извлекающая ключ из элемента.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Сортирует элементы последовательности в порядке убывания ключа.</summary>
      <returns>Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, элементы которого отсортированы по ключу в порядке убывания.</returns>
      <param name="source">Последовательность значений, которые следует упорядочить.</param>
      <param name="keySelector">Функция, извлекающая ключ из элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="keySelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.OrderByDescending``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Сортирует элементы последовательности в порядке убывания с использованием указанного компаратора.</summary>
      <returns>Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, элементы которого отсортированы по ключу в порядке убывания.</returns>
      <param name="source">Последовательность значений, которые следует упорядочить.</param>
      <param name="keySelector">Функция, извлекающая ключ из элемента.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Reverse``1(System.Linq.IQueryable{``0})">
      <summary>Изменяет порядок элементов последовательности на противоположный.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого соответствуют элементам входной последовательности, но следуют в противоположном порядке.</returns>
      <param name="source">Последовательность значений, которые следует расставить в обратном порядке.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Проецирует каждый элемент последовательности в новую форму.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого получены в результате вызова функции проекции для каждого элемента последовательности <paramref name="source" />.</returns>
      <param name="source">Последовательность значений, которые следует проецировать.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Тип значения, возвращаемого функцией, заданной параметром <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Select``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,``1}})">
      <summary>Проецирует каждый элемент последовательности в новую форму, добавляя индекс элемента.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого получены в результате вызова функции проекции для каждого элемента последовательности <paramref name="source" />.</returns>
      <param name="source">Последовательность значений, которые следует проецировать.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Тип значения, возвращаемого функцией, заданной параметром <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Проецирует каждый элемент последовательности в объект <see cref="T:System.Collections.Generic.IEnumerable`1" /> и вызывает функцию селектора результата для каждого элемента.Результирующие значения из всех промежуточных последовательностей возвращаются объединенными в одну одномерную последовательность.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого получены в результате вызова функции проекции "один ко многим" <paramref name="collectionSelector" /> для каждого элемента последовательности <paramref name="source" /> и последующего сопоставления каждого элемента такой промежуточной последовательности и соответствующего ему элемента последовательности <paramref name="source" /> с результирующим элементом.</returns>
      <param name="source">Последовательность значений, которые следует проецировать.</param>
      <param name="collectionSelector">Функция проекции, применяемая к каждому элементу входной последовательности.</param>
      <param name="resultSelector">Функция проекции, применяемая к каждому элементу каждой промежуточной последовательности.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">Тип промежуточных элементов, собранных функцией, заданной параметром <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">Тип элементов результирующей последовательности.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="collectionSelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>Проецирует каждый элемент последовательности в объект <see cref="T:System.Collections.Generic.IEnumerable`1" /> и объединяет результирующие последовательности в одну последовательность.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого получены в результате вызова функции проекции "один ко многим" для каждого элемента входной последовательности.</returns>
      <param name="source">Последовательность значений, которые следует проецировать.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Тип элементов последовательности, возвращаемых функцией, заданной параметром <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``3(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Проецирует каждый элемент последовательности в объект <see cref="T:System.Collections.Generic.IEnumerable`1" />, включающий индекс исходного элемента, на основе которого он был создан.Для каждого элемента каждой промежуточной последовательности вызывается функция селектора результата, и результирующие значения возвращаются объединенными в одну одномерную последовательность.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого получены в результате вызова функции проекции "один ко многим" <paramref name="collectionSelector" /> для каждого элемента последовательности <paramref name="source" /> и последующего сопоставления каждого элемента такой промежуточной последовательности и соответствующего ему элемента последовательности <paramref name="source" /> с результирующим элементом.</returns>
      <param name="source">Последовательность значений, которые следует проецировать.</param>
      <param name="collectionSelector">Функция проекции, применяемая к каждому элементу входной последовательности; второй параметр этой функции представляет индекс исходного элемента.</param>
      <param name="resultSelector">Функция проекции, применяемая к каждому элементу каждой промежуточной последовательности.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TCollection">Тип промежуточных элементов, собранных функцией, заданной параметром <paramref name="collectionSelector" />.</typeparam>
      <typeparam name="TResult">Тип элементов результирующей последовательности.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="collectionSelector" /> или <paramref name="resultSelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SelectMany``2(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Collections.Generic.IEnumerable{``1}}})">
      <summary>Проецирует каждый элемент последовательности в объект <see cref="T:System.Collections.Generic.IEnumerable`1" /> и объединяет результирующие последовательности в одну последовательность.Индекс каждого элемента исходной последовательности используется в проецированной форме этого элемента.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого получены в результате вызова функции проекции "один ко многим" для каждого элемента входной последовательности.</returns>
      <param name="source">Последовательность значений, которые следует проецировать.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу; второй параметр этой функции представляет индекс исходного элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TResult">Тип элементов последовательности, возвращаемых функцией, заданной параметром <paramref name="selector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Определяет, совпадают ли две последовательности, используя для сравнения элементов компаратор проверки на равенство по умолчанию.</summary>
      <returns>true, если у двух исходных последовательностей одинаковая длина и соответствующие элементы совпадают, в противном случае — false.</returns>
      <param name="source1">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого сравниваются с элементами последовательности <paramref name="source2" />.</param>
      <param name="source2">Объект <see cref="T:System.Collections.Generic.IEnumerable`1" />, элементы которого сравниваются с элементами первой последовательности.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SequenceEqual``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Определяет, совпадают ли две последовательности, используя для сравнения элементов указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>true, если у двух исходных последовательностей одинаковая длина и соответствующие элементы совпадают, в противном случае — false.</returns>
      <param name="source1">Объект <see cref="T:System.Linq.IQueryable`1" />, элементы которого сравниваются с элементами последовательности <paramref name="source2" />.</param>
      <param name="source2">Объект <see cref="T:System.Collections.Generic.IEnumerable`1" />, элементы которого сравниваются с элементами первой последовательности.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения элементов.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает единственный элемент последовательности и генерирует исключение, если число элементов последовательности отлично от 1.</summary>
      <returns>Единственный элемент входной последовательности.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, единственный элемент которого требуется возвратить.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> имеет более одного элемента.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Single``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает единственный элемент последовательности, удовлетворяющий заданному условию, и генерирует исключение, если таких элементов больше одного.</summary>
      <returns>Единственный элемент входной последовательности, удовлетворяющий условию предиката <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить единственный элемент.</param>
      <param name="predicate">Функция для проверки элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Ни один элемент не удовлетворяет условию предиката <paramref name="predicate" />.– или –Условию предиката <paramref name="predicate" /> удовлетворяет более одного элемента.– или –Исходная последовательность пуста.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0})">
      <summary>Возвращает единственный элемент последовательности или значение по умолчанию, если последовательность пуста; если в последовательности более одного элемента, генерируется исключение.</summary>
      <returns>Единственный элемент входной последовательности или default(<paramref name="TSource" />), если в последовательности нет элементов.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, единственный элемент которого требуется возвратить.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="source" /> имеет более одного элемента.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SingleOrDefault``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает единственный элемент последовательности, удовлетворяющий заданному условию, или значение по умолчанию, если такого элемента не существует; если условию удовлетворяет более одного элемента, генерируется исключение.</summary>
      <returns>Единственный элемент входной последовательности, удовлетворяющий условию предиката <paramref name="predicate" />, или default(<paramref name="TSource" />), если такой элемент не найден.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить единственный элемент.</param>
      <param name="predicate">Функция для проверки элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
      <exception cref="T:System.InvalidOperationException">Условию предиката <paramref name="predicate" /> удовлетворяет более одного элемента.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Skip``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Пропускает заданное число элементов в последовательности и возвращает остальные элементы.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы из входной последовательности, начиная с указанного индекса.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элементы.</param>
      <param name="count">Число элементов, пропускаемых перед возвращением остальных элементов.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Пропускает элементы в последовательности, пока они удовлетворяют заданному условию, и затем возвращает оставшиеся элементы.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий цепочку элементов последовательности <paramref name="source" />, начиная с первого элемента, который не прошел проверку, заданную предикатом <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элементы.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.SkipWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Пропускает элементы в последовательности, пока они удовлетворяют заданному условию, и затем возвращает оставшиеся элементы.Индекс элемента используется в логике функции предиката.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий цепочку элементов последовательности <paramref name="source" />, начиная с первого элемента, который не прошел проверку, заданную предикатом <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, из которого требуется возвратить элементы.</param>
      <param name="predicate">Функция, применяемая к каждому элементу для проверки условия; второй параметр этой функции представляет индекс исходного элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Decimal})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Decimal" />.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Decimal" />, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Double})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Double" />.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Double" />, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int32})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Int32" />.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int32" />, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Int64})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Int64" />.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int64" />, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Decimal}})">
      <summary>Вычисляет сумму последовательности значений <see cref="T:System.Decimal" /> обнуляемого типа.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Decimal" /> обнуляемого типа, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Double}})">
      <summary>Вычисляет сумму последовательности значений <see cref="T:System.Double" /> обнуляемого типа.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Double" /> обнуляемого типа, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int32}})">
      <summary>Вычисляет сумму последовательности значений <see cref="T:System.Int32" /> обнуляемого типа.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int32" /> обнуляемого типа, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Int64}})">
      <summary>Вычисляет сумму последовательности значений <see cref="T:System.Int64" /> обнуляемого типа.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Int64" /> обнуляемого типа, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Nullable{System.Single}})">
      <summary>Вычисляет сумму последовательности значений <see cref="T:System.Single" /> обнуляемого типа.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Single" /> обнуляемого типа, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum(System.Linq.IQueryable{System.Single})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Single" />.</summary>
      <returns>Сумма последовательности значений.</returns>
      <param name="source">Последовательность значений <see cref="T:System.Single" />, сумму которых требуется вычислить.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Decimal}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Decimal" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Double}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Double" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Int32" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int64}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Int64" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Decimal}}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Decimal" /> (допускающей значения NULL), получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Double}}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Double" /> (допускающей значения NULL), получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int32}}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Int32" /> (допускающей значения NULL), получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Int64}}})">
      <summary>Вычисляет сумму последовательности значений <see cref="T:System.Int64" /> обнуляемого типа, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
      <exception cref="T:System.OverflowException">Сумма больше значения <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Nullable{System.Single}}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Single" /> (допускающей значения NULL), получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Sum``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Single}})">
      <summary>Вычисляет сумму последовательности значений типа <see cref="T:System.Single" />, получаемой в результате применения функции проекции к каждому элементу входной последовательности.</summary>
      <returns>Сумма проецированных значений.</returns>
      <param name="source">Последовательность значений типа <paramref name="TSource" />.</param>
      <param name="selector">Функция проекции, применяемая к каждому элементу.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="selector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Take``1(System.Linq.IQueryable{``0},System.Int32)">
      <summary>Возвращает указанное число подряд идущих элементов с начала последовательности.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий заданное число элементов с начала последовательности <paramref name="source" />.</returns>
      <param name="source">Последовательность, из которой требуется возвратить элементы.</param>
      <param name="count">Число возвращаемых элементов.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="source" /> имеет значение null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Возвращает цепочку элементов последовательности, удовлетворяющих указанному условию.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы входной последовательности до первого элемента, который не прошел проверку, заданную предикатом <paramref name="predicate" />.</returns>
      <param name="source">Последовательность, из которой требуется возвратить элементы.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.TakeWhile``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Возвращает цепочку элементов последовательности, удовлетворяющих указанному условию.Индекс элемента используется в логике функции предиката.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы входной последовательности до первого элемента, который не прошел проверку, заданную предикатом <paramref name="predicate" />.</returns>
      <param name="source">Последовательность, из которой требуется возвратить элементы.</param>
      <param name="predicate">Функция, применяемая к каждому элементу для проверки условия; второй параметр этой функции представляет индекс элемента в исходной последовательности.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Выполняет дополнительное упорядочение элементов последовательности в порядке возрастания ключа.</summary>
      <returns>Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, элементы которого отсортированы по ключу.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, содержащий сортируемые элементы.</param>
      <param name="keySelector">Функция, извлекающая ключ из каждого элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="keySelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenBy``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Выполняет дополнительное упорядочение элементов последовательности в порядке возрастания с использованием указанного компаратора.</summary>
      <returns>Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, элементы которого отсортированы по ключу.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, содержащий сортируемые элементы.</param>
      <param name="keySelector">Функция, извлекающая ключ из каждого элемента.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}})">
      <summary>Выполняет дополнительное упорядочение элементов последовательности в порядке убывания ключа.</summary>
      <returns>Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, элементы которого отсортированы по ключу в порядке убывания.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, содержащий сортируемые элементы.</param>
      <param name="keySelector">Функция, извлекающая ключ из каждого элемента.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией, заданной параметром <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="keySelector" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.ThenByDescending``2(System.Linq.IOrderedQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,``1}},System.Collections.Generic.IComparer{``1})">
      <summary>Выполняет дополнительное упорядочение элементов последовательности в порядке убывания с использованием указанного компаратора.</summary>
      <returns>Коллекция, элементы которой отсортированы по ключу в порядке убывания.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IOrderedQueryable`1" />, содержащий сортируемые элементы.</param>
      <param name="keySelector">Функция, извлекающая ключ из каждого элемента.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IComparer`1" />, используемый для сравнения ключей.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <typeparam name="TKey">Тип ключа, возвращаемого функцией <paramref name="keySelector" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" />, <paramref name="keySelector" /> или <paramref name="comparer" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0})">
      <summary>Находит объединение множеств, представленных двумя последовательностями, используя для сравнения значений компаратор проверки на равенство по умолчанию.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, который содержит элементы, имеющиеся в обеих входных последовательностях, кроме дубликатов.</returns>
      <param name="source1">Последовательность, различающиеся элементы которой образуют первое множество для операции объединения.</param>
      <param name="source2">Последовательность, различающиеся элементы которой образуют второе множество для операции объединения.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Union``1(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``0},System.Collections.Generic.IEqualityComparer{``0})">
      <summary>Находит объединение множеств, представленных двумя последовательностями, используя указанный компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, который содержит элементы, имеющиеся в обеих входных последовательностях, кроме дубликатов.</returns>
      <param name="source1">Последовательность, различающиеся элементы которой образуют первое множество для операции объединения.</param>
      <param name="source2">Последовательность, различающиеся элементы которой образуют второе множество для операции объединения.</param>
      <param name="comparer">Компаратор <see cref="T:System.Collections.Generic.IEqualityComparer`1" />, используемый для сравнения значений.</param>
      <typeparam name="TSource">Тип элементов входных последовательностей.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Boolean}})">
      <summary>Выполняет фильтрацию последовательности значений на основе заданного предиката.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы входной последовательности, которые удовлетворяют условию, заданному предикатом <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, подлежащий фильтрации.</param>
      <param name="predicate">Функция для проверки каждого элемента на соответствие условию.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Where``1(System.Linq.IQueryable{``0},System.Linq.Expressions.Expression{System.Func{``0,System.Int32,System.Boolean}})">
      <summary>Выполняет фильтрацию последовательности значений на основе заданного предиката.Индекс каждого элемента используется в логике функции предиката.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий элементы входной последовательности, которые удовлетворяют условию, заданному предикатом <paramref name="predicate" />.</returns>
      <param name="source">Объект <see cref="T:System.Linq.IQueryable`1" />, подлежащий фильтрации.</param>
      <param name="predicate">Функция, применяемая к каждому элементу для проверки условия; второй параметр этой функции представляет индекс элемента в исходной последовательности.</param>
      <typeparam name="TSource">Тип элементов последовательности <paramref name="source" />.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source" /> или <paramref name="predicate" /> — null.</exception>
    </member>
    <member name="M:System.Linq.Queryable.Zip``3(System.Linq.IQueryable{``0},System.Collections.Generic.IEnumerable{``1},System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
      <summary>Объединяет две последовательности, используя указанную функцию предиката.</summary>
      <returns>Объект <see cref="T:System.Linq.IQueryable`1" />, содержащий объединенные элементы двух входных последовательностей.</returns>
      <param name="source1">Первая последовательность для объединения.</param>
      <param name="source2">Вторая последовательность для объединения.</param>
      <param name="resultSelector">Функция, которая определяет, как объединить элементы двух последовательностей.</param>
      <typeparam name="TFirst">Тип элементов первой входной последовательности.</typeparam>
      <typeparam name="TSecond">Тип элементов второй входной последовательности.</typeparam>
      <typeparam name="TResult">Тип элементов результирующей последовательности.</typeparam>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="source1" /> или <paramref name="source2 " />— null.</exception>
    </member>
  </members>
</doc>