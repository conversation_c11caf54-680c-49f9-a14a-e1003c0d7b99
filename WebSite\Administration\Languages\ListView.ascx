<%@ Control CodeBehind="ListView.ascx.cs" Language="c#" AutoEventWireup="false" Inherits="SplendidCRM.Administration.Languages.ListView" %>
<script runat="server">
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005-2007 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
</script>
<div id="divListView">
	<%@ Register TagPrefix="SplendidCRM" Tagname="ModuleHeader" Src="~/_controls/ModuleHeader.ascx" %>
	<SplendidCRM:ModuleHeader ID="ctlModuleHeader" Module="Administration" Title="Administration.LBL_MANAGE_LANGUAGES" EnablePrint="true" HelpName="index" EnableHelp="true" Runat="Server" />
	<%@ Register TagPrefix="SplendidCRM" Tagname="ListHeader" Src="~/_controls/ListHeader.ascx" %>

	<asp:Panel CssClass="button-panel" Visible="<%# !PrintView %>" runat="server">
		<asp:Button ID="btnAdd"    OnClientClick="window.location.href='edit.aspx'; return false;"       CssClass="button" Text='<%# "  " + L10n.Term(".LBL_ADD_BUTTON_LABEL"   ) + "  " %>' ToolTip='<%# L10n.Term(".LBL_ADD_BUTTON_TITLE"   ) %>' Runat="server" />
		<asp:Button ID="btnCancel" OnClientClick="window.location.href='../default.aspx'; return false;" CssClass="button" Text='<%# "  " + L10n.Term(".LBL_CANCEL_BUTTON_LABEL") + "  " %>' ToolTip='<%# L10n.Term(".LBL_CANCEL_BUTTON_TITLE") %>' Runat="server" />
		<asp:Label ID="lblError" CssClass="error" EnableViewState="false" Runat="server" />
	</asp:Panel>
	
	<SplendidCRM:SplendidGrid id="grdMain" AllowPaging="false" AllowSorting="false" EnableViewState="true" runat="server">
		<Columns>
			<asp:BoundColumn    HeaderText="Terminology.LBL_LIST_LANG"         DataField="NAME"         ItemStyle-Width="20%" />
			<asp:BoundColumn    HeaderText="Terminology.LBL_LIST_NAME_NAME"    DataField="DISPLAY_NAME" ItemStyle-Width="30%" />
			<asp:BoundColumn    HeaderText="Terminology.LBL_LIST_DISPLAY_NAME" DataField="NATIVE_NAME"  ItemStyle-Width="30%" />
			<asp:TemplateColumn HeaderText="Administration.LNK_ENABLED" ItemStyle-Width="5%" ItemStyle-Wrap="false">
				<ItemTemplate>
					<asp:Label Visible='<%#  Sql.ToBoolean(Eval("ACTIVE")) %>' Text='<%# L10n.Term(".LBL_YES") %>' Runat="server" />
					<asp:Label Visible='<%# !Sql.ToBoolean(Eval("ACTIVE")) %>' Text='<%# L10n.Term(".LBL_NO" ) %>' Runat="server" />
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn HeaderText="" ItemStyle-Width="10%" ItemStyle-Wrap="false">
				<ItemTemplate>
					<asp:ImageButton CommandName="Languages.Disable"  Visible='<%# (Sql.ToString(Eval("NAME")) != "en-US") &&  Sql.ToBoolean(Eval("ACTIVE")) %>' CommandArgument='<%# Eval("NAME") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_DISABLE") %>' SkinID="minus_inline" Runat="server" />
					<asp:LinkButton  CommandName="Languages.Disable"  Visible='<%# (Sql.ToString(Eval("NAME")) != "en-US") &&  Sql.ToBoolean(Eval("ACTIVE")) %>' CommandArgument='<%# Eval("NAME") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_DISABLE"         ) %>' Runat="server" />
					<asp:ImageButton CommandName="Languages.Enable"   Visible='<%# (Sql.ToString(Eval("NAME")) != "en-US") && !Sql.ToBoolean(Eval("ACTIVE")) %>' CommandArgument='<%# Eval("NAME") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term("Administration.LNK_ENABLE" ) %>' SkinID="plus_inline" Runat="server" />
					<asp:LinkButton  CommandName="Languages.Enable"   Visible='<%# (Sql.ToString(Eval("NAME")) != "en-US") && !Sql.ToBoolean(Eval("ACTIVE")) %>' CommandArgument='<%# Eval("NAME") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term("Administration.LNK_ENABLE"          ) %>' Runat="server" />
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn HeaderText="" ItemStyle-Width="1%" ItemStyle-HorizontalAlign="Left" ItemStyle-Wrap="false">
				<ItemTemplate>
					<span onclick="return confirm('<%= L10n.TermJavaScript(".NTC_DELETE_CONFIRMATION") %>')">
						<asp:ImageButton CommandName="Languages.Delete" CommandArgument='<%# Eval("NAME") %>' Visible='<%# (Sql.ToString(Eval("NAME")) != "en-US") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" AlternateText='<%# L10n.Term(".LNK_DELETE") %>' SkinID="delete_inline" Runat="server" />
						<asp:LinkButton  CommandName="Languages.Delete" CommandArgument='<%# Eval("NAME") %>' Visible='<%# (Sql.ToString(Eval("NAME")) != "en-US") %>' OnCommand="Page_Command" CssClass="listViewTdToolsS1" Text='<%# L10n.Term(".LNK_DELETE") %>' Runat="server" />
					</span>
				</ItemTemplate>
			</asp:TemplateColumn>
		</Columns>
	</SplendidCRM:SplendidGrid>

	<%@ Register TagPrefix="SplendidCRM" Tagname="DumpSQL" Src="~/_controls/DumpSQL.ascx" %>
	<SplendidCRM:DumpSQL ID="ctlDumpSQL" Visible="<%# !PrintView %>" Runat="Server" />
</div>
