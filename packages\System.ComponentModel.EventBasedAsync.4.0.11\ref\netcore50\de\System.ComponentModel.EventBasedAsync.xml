﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>Stellt Daten für das MethodennameCompleted-Ereignis bereit.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />-Klasse. </summary>
      <param name="error">Ein während des asynchronen Vorgangs aufgetretener Fehler.</param>
      <param name="cancelled">Ein Wert, der angibt, ob der asynchrone Vorgang abgebrochen wurde.</param>
      <param name="userState">Das an die <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />-<PERSON><PERSON> übergebene, optionale benutzerdefinierte Zustandsobjekt.</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>Ruft einen Wert ab, der angibt, ob ein asynchroner Vorgang abgebrochen wurde.</summary>
      <returns>true, wenn der Hintergrundvorgang abgebrochen wurde, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>Ruft einen Wert ab, der angibt, welcher Fehler während eines asynchronen Vorgangs aufgetreten ist.</summary>
      <returns>Eine <see cref="T:System.Exception" />-Instanz, wenn während eines asynchronen Vorgangs ein Fehler aufgetreten ist, andernfalls null.</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>Löst eine benutzerdefinierte Ausnahme aus, wenn bei einem asynchronen Vorgang ein Fehler aufgetreten ist.</summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" />-Eigenschaft ist true. </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">Die <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />-Eigenschaft wurde durch den asynchronen Vorgang festgelegt.Die <see cref="P:System.Exception.InnerException" />-Eigenschaft enthält einen Verweis auf <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>Ruft den eindeutigen Bezeichner der asynchronen Aufgabe ab.</summary>
      <returns>Ein Objektverweis, der die asynchrone Aufgabe eindeutig bezeichnet, andernfalls null, wenn kein Wert festgelegt wurde.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>Stellt die Methode dar, die das MethodennameCompleted-Ereignis eines asynchronen Vorgangs behandelt.</summary>
      <param name="sender">Die Quelle des Ereignisses. </param>
      <param name="e">Ein <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />, das die Ereignisdaten enthält. </param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>Verfolgt die Lebensdauer eines asynchronen Vorgangs.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>Beendet den asynchronen Vorgang.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>Beendet die Lebensdauer eines asynchronen Vorgangs.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> wurde vorher für diese Aufgabe aufgerufen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Ruft einen Delegaten auf dem Thread oder Kontext auf, der für das Anwendungsmodell geeignet ist.</summary>
      <param name="d">Ein <see cref="T:System.Threading.SendOrPostCallback" />-Objekt, das den am Ende des Vorgangs aufzurufenden Delegaten umschließt. </param>
      <param name="arg">Ein Argument für den Delegaten, der im <paramref name="d" />-Parameter enthalten ist. </param>
      <exception cref="T:System.InvalidOperationException">Die <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" />-Methode wurde zuvor für diese Aufgabe aufgerufen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> ist null. </exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Beendet die Lebensdauer eines asynchronen Vorgangs.</summary>
      <param name="d">Ein <see cref="T:System.Threading.SendOrPostCallback" />-Objekt, das den am Ende des Vorgangs aufzurufenden Delegaten umschließt. </param>
      <param name="arg">Ein Argument für den Delegaten, der im <paramref name="d" />-Parameter enthalten ist. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> wurde vorher für diese Aufgabe aufgerufen. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> ist null. </exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>Ruft das <see cref="T:System.Threading.SynchronizationContext" />-Objekt ab, das an den Konstruktor übergeben wurde.</summary>
      <returns>Das <see cref="T:System.Threading.SynchronizationContext" />-Objekt, das an den Konstruktor übergeben wurde.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>Ruft ein Objekt ab, das für die eindeutige Identifikation eines asynchronen Vorgangs verwendet wird, oder legt dieses fest.</summary>
      <returns>Das Zustandsobjekt, das an den asynchronen Methodenaufruf übergeben wurde.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>Stellt Parallelitätsverwaltung für Klassen bereit, die asynchrone Methodenaufrufe unterstützen.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>Gibt für die Überwachung der Dauer eines bestimmten asynchronen Vorgangs eine <see cref="T:System.ComponentModel.AsyncOperation" /> zurück.</summary>
      <returns>Eine <see cref="T:System.ComponentModel.AsyncOperation" />, die Sie verwenden können, um die Dauer eines asynchronen Methodenaufrufs zu verfolgen.</returns>
      <param name="userSuppliedState">Ein Objekt, das dazu verwendet wird, einem bestimmten asynchronen Vorgang einen Bestandteil des Clientzustands, z. B. eine Aufgaben-ID, zuzuordnen. </param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>Ruft den Synchronisierungskontext für den asynchronen Vorgang ab oder legt diesen fest.</summary>
      <returns>Der Synchronisierungskontext für den asynchronen Vorgang.</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>Führt einen Vorgang für einen getrennten Thread aus.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.ComponentModel.BackgroundWorker" />-Klasse.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>Fordert das Abbrechen eines anstehenden Hintergrundvorgangs an.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> ist false. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>Ruft einen Wert ab, der angibt, ob die Anwendung den Abbruch eines Hintergrundvorgangs angefordert hat.</summary>
      <returns>true, wenn die Anwendung den Abbruch eines Hintergrundvorgangs angefordert hat, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>Tritt ein, wenn <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" /> aufgerufen wird.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>Ruft einen Wert ab, der angibt, ob <see cref="T:System.ComponentModel.BackgroundWorker" /> einen asynchronen Vorgang ausführt.</summary>
      <returns>true, wenn der <see cref="T:System.ComponentModel.BackgroundWorker" /> einen asynchronen Vorgang ausführt, andernfalls false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>Löst das <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />-Ereignis aus. </summary>
      <param name="e">Eine <see cref="T:System.EventArgs" />-Klasse, die die Ereignisdaten enthält.</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>Löst das <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />-Ereignis aus.</summary>
      <param name="e">Eine <see cref="T:System.EventArgs" />-Klasse, die die Ereignisdaten enthält. </param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>Löst das <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" />-Ereignis aus.</summary>
      <param name="e">Eine <see cref="T:System.EventArgs" />-Klasse, die die Ereignisdaten enthält. </param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>Tritt ein, wenn <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" /> aufgerufen wird.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>Löst das <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />-Ereignis aus.</summary>
      <param name="percentProgress">Bereits abgeschlossener Teil des Hintergrundvorgangs in Prozent (1-100). </param>
      <exception cref="T:System.InvalidOperationException">Die <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" />-Eigenschaft ist auf false festgelegt. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>Löst das <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />-Ereignis aus.</summary>
      <param name="percentProgress">Bereits abgeschlossener Teil des Hintergrundvorgangs in Prozent (1-100).</param>
      <param name="userState">Das an <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> übergebene Zustandsobjekt.</param>
      <exception cref="T:System.InvalidOperationException">Die <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" />-Eigenschaft ist auf false festgelegt. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>Startet die Ausführung eines Hintergrundvorgangs.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> ist true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>Startet die Ausführung eines Hintergrundvorgangs.</summary>
      <param name="argument">Ein Parameter, der von dem Hintergrundvorgang verwendet wird, der im <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />-Ereignishandler ausgeführt wird. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> ist true. </exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>Tritt ein, wenn der Hintergrundvorgang entweder abgeschlossen ist, abgebrochen wurde oder eine Ausnahme ausgelöst hat.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.ComponentModel.BackgroundWorker" /> Fortschrittsaktualisierungen melden kann, oder legt diesen Wert fest.</summary>
      <returns>true, wenn der <see cref="T:System.ComponentModel.BackgroundWorker" /> Fortschrittsaktualisierungen unterstützt, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.ComponentModel.BackgroundWorker" /> asynchrone Abbrüche unterstützt, oder legt diesen Wert fest.</summary>
      <returns>true, wenn <see cref="T:System.ComponentModel.BackgroundWorker" /> Abbrüche unterstützt, andernfalls false.Die Standardeinstellung ist false.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>Stellt Daten für den <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />-Ereignishandler bereit.</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.ComponentModel.DoWorkEventArgs" />-Klasse.</summary>
      <param name="argument">Gibt ein Argument für einen asynchronen Vorgang an.</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>Ruft einen Wert ab, der das Argument eines asynchronen Vorgangs darstellt.</summary>
      <returns>Ein <see cref="T:System.Object" />, das das Argument eines asynchronen Vorgangs darstellt.</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>Ruft einen Wert ab, der das Ergebnis eines asynchronen Vorgangs darstellt, oder legt diesen fest.</summary>
      <returns>Ein <see cref="T:System.Object" />, das das Ergebnis eines asynchronen Vorgangs darstellt.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>Stellt die Methode für die Behandlung des <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />-Ereignisses dar.Diese Klasse kann nicht vererbt werden.</summary>
      <param name="sender">Die Quelle des Ereignisses.</param>
      <param name="e">Ein <see cref="T:System.ComponentModel.DoWorkEventArgs" />, das die Ereignisdaten enthält.</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>Stellt Daten für das <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />-Ereignis bereit.</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />-Klasse.</summary>
      <param name="progressPercentage">Gibt an, wieviel Prozent einer asynchronen Aufgabe ausgeführt wurden.</param>
      <param name="userState">Ein eindeutiger Benutzerzustand.</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>Ruft den Fortschrittsprozentsatz einer asynchronen Aufgabe ab.</summary>
      <returns>Ein Prozentwert, der den Fortschritt der asynchronen Aufgabe angibt.</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>Ruft einen eindeutigen Benutzerzustand ab.</summary>
      <returns>Ein eindeutiges <see cref="T:System.Object" />, das den Benutzerzustand angibt.</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>Stellt die Methode dar, die das <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />-Ereignis der <see cref="T:System.ComponentModel.BackgroundWorker" />-Klasse behandelt.Diese Klasse kann nicht vererbt werden.</summary>
      <param name="sender">Die Quelle des Ereignisses. </param>
      <param name="e">Ein <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />, das die Ereignisdaten enthält. </param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>Stellt Daten für das MethodennameCompleted-Ereignis bereit.</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />-Klasse.</summary>
      <param name="result">Das Ergebnis eines asynchronen Vorgangs.</param>
      <param name="error">Ein während des asynchronen Vorgangs aufgetretener Fehler.</param>
      <param name="cancelled">Ein Wert, der angibt, ob der asynchrone Vorgang abgebrochen wurde.</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>Ruft einen Wert ab, der das Ergebnis eines asynchronen Vorgangs darstellt.</summary>
      <returns>Ein <see cref="T:System.Object" />, das das Ergebnis eines asynchronen Vorgangs darstellt.</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> ist nicht null.Die <see cref="P:System.Exception.InnerException" />-Eigenschaft enthält einen Verweis auf <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> ist true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>Ruft einen Wert ab, der den Benutzerzustand darstellt.</summary>
      <returns>Ein <see cref="T:System.Object" />, das den Benutzerzustand darstellt.</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>Stellt die Methode dar, die das <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" />-Ereignis einer <see cref="T:System.ComponentModel.BackgroundWorker" />-Klasse behandelt.</summary>
      <param name="sender">Die Quelle des Ereignisses. </param>
      <param name="e">Ein <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />, das die Ereignisdaten enthält. </param>
    </member>
  </members>
</doc>