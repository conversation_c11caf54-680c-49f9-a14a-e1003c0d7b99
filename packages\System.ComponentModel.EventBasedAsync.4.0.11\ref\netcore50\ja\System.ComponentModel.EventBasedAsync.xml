﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>MethodNameCompleted イベントのデータを提供します。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>
        <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="error">非同期操作中に発生したエラー。</param>
      <param name="cancelled">非同期操作がキャンセルされたかどうかを示す値。</param>
      <param name="userState">
        <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> メソッドに渡される、オプションのユーザー指定の状態オブジェクト。</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>非同期操作がキャンセルされたかどうかを示す値を取得します。</summary>
      <returns>バックグラウンドでの操作がキャンセルされた場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>非同期操作中に発生したエラーを示す値を取得します。</summary>
      <returns>非同期操作中にエラーが発生した場合は <see cref="T:System.Exception" /> インスタンス。それ以外の場合は null。</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>非同期操作が失敗した場合は、ユーザー指定の例外を発生させます。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> プロパティが true である。</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> プロパティは、非同期操作によって設定されています。<see cref="P:System.Exception.InnerException" /> プロパティは、<see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> への参照を保持します。</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>非同期タスクの一意の識別子を取得します。</summary>
      <returns>非同期タスクを一意に識別するオブジェクト参照。値が設定されていない場合は null。</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>非同期操作の MethodNameCompleted イベントを処理するメソッドを表します。</summary>
      <param name="sender">イベントのソース。</param>
      <param name="e">イベント データを格納している <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>非同期操作の有効期間を追跡します。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>非同期操作の終了処理を行います。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>非同期操作の有効期間を終了します。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> は、このタスクに対して既に呼び出されています。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>アプリケーション モデルに適したスレッドまたはコンテキストでデリゲートを呼び出します。</summary>
      <param name="d">操作終了時に呼び出されるデリゲートをラップする <see cref="T:System.Threading.SendOrPostCallback" /> オブジェクト。</param>
      <param name="arg">
        <paramref name="d" /> パラメーターに格納されているデリゲートの引数。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> メソッドは、このタスクに対して既に呼び出されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> は null です。</exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>非同期操作の有効期間を終了します。</summary>
      <param name="d">操作終了時に呼び出されるデリゲートをラップする <see cref="T:System.Threading.SendOrPostCallback" /> オブジェクト。</param>
      <param name="arg">
        <paramref name="d" /> パラメーターに格納されているデリゲートの引数。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> は、このタスクに対して既に呼び出されています。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> は null です。</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>コンストラクターに渡された <see cref="T:System.Threading.SynchronizationContext" /> オブジェクトを取得します。</summary>
      <returns>コンストラクターに渡された <see cref="T:System.Threading.SynchronizationContext" /> オブジェクト。</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>非同期操作を一意に識別するために使用するオブジェクトを取得または設定します。</summary>
      <returns>非同期メソッド呼び出しに渡される状態オブジェクト。</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>非同期メソッド呼び出しをサポートするクラスに同時実行管理機能を提供します。このクラスは継承できません。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>特定の非同期操作の存続期間を追跡するために使用する <see cref="T:System.ComponentModel.AsyncOperation" /> を返します。</summary>
      <returns>非同期メソッド呼び出しの存続期間を追跡するために使用できる <see cref="T:System.ComponentModel.AsyncOperation" />。</returns>
      <param name="userSuppliedState">クライアント状態の一部 (タスク ID など) を特定の非同期操作に関連付けるために使用されるオブジェクト。</param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>非同期操作の同期コンテキストを取得または設定します。</summary>
      <returns>非同期操作の同期コンテキスト。</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>別のスレッドで操作を実行します。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>保留中のバックグラウンド操作のキャンセルを要求します。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> は false なので、</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>アプリケーションがバックグラウンド操作のキャンセルを要求したかどうかを示す値を取得します。</summary>
      <returns>アプリケーションがバックグラウンド操作のキャンセルを要求した場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>
        <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" /> が呼び出されたときに発生します。</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> が非同期操作を実行中かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> が非同期操作を実行中の場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> イベントを発生させます。</summary>
      <param name="e">イベント データを格納している <see cref="T:System.EventArgs" />。</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> イベントを発生させます。</summary>
      <param name="e">イベント データを格納している <see cref="T:System.EventArgs" />。</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> イベントを発生させます。</summary>
      <param name="e">イベント データを格納している <see cref="T:System.EventArgs" />。</param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>
        <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" /> が呼び出されたときに発生します。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> イベントを発生させます。</summary>
      <param name="percentProgress">完了しているバックグラウンド操作の比率 (0 ～ 100%)。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> プロパティは false に設定されています。</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> イベントを発生させます。</summary>
      <param name="percentProgress">完了しているバックグラウンド操作の比率 (0 ～ 100%)。</param>
      <param name="userState">
        <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> に渡される状態オブジェクト。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> プロパティは false に設定されています。</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>バックグラウンド操作の実行を開始します。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> は true なので、</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>バックグラウンド操作の実行を開始します。</summary>
      <param name="argument">
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> イベント ハンドラーで実行されるバックグラウンド操作で使用するパラメーター。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> は true なので、</exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>バックグラウンド操作の完了時、キャンセル時、またはバックグラウンド操作によって例外が発生したときに発生します。</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> が進行状況の更新を報告できるかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> が進捗状況の更新をサポートしている場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> が非同期のキャンセルをサポートしているかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> がキャンセルをサポートしている場合は true。それ以外の場合は false。既定値は、false です。</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> イベント ハンドラーのデータを提供します。</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>
        <see cref="T:System.ComponentModel.DoWorkEventArgs" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="argument">非同期操作の引数を指定します。</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>非同期操作の引数を表す値を取得します。</summary>
      <returns>非同期操作の引数を表す <see cref="T:System.Object" />。</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>非同期操作の結果を表す値を取得または設定します。</summary>
      <returns>非同期操作の結果を表す <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> イベントを処理するメソッドを表します。このクラスは継承できません。</summary>
      <param name="sender">イベントのソース。</param>
      <param name="e">イベント データを格納している <see cref="T:System.ComponentModel.DoWorkEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>
        <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> イベントにデータを提供します。</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>
        <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="progressPercentage">非同期タスクが完了した割合。</param>
      <param name="userState">一意のユーザー状態。</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>非同期タスクの進行状況の割合を取得します。</summary>
      <returns>非同期タスクの進行状況を示す割合値。</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>一意のユーザー状態を取得します。</summary>
      <returns>ユーザー状態を示す一意の <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> クラスの <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> イベントを処理するメソッドを表します。このクラスは継承できません。</summary>
      <param name="sender">イベントのソース。</param>
      <param name="e">イベント データを格納している <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>MethodNameCompleted イベントのデータを提供します。</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>
        <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="result">非同期操作の結果。</param>
      <param name="error">非同期操作中に発生したエラー。</param>
      <param name="cancelled">非同期操作がキャンセルされたかどうかを示す値。</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>非同期操作の結果を表す値を取得します。</summary>
      <returns>非同期操作の結果を表す <see cref="T:System.Object" />。</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> が null ではありません。<see cref="P:System.Exception.InnerException" /> プロパティは、<see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> への参照を保持します。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> は true なので、</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>ユーザーの状態を表す値を取得します。</summary>
      <returns>ユーザーの状態を表す <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>
        <see cref="T:System.ComponentModel.BackgroundWorker" /> クラスの <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> イベントを処理するメソッドを表します。</summary>
      <param name="sender">イベントのソース。</param>
      <param name="e">イベント データを格納している <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />。</param>
    </member>
  </members>
</doc>