﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>Fournit des données pour l'événement NomMéthodeCompleted.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />. </summary>
      <param name="error">Toute erreur qui s'est produite pendant l'opération asynchrone.</param>
      <param name="cancelled">Valeur qui indique si l'opération asynchrone a été annulée.</param>
      <param name="userState">L'objet d'état fourni par l'utilisateur facultatif est passé à la méthode <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />.</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>Obtient une valeur qui indique si une opération asynchrone a été annulée.</summary>
      <returns>true si l'opération d'arrière-plan a été annulée ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>Obtient une valeur qui indique quelle erreur s'est produite pendant une opération asynchrone.</summary>
      <returns>Une instance de <see cref="T:System.Exception" />, si une erreur s'est produite au cours d'une opération asynchrone ; sinon, null.</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>Lève une exception fournie par l'utilisateur en cas d'échec d'une opération asynchrone.</summary>
      <exception cref="T:System.InvalidOperationException">La propriété <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> est true. </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">La propriété <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> a été définie par l'opération asynchrone.La propriété <see cref="P:System.Exception.InnerException" /> contient une référence à <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>Obtient l'identificateur unique de la tâche asynchrone.</summary>
      <returns>Référence d'objet qui identifie uniquement la tâche asynchrone ; sinon, null si aucune valeur n'a été définie.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>Représente la méthode qui gère l'événement NomMéthodeCompleted d'une opération asynchrone.</summary>
      <param name="sender">Source de l'événement. </param>
      <param name="e">
        <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> qui contient les données de l'événement. </param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>Suit la durée de vie d'une opération asynchrone.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>Finalise l'opération asynchrone.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>Termine la durée de vie d'une opération asynchrone.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> a été appelé précédemment pour cette tâche. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Appelle un délégué sur le thread ou le contexte approprié pour le modèle d'application.</summary>
      <param name="d">Objet <see cref="T:System.Threading.SendOrPostCallback" /> qui inclut dans un wrapper le délégué à appeler lorsque l'opération se termine. </param>
      <param name="arg">Argument pour le délégué contenu dans le paramètre <paramref name="d" />. </param>
      <exception cref="T:System.InvalidOperationException">La méthode <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> a été appelée précédemment pour cette tâche. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> a la valeur null. </exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Termine la durée de vie d'une opération asynchrone.</summary>
      <param name="d">Objet <see cref="T:System.Threading.SendOrPostCallback" /> qui inclut dans un wrapper le délégué à appeler lorsque l'opération se termine. </param>
      <param name="arg">Argument pour le délégué contenu dans le paramètre <paramref name="d" />. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> a été appelé précédemment pour cette tâche. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> a la valeur null. </exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>Obtient l'objet <see cref="T:System.Threading.SynchronizationContext" /> passé au constructeur.</summary>
      <returns>Objet <see cref="T:System.Threading.SynchronizationContext" /> passé au constructeur.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>Obtient ou définit un objet servant à identifier de manière unique une opération asynchrone.</summary>
      <returns>Objet d'état passé à l'appel de méthode asynchrone.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>Fournit la gestion de l'accès concurrentiel pour les classes qui prennent en charge des appels de méthode asynchrone.Cette classe ne peut pas être héritée.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>Retourne <see cref="T:System.ComponentModel.AsyncOperation" /> permettant de suivre la durée d'une opération asynchrone particulière.</summary>
      <returns>
        <see cref="T:System.ComponentModel.AsyncOperation" /> que vous pouvez utiliser pour suivre la durée d'un appel de méthode asynchrone.</returns>
      <param name="userSuppliedState">Objet utilisé pour associer une portion d'état client, telle qu'un identificateur de tâche, à une opération asynchrone particulière. </param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>Obtient ou définit le contexte de synchronisation pour l'opération asynchrone.</summary>
      <returns>Contexte de synchronisation pour l'opération asynchrone.</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>Exécute une opération sur un thread séparé.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.BackgroundWorker" />.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>Demande l'annulation d'une opération d'arrière-plan en attente.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> a la valeur false. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>Obtient une valeur qui indique si l'application a demandé l'annulation d'une opération d'arrière-plan.</summary>
      <returns>true si l'application a demandé l'annulation d'une opération d'arrière-plan ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>Se produit lorsque <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" /> est appelée.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>Obtient une valeur qui indique si <see cref="T:System.ComponentModel.BackgroundWorker" /> exécute une opération asynchrone.</summary>
      <returns>true, si <see cref="T:System.ComponentModel.BackgroundWorker" /> exécute une opération asynchrone ; sinon, false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>Déclenche l'événement <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />. </summary>
      <param name="e">
        <see cref="T:System.EventArgs" /> qui contient les données de l'événement.</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>Déclenche l'événement <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="e">
        <see cref="T:System.EventArgs" /> qui contient les données de l'événement. </param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>Déclenche l'événement <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" />.</summary>
      <param name="e">
        <see cref="T:System.EventArgs" /> qui contient les données de l'événement. </param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>Se produit lorsque <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" /> est appelée.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>Déclenche l'événement <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="percentProgress">Pourcentage, de 0 à 100, de l'opération d'arrière-plan qui est terminé. </param>
      <exception cref="T:System.InvalidOperationException">La propriété <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> a pour valeur false. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>Déclenche l'événement <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="percentProgress">Pourcentage, de 0 à 100, de l'opération d'arrière-plan qui est terminé.</param>
      <param name="userState">L'objet d'état est passé à <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />.</param>
      <exception cref="T:System.InvalidOperationException">La propriété <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> a pour valeur false. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>Démarre l'exécution d'une opération d'arrière-plan.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> a la valeur true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>Démarre l'exécution d'une opération d'arrière-plan.</summary>
      <param name="argument">Paramètre disponible pour une exécution par l'opération d'arrière-plan dans le gestionnaire d'événements <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />. </param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> a la valeur true. </exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>Se produit lorsque l'opération d'arrière-plan est terminée, a été annulée ou a levé une exception.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>Obtient ou définit une valeur qui indique si <see cref="T:System.ComponentModel.BackgroundWorker" /> peut signaler des mises à jour de progression.</summary>
      <returns>true si <see cref="T:System.ComponentModel.BackgroundWorker" /> prend en charge les mises à jour de progression ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>Obtient ou définit une valeur qui indique si <see cref="T:System.ComponentModel.BackgroundWorker" /> prend en charge l'annulation asynchrone.</summary>
      <returns>true si <see cref="T:System.ComponentModel.BackgroundWorker" /> prend en charge l'annulation ; sinon, false.La valeur par défaut est false.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>Fournit des données pour le gestionnaire d'événements <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.DoWorkEventArgs" />.</summary>
      <param name="argument">Spécifie un argument pour une opération asynchrone.</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>Obtient une valeur qui représente l'argument d'une opération asynchrone.</summary>
      <returns>
        <see cref="T:System.Object" /> qui représente l'argument d'une opération asynchrone.</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>Obtient ou définit une valeur qui représente le résultat d'une opération asynchrone.</summary>
      <returns>
        <see cref="T:System.Object" /> qui représente le résultat d'une opération asynchrone.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>Représente la méthode qui gérera l'événement <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />.Cette classe ne peut pas être héritée.</summary>
      <param name="sender">Source de l'événement.</param>
      <param name="e">
        <see cref="T:System.ComponentModel.DoWorkEventArgs" /> qui contient les données d'événement.</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>Fournit des données pour l'événement <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />.</summary>
      <param name="progressPercentage">Pourcentage d'une tâche asynchrone terminée.</param>
      <param name="userState">État d'utilisateur unique.</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>Obtient le pourcentage de la progression de la tâche asynchrone.</summary>
      <returns>Valeur en pourcentage qui indique la progression de la tâche asynchrone.</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>Obtient un état d'utilisateur unique.</summary>
      <returns>
        <see cref="T:System.Object" /> unique qui indique l'état d'utilisateur.</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>Représente la méthode qui gérera l'événement <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> de la classe <see cref="T:System.ComponentModel.BackgroundWorker" />.Cette classe ne peut pas être héritée.</summary>
      <param name="sender">Source de l'événement. </param>
      <param name="e">
        <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> qui contient les données d'événement. </param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>Fournit des données pour l'événement NomMéthodeCompleted.</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />.</summary>
      <param name="result">Résultat d'une opération asynchrone.</param>
      <param name="error">Toute erreur qui s'est produite pendant l'opération asynchrone.</param>
      <param name="cancelled">Valeur qui indique si l'opération asynchrone a été annulée.</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>Obtient une valeur qui représente le résultat d'une opération asynchrone.</summary>
      <returns>
        <see cref="T:System.Object" /> qui représente le résultat d'une opération asynchrone.</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> n'est pas null.La propriété <see cref="P:System.Exception.InnerException" /> contient une référence à <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> a la valeur true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>Obtient une valeur qui représente l'état d'utilisateur.</summary>
      <returns>
        <see cref="T:System.Object" /> qui représente l'état d'utilisateur.</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>Représente la méthode qui gérera l'événement <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> d'une classe <see cref="T:System.ComponentModel.BackgroundWorker" />.</summary>
      <param name="sender">Source de l'événement. </param>
      <param name="e">
        <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> qui contient les données d'événement. </param>
    </member>
  </members>
</doc>