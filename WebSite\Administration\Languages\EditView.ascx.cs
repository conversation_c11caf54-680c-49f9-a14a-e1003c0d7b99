/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
using System;
using System.Data;
using System.Data.Common;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Diagnostics;
using System.Globalization;

namespace SplendidCRM.Administration.Languages
{
	/// <summary>
	///		Summary description for EditView.
	/// </summary>
	public class EditView : SplendidControl
	{
		protected DataView        vwMain       ;
		protected SplendidGrid    grdMain      ;
		protected Label           lblError     ;

		protected _controls.ListHeader ctlListHeader ;

		protected void Page_Command(object sender, CommandEventArgs e)
		{
			try
			{
				string sNAME = Sql.ToString(e.CommandArgument);
				if ( e.CommandName == "Languages.Add" )
				{
					if ( Sql.IsEmptyString(sNAME) )
						throw(new Exception("Unspecified argument"));
					CultureInfo ci = CultureInfo.CreateSpecificCulture(sNAME);
					Guid gID = Guid.Empty;
					SqlProcs.spLANGUAGES_Update(ref gID, ci.Name, ci.LCID, true, ci.NativeName, ci.DisplayName);
				}
				SplendidCache.ClearLanguages();
				// 05/20/2008 Paul.  We want to redirect so that the Language combo on the master page will get rebuilt. 
				Response.Redirect("default.aspx");
			}
			catch(Exception ex)
			{
				SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
				lblError.Text = ex.Message;
			}
		}

		private void BindGrid(bool bBind)
		{
			try
			{
				DataTable dt = new DataTable();
				DataView vwLanguages = new DataView(SplendidCache.Languages());
				dt.Columns.Add("NAME"        , Type.GetType("System.String" ));
				dt.Columns.Add("LCID"        , Type.GetType("System.Int32"  ));
				dt.Columns.Add("NATIVE_NAME" , Type.GetType("System.String" ));
				dt.Columns.Add("DISPLAY_NAME", Type.GetType("System.String" ));
				CultureInfo[] aCultures = CultureInfo.GetCultures(CultureTypes.SpecificCultures);
				foreach ( CultureInfo culture in aCultures)
				{
					// 05/20/2008 Paul.  Only display languages that are not currently used. 
					vwLanguages.RowFilter = "NAME = '" + culture.Name + "'";
					if ( vwLanguages.Count == 0 )
					{
						DataRow row = dt.NewRow();
						dt.Rows.Add(row);
						row["NAME"        ] = culture.Name        ;
						row["LCID"        ] = culture.LCID        ;
						row["NATIVE_NAME" ] = culture.NativeName  ;
						row["DISPLAY_NAME"] = culture.DisplayName ;
					}
				}
				vwMain = dt.DefaultView;
				vwMain.Sort = "DISPLAY_NAME";
				grdMain.DataSource = vwMain ;
				if ( bBind )
					grdMain.DataBind();
			}
			catch(Exception ex)
			{
				SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
				lblError.Text = ex.Message;
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			SetPageTitle(L10n.Term("Administration.LBL_ADD_LANGUAGE"));
			// 06/04/2006 Paul.  Visibility is already controlled by the ASPX page, but it is probably a good idea to skip the load. 
			this.Visible = SplendidCRM.Security.IS_ADMIN;
			if ( !this.Visible )
				return;

			// Must bind in order for LinkButton to get the argument. 
			// ImageButton does not work no matter what I try. 
			BindGrid(true);
			// 01/04/2006 Paul.  DataBind seems to be required, otherwise the table header will not get translated. 
			// 06/09/2006 Paul.  Remove data binding in the user controls.  Binding is required, but only do so in the ASPX pages. 
			//Page.DataBind();
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		///		Required method for Designer support - do not modify
		///		the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.Load += new System.EventHandler(this.Page_Load);
		}
		#endregion
	}
}
