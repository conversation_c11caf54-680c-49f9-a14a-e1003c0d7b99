﻿using System;

namespace SplendidCRM.UtilityBillPay.ExConfig
{
    public class DictType : SplendidAdminPage
    {
        private void Page_Load(object sender, System.EventArgs e)
        {
            if (!IsPostBack)
            {
                if (Security.USER_PASSWORD == string.Empty || Security.USER_FIRST_LOGIN != string.Empty || Security.USER_REQUIRE_UPDATEPASS == "True")
                {
                    Page.ClientScript.RegisterStartupScript(this.GetType(), "", "PasswordPopup();", true);
                }
            }
        }

        #region Web Form Designer generated code
        override protected void OnInit(EventArgs e)
        {
            InitializeComponent();
            base.OnInit(e);
        }

        private void InitializeComponent()
        {
            this.Load += new System.EventHandler(this.Page_Load);
        }
        #endregion
    }
}







                        
        
        