<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Security.OAuth</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestCustomExtension">
            <summary>
            Data object used by TokenEndpointRequest which contains parameter information when the "grant_type" is unrecognized.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestCustomExtension.Parameters">
            <summary>
            The parameter information when the "grant_type" is unrecognized.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestRefreshToken">
            <summary>
            Data object used by TokenEndpointRequest when the "grant_type" parameter is "refresh_token".
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestRefreshToken.RefreshToken">
            <summary>
            The value passed to the Token endpoint in the "refresh_token" parameter
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestRefreshToken.Scope">
            <summary>
            The value passed to the Token endpoint in the "scope" parameter
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestAuthorizationCode">
            <summary>
            Data object used by TokenEndpointRequest when the "grant_type" is "authorization_code".
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestAuthorizationCode.Code">
            <summary>
            The value passed to the Token endpoint in the "code" parameter
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestAuthorizationCode.RedirectUri">
            <summary>
            The value passed to the Token endpoint in the "redirect_uri" parameter. This MUST be provided by the caller
            if the original visit to the Authorize endpoint contained a "redirect_uri" parameter.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest">
            <summary>
            Data object representing the information contained in the query string of an Authorize endpoint request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.#ctor(Microsoft.Owin.IReadableStringCollection)">
            <summary>
            Creates a new instance populated with values from the query string parameters.
            </summary>
            <param name="parameters">Query string parameters from a request.</param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.ResponseType">
            <summary>
            The "response_type" query string parameter of the Authorize request. Known values are "code" and "token".
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.ResponseMode">
            <summary>
            The "response_mode" query string parameter of the Authorize request. Known values are "query", "fragment" and "form_post"
            See also, http://openid.net/specs/oauth-v2-form-post-response-mode-1_0.html
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.ClientId">
            <summary>
            The "client_id" query string parameter of the Authorize request. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.RedirectUri">
            <summary>
            The "redirect_uri" query string parameter of the Authorize request. May be absent if the server should use the 
            redirect uri known to be registered to the client id.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.Scope">
            <summary>
            The "scope" query string parameter of the Authorize request. May be absent if the server should use default scopes.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.State">
            <summary>
            The "scope" query string parameter of the Authorize request. May be absent if the client does not require state to be 
            included when returning to the RedirectUri.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.IsAuthorizationCodeGrantType">
            <summary>
            True if the "response_type" query string parameter is "code".
            See also, http://tools.ietf.org/html/rfc6749#section-4.1.1
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.IsImplicitGrantType">
            <summary>
            True if the "response_type" query string parameter is "token".
            See also, http://tools.ietf.org/html/rfc6749#section-4.2.1
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest.ContainsGrantType(System.String)">
            <summary>
            True if the "response_type" query string contains the passed responseType.
            See also, http://openid.net/specs/oauth-v2-multiple-response-types-1_0.html
            </summary>
            <param name="responseType">The responseType that is expected within the "response_type" query string</param>
            <returns>True if the "response_type" query string contains the passed responseType.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest">
            <summary>
            Data object representing the information contained in form encoded body of a Token endpoint request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.#ctor(Microsoft.Owin.IReadableStringCollection)">
            <summary>
            Creates a new instance populated with values from the form encoded body parameters.
            </summary>
            <param name="parameters">Form encoded body parameters from a request.</param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.Parameters">
            <summary>
            The form encoded body parameters of the Token endpoint request
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.GrantType">
            <summary>
            The "grant_type" parameter of the Token endpoint request. This parameter is required.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.ClientId">
            <summary>
            The "client_id" parameter of the Token endpoint request. This parameter is optional. It might not
            be present if the request is authenticated in a different way, for example, by using basic authentication
            credentials.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.AuthorizationCodeGrant">
            <summary>
            Data object available when the "grant_type" is "authorization_code".
            See also http://tools.ietf.org/html/rfc6749#section-4.1.3
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.ClientCredentialsGrant">
            <summary>
            Data object available when the "grant_type" is "client_credentials".
            See also http://tools.ietf.org/html/rfc6749#section-4.4.2
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.RefreshTokenGrant">
            <summary>
            Data object available when the "grant_type" is "refresh_token".
            See also http://tools.ietf.org/html/rfc6749#section-6
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.ResourceOwnerPasswordCredentialsGrant">
            <summary>
            Data object available when the "grant_type" is "password".
            See also http://tools.ietf.org/html/rfc6749#section-4.3.2
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.CustomExtensionGrant">
            <summary>
            Data object available when the "grant_type" is unrecognized.
            See also http://tools.ietf.org/html/rfc6749#section-4.5
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.IsAuthorizationCodeGrantType">
            <summary>
            True when the "grant_type" is "authorization_code".
            See also http://tools.ietf.org/html/rfc6749#section-4.1.3
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.IsClientCredentialsGrantType">
            <summary>
            True when the "grant_type" is "client_credentials".
            See also http://tools.ietf.org/html/rfc6749#section-4.4.2
            </summary>  
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.IsRefreshTokenGrantType">
            <summary>
            True when the "grant_type" is "refresh_token".
            See also http://tools.ietf.org/html/rfc6749#section-6
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.IsResourceOwnerPasswordCredentialsGrantType">
            <summary>
            True when the "grant_type" is "password".
            See also http://tools.ietf.org/html/rfc6749#section-4.3.2
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest.IsCustomExtensionGrantType">
            <summary>
            True when the "grant_type" is unrecognized.
            See also http://tools.ietf.org/html/rfc6749#section-4.5
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestClientCredentials">
            <summary>
            Data object used by TokenEndpointRequest when the "grant_type" is "client_credentials".
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestClientCredentials.Scope">
            <summary>
            The value passed to the Token endpoint in the "scope" parameter
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestResourceOwnerPasswordCredentials">
            <summary>
            Data object used by TokenEndpointRequest when the "grant_type" is "password".
            </summary>    
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestResourceOwnerPasswordCredentials.UserName">
            <summary>
            The value passed to the Token endpoint in the "username" parameter
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestResourceOwnerPasswordCredentials.Password">
            <summary>
            The value passed to the Token endpoint in the "password" parameter
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequestResourceOwnerPasswordCredentials.Scope">
            <summary>
            The value passed to the Token endpoint in the "scope" parameter
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions">
            <summary>
            Options class provides information needed to control Authorization Server middleware behavior
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.#ctor">
            <summary>
            Creates an instance of authorization server options with default values.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AuthorizeEndpointPath">
            <summary>
            The request path where client applications will redirect the user-agent in order to 
            obtain user consent to issue a token. Must begin with a leading slash, like "/Authorize".
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.TokenEndpointPath">
            <summary>
            The request path client applications communicate with directly as part of the OAuth protocol. 
            Must begin with a leading slash, like "/Token". If the client is issued a client_secret, it must
            be provided to this endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.Provider">
            <summary>
            The object provided by the application to process events raised by the Authorization Server middleware.
            The application may implement the interface fully, or it may create an instance of OAuthAuthorizationServerProvider
            and assign delegates only to the events it wants to process.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AuthorizationCodeFormat">
            <summary>
            The data format used to protect and unprotect the information contained in the authorization code. 
            If not provided by the application the default data protection provider depends on the host server. 
            The SystemWeb host on IIS will use ASP.NET machine key data protection, and HttpListener and other self-hosted
            servers will use DPAPI data protection.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AccessTokenFormat">
            <summary>
            The data format used to protect the information contained in the access token. 
            If not provided by the application the default data protection provider depends on the host server. 
            The SystemWeb host on IIS will use ASP.NET machine key data protection, and HttpListener and other self-hosted
            servers will use DPAPI data protection. If a different access token
            provider or format is assigned, a compatible instance must be assigned to the OAuthBearerAuthenticationOptions.AccessTokenProvider 
            or OAuthBearerAuthenticationOptions.AccessTokenFormat property of the resource server.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.RefreshTokenFormat">
            <summary>
            The data format used to protect and unprotect the information contained in the refresh token. 
            If not provided by the application the default data protection provider depends on the host server. 
            The SystemWeb host on IIS will use ASP.NET machine key data protection, and HttpListener and other self-hosted
            servers will use DPAPI data protection.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AuthorizationCodeExpireTimeSpan">
            <summary>
            The period of time the authorization code remains valid after being issued. The default is five minutes.
            This time span must also take into account clock synchronization between servers in a web farm, so a very 
            brief value could result in unexpectedly expired tokens.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AccessTokenExpireTimeSpan">
            <summary>
            The period of time the access token remains valid after being issued. The default is twenty minutes.
            The client application is expected to refresh or acquire a new access token after the token has expired. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AuthorizationCodeProvider">
            <summary>
            Produces a single-use authorization code to return to the client application. For the OAuth server to be secure the
            application MUST provide an instance for AuthorizationCodeProvider where the token produced by the OnCreate or OnCreateAsync event 
            is considered valid for only one call to OnReceive or OnReceiveAsync. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AccessTokenProvider">
            <summary>
            Produces a bearer token the client application will typically be providing to resource server as the authorization bearer 
            http request header. If not provided the token produced on the server's default data protection. If a different access token
            provider or format is assigned, a compatible instance must be assigned to the OAuthBearerAuthenticationOptions.AccessTokenProvider 
            or OAuthBearerAuthenticationOptions.AccessTokenFormat property of the resource server.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.RefreshTokenProvider">
            <summary>
            Produces a refresh token which may be used to produce a new access token when needed. If not provided the authorization server will
            not return refresh tokens from the /Token endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.ApplicationCanDisplayErrors">
            <summary>
            Set to true if the web application is able to render error messages on the /Authorize endpoint. This is only needed for cases where
            the browser is not redirected back to the client application, for example, when the client_id or redirect_uri are incorrect. The 
            /Authorize endpoint should expect to see "oauth.Error", "oauth.ErrorDescription", "oauth.ErrorUri" properties added to the owin environment.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.SystemClock">
            <summary>
            Used to know what the current clock time is when calculating or validating token expiration. When not assigned default is based on
            DateTimeOffset.UtcNow. This is typically needed only for unit testing.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.AllowInsecureHttp">
            <summary>
            True to allow authorize and token requests to arrive on http URI addresses, and to allow incoming 
            redirect_uri authorize request parameter to have http URI addresses.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions.FormPostEndpoint">
            <summary>
            Endpoint responsible for Form Post Response Mode
            See also, http://openid.net/specs/oauth-v2-form-post-response-mode-1_0.html
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerMiddleware">
            <summary>
            Authorization Server middleware component which is added to an OWIN pipeline. This class is not
            created by application code directly, instead it is added by calling the the IAppBuilder UseOAuthAuthorizationServer 
            extension method.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions)">
            <summary>
            Authorization Server middleware component which is added to an OWIN pipeline. This constructor is not
            called by application code directly, instead it is added by calling the the IAppBuilder UseOAuthAuthorizationServer 
            extension method.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerMiddleware.CreateHandler">
            <summary>
            Called by the AuthenticationMiddleware base class to create a per-request handler. 
            </summary>
            <returns>A new instance of the request handler</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationMiddleware">
            <summary>
            Bearer authentication middleware component which is added to an OWIN pipeline. This class is not
            created by application code directly, instead it is added by calling the the IAppBuilder UseOAuthBearerAuthentication
            extension method.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions)">
            <summary>
            Bearer authentication component which is added to an OWIN pipeline. This constructor is not
            called by application code directly, instead it is added by calling the the IAppBuilder UseOAuthBearerAuthentication 
            extension method.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationMiddleware.CreateHandler">
            <summary>
            Called by the AuthenticationMiddleware base class to create a per-request handler. 
            </summary>
            <returns>A new instance of the request handler</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions">
            <summary>
            Options class provides information needed to control Bearer Authentication middleware behavior
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions.#ctor">
            <summary>
            Creates an instance of bearer authentication options with default values.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions.Realm">
            <summary>
            Determines what realm value is included when the bearer middleware adds a response header to an unauthorized request.
            If not assigned, the response header does not have a realm.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions.Challenge">
            <summary>
            Specifies the full challenge to send to the client, and should start with "Bearer". If a challenge is provided then the
            Realm property is ignored. If no challenge is specified then one is created using "Bearer" and the value of the Realm
            property.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions.Provider">
            <summary>
            The object provided by the application to process events raised by the bearer authentication middleware.
            The application may implement the interface fully, or it may create an instance of OAuthBearerAuthenticationProvider
            and assign delegates only to the events it wants to process.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions.AccessTokenFormat">
            <summary>
            The data format used to un-protect the information contained in the access token.
            If not provided by the application the default data protection provider depends on the host server. 
            The SystemWeb host on IIS will use ASP.NET machine key data protection, and HttpListener and other self-hosted
            servers will use DPAPI data protection. If a different access token
            provider or format is assigned, a compatible instance must be assigned to the OAuthAuthorizationServerOptions.AccessTokenProvider 
            and OAuthAuthorizationServerOptions.AccessTokenFormat of the authorization server.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions.AccessTokenProvider">
            <summary>
            Receives the bearer token the client application will be providing to web application. If not provided the token 
            produced on the server's default data protection by using the AccessTokenFormat. If a different access token
            provider or format is assigned, a compatible instance must be assigned to the OAuthAuthorizationServerOptions.AccessTokenProvider 
            and OAuthAuthorizationServerOptions.AccessTokenFormat of the authorization server.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions.SystemClock">
            <summary>
            Used to know what the current clock time is when calculating or validating token expiration. When not assigned default is based on
            DateTimeOffset.UtcNow. This is typically needed only for unit testing.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthDefaults">
            <summary>
            Default values used by authorization server and bearer authentication.
            </summary>
        </member>
        <member name="F:Microsoft.Owin.Security.OAuth.OAuthDefaults.AuthenticationType">
            <summary>
            Default value for AuthenticationType property in the OAuthBearerAuthenticationOptions and
            OAuthAuthorizationServerOptions.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext">
            <summary>
            Provides context information used at the end of a token-endpoint-request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.AuthenticationTicket,Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest,System.String,System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="ticket"></param>
            <param name="tokenEndpointRequest"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.Identity">
            <summary>
            Gets the identity of the resource owner.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.Properties">
            <summary>
            Dictionary containing the state of the authentication session.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.AccessToken">
            <summary>
            The issued Access-Token
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.TokenEndpointRequest">
            <summary>
            Gets information about the token endpoint request. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.TokenIssued">
            <summary>
            Gets whether or not the token should be issued.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.AdditionalResponseParameters">
            <summary>
            Enables additional values to be appended to the token response.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext.Issue(System.Security.Claims.ClaimsIdentity,Microsoft.Owin.Security.AuthenticationProperties)">
            <summary>
            Issues the token.
            </summary>
            <param name="identity"></param>
            <param name="properties"></param>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.BaseValidatingClientContext">
            <summary>
            Base class used for certain event contexts
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingClientContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,System.String)">
            <summary>
            Initializes base class used for certain event contexts
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.BaseValidatingClientContext.ClientId">
            <summary>
            The "client_id" parameter for the current request. The Authorization Server application is responsible for 
            validating this value identifies a registered client.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1">
            <summary>
            Base class used for certain event contexts
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.#ctor(Microsoft.Owin.IOwinContext,`0)">
            <summary>
            Initializes base class used for certain event contexts
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.IsValidated">
            <summary>
            True if application code has called any of the Validate methods on this context.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.HasError">
            <summary>
            True if application code has called any of the SetError methods on this context.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.Error">
            <summary>
            The error argument provided when SetError was called on this context. This is eventually
            returned to the client app as the OAuth "error" parameter.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.ErrorDescription">
            <summary>
            The optional errorDescription argument provided when SetError was called on this context. This is eventually
            returned to the client app as the OAuth "error_description" parameter.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.ErrorUri">
            <summary>
            The optional errorUri argument provided when SetError was called on this context. This is eventually
            returned to the client app as the OAuth "error_uri" parameter.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.Validated">
            <summary>
            Marks this context as validated by the application. IsValidated becomes true and HasError becomes false as a result of calling.
            </summary>
            <returns>True if the validation has taken effect.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.Rejected">
            <summary>
            Marks this context as not validated by the application. IsValidated and HasError become false as a result of calling.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.SetError(System.String)">
            <summary>
            Marks this context as not validated by the application and assigns various error information properties. 
            HasError becomes true and IsValidated becomes false as a result of calling.
            </summary>
            <param name="error">Assigned to the Error property</param>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.SetError(System.String,System.String)">
            <summary>
            Marks this context as not validated by the application and assigns various error information properties. 
            HasError becomes true and IsValidated becomes false as a result of calling.
            </summary>
            <param name="error">Assigned to the Error property</param>
            <param name="errorDescription">Assigned to the ErrorDescription property</param>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingContext`1.SetError(System.String,System.String,System.String)">
            <summary>
            Marks this context as not validated by the application and assigns various error information properties. 
            HasError becomes true and IsValidated becomes false as a result of calling.
            </summary>
            <param name="error">Assigned to the Error property</param>
            <param name="errorDescription">Assigned to the ErrorDescription property</param>
            <param name="errorUri">Assigned to the ErrorUri property</param>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.BaseValidatingTicketContext`1">
            <summary>
            Base class used for certain event contexts
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingTicketContext`1.#ctor(Microsoft.Owin.IOwinContext,`0,Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            Initializes base class used for certain event contexts
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.BaseValidatingTicketContext`1.Ticket">
            <summary>
            Contains the identity and properties for the application to authenticate. If the Validated method
            is invoked with an AuthenticationTicket or ClaimsIdentity argument, that new value is assigned to 
            this property in addition to changing IsValidated to true.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingTicketContext`1.Validated(Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            Replaces the ticket information on this context and marks it as as validated by the application. 
            IsValidated becomes true and HasError becomes false as a result of calling.
            </summary>
            <param name="ticket">Assigned to the Ticket property</param>
            <returns>True if the validation has taken effect.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.BaseValidatingTicketContext`1.Validated(System.Security.Claims.ClaimsIdentity)">
            <summary>
            Alters the ticket information on this context and marks it as as validated by the application. 
            IsValidated becomes true and HasError becomes false as a result of calling.
            </summary>
            <param name="identity">Assigned to the Ticket.Identity property</param>
            <returns>True if the validation has taken effect.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext">
            <summary>
            Provides context information when processing an Authorization Response
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.AuthenticationTicket,Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="ticket"></param>
            <param name="tokenEndpointRequest"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext.Identity">
            <summary>
            Gets the identity of the resource owner.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext.Properties">
            <summary>
            Dictionary containing the state of the authentication session.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext.AuthorizeEndpointRequest">
            <summary>
            Gets information about the authorize endpoint request. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext.AdditionalResponseParameters">
            <summary>
            Enables additional values to be appended to the token response.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext.AccessToken">
            <summary>
            The serialized Access-Token. Depending on the flow, it can be null.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext.AuthorizationCode">
            <summary>
            The created Authorization-Code. Depending on the flow, it can be null.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthAuthorizeEndpointContext">
            <summary>
            An event raised after the Authorization Server has processed the request, but before it is passed on to the web application.
            Calling RequestCompleted will prevent the request from passing on to the web application.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizeEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest)">
            <summary>
            Creates an instance of this context
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizeEndpointContext.AuthorizeRequest">
            <summary>
            Gets OAuth authorization request data.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider">
            <summary>
            Interface for OAuthAuthorizationServerOptions.Provider property used by Authorization
            Server to communicate with the web application while processing requests.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.MatchEndpoint(Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext)">
            <summary>
            Called to determine if an incoming request is treated as an Authorize or Token
            endpoint. If Options.AuthorizeEndpointPath or Options.TokenEndpointPath
            are assigned values, then handling this event is optional and context.IsAuthorizeEndpoint and context.IsTokenEndpoint
            will already be true if the request path matches.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.ValidateClientRedirectUri(Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext)">
            <summary>
            Called to validate that the context.ClientId is a registered "client_id", and that the context.RedirectUri a "redirect_uri" 
            registered for that client. This only occurs when processing the Authorize endpoint. The application MUST implement this
            call, and it MUST validate both of those factors before calling context.Validated. If the context.Validated method is called
            with a given redirectUri parameter, then IsValidated will only become true if the incoming redirect URI matches the given redirect URI. 
            If context.Validated is not called the request will not proceed further. 
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.ValidateClientAuthentication(Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext)">
            <summary>
            Called to validate that the origin of the request is a registered "client_id", and that the correct credentials for that client are
            present on the request. If the web application accepts Basic authentication credentials, 
            context.TryGetBasicCredentials(out clientId, out clientSecret) may be called to acquire those values if present in the request header. If the web 
            application accepts "client_id" and "client_secret" as form encoded POST parameters, 
            context.TryGetFormCredentials(out clientId, out clientSecret) may be called to acquire those values if present in the request body.
            If context.Validated is not called the request will not proceed further. 
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.ValidateAuthorizeRequest(Microsoft.Owin.Security.OAuth.OAuthValidateAuthorizeRequestContext)">
            <summary>
            Called for each request to the Authorize endpoint to determine if the request is valid and should continue. 
            The default behavior when using the OAuthAuthorizationServerProvider is to assume well-formed requests, with 
            validated client redirect URI, should continue processing. An application may add any additional constraints.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.ValidateTokenRequest(Microsoft.Owin.Security.OAuth.OAuthValidateTokenRequestContext)">
            <summary>
            Called for each request to the Token endpoint to determine if the request is valid and should continue. 
            The default behavior when using the OAuthAuthorizationServerProvider is to assume well-formed requests, with 
            validated client credentials, should continue processing. An application may add any additional constraints.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.GrantAuthorizationCode(Microsoft.Owin.Security.OAuth.OAuthGrantAuthorizationCodeContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "authorization_code". This occurs after the Authorize
            endpoint as redirected the user-agent back to the client with a "code" parameter, and the client is exchanging that for an "access_token".
            The claims and properties 
            associated with the authorization code are present in the context.Ticket. The application must call context.Validated to instruct the Authorization
            Server middleware to issue an access token based on those claims and properties. The call to context.Validated may be given a different
            AuthenticationTicket or ClaimsIdentity in order to control which information flows from authorization code to access token.
            The default behavior when using the OAuthAuthorizationServerProvider is to flow information from the authorization code to 
            the access token unmodified.
            See also http://tools.ietf.org/html/rfc6749#section-4.1.3
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.GrantRefreshToken(Microsoft.Owin.Security.OAuth.OAuthGrantRefreshTokenContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "refresh_token". This occurs if your application has issued a "refresh_token" 
            along with the "access_token", and the client is attempting to use the "refresh_token" to acquire a new "access_token", and possibly a new "refresh_token".
            To issue a refresh token the an Options.RefreshTokenProvider must be assigned to create the value which is returned. The claims and properties 
            associated with the refresh token are present in the context.Ticket. The application must call context.Validated to instruct the 
            Authorization Server middleware to issue an access token based on those claims and properties. The call to context.Validated may 
            be given a different AuthenticationTicket or ClaimsIdentity in order to control which information flows from the refresh token to 
            the access token. The default behavior when using the OAuthAuthorizationServerProvider is to flow information from the refresh token to 
            the access token unmodified.
            See also http://tools.ietf.org/html/rfc6749#section-6
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.GrantResourceOwnerCredentials(Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "password". This occurs when the user has provided name and password
            credentials directly into the client application's user interface, and the client application is using those to acquire an "access_token" and 
            optional "refresh_token". If the web application supports the
            resource owner credentials grant type it must validate the context.Username and context.Password as appropriate. To issue an
            access token the context.Validated must be called with a new ticket containing the claims about the resource owner which should be associated
            with the access token. The application should take appropriate measures to ensure that the endpoint isn’t abused by malicious callers.  . 
            The default behavior is to reject this grant type.
            See also http://tools.ietf.org/html/rfc6749#section-4.3.2
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.GrantClientCredentials(Microsoft.Owin.Security.OAuth.OAuthGrantClientCredentialsContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "client_credentials". This occurs when a registered client
            application wishes to acquire an "access_token" to interact with protected resources on it's own behalf, rather than on behalf of an authenticated user. 
            If the web application supports the client credentials it may assume the context.ClientId has been validated by the ValidateClientAuthentication call.
            To issue an access token the context.Validated must be called with a new ticket containing the claims about the client application which should be associated
            with the access token. The application should take appropriate measures to ensure that the endpoint isn’t abused by malicious callers.
            The default behavior is to reject this grant type.
            See also http://tools.ietf.org/html/rfc6749#section-4.4.2
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.GrantCustomExtension(Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext)">
            <summary>
            Called when a request to the Token andpoint arrives with a "grant_type" of any other value. If the application supports custom grant types
            it is entirely responsible for determining if the request should result in an access_token. If context.Validated is called with ticket
            information the response body is produced in the same way as the other standard grant types. If additional response parameters must be
            included they may be added in the final TokenEndpoint call.
            See also http://tools.ietf.org/html/rfc6749#section-4.5
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.AuthorizeEndpoint(Microsoft.Owin.Security.OAuth.OAuthAuthorizeEndpointContext)">
            <summary>
            Called at the final stage of an incoming Authorize endpoint request before the execution continues on to the web application component 
            responsible for producing the html response. Anything present in the OWIN pipeline following the Authorization Server may produce the
            response for the Authorize page. If running on IIS any ASP.NET technology running on the server may produce the response for the 
            Authorize page. If the web application wishes to produce the response directly in the AuthorizeEndpoint call it may write to the 
            context.Response directly and should call context.RequestCompleted to stop other handlers from executing. If the web application wishes
            to grant the authorization directly in the AuthorizeEndpoint call it cay call context.OwinContext.Authentication.SignIn with the
            appropriate ClaimsIdentity and should call context.RequestCompleted to stop other handlers from executing.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.TokenEndpoint(Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext)">
            <summary>
            Called at the final stage of a successful Token endpoint request. An application may implement this call in order to do any final 
            modification of the claims being used to issue access or refresh tokens. This call may also be used in order to add additional 
            response parameters to the Token endpoint's json response body.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.AuthorizationEndpointResponse(Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext)">
            <summary>
            Called before the AuthorizationEndpoint redirects its response to the caller. The response could be the
            token, when using implicit flow or the AuthorizationEndpoint when using authorization code flow.  
            An application may implement this call in order to do any final modification of the claims being used 
            to issue access or refresh tokens. This call may also be used in order to add additional 
            response parameters to the authorization endpoint's response.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthAuthorizationServerProvider.TokenEndpointResponse(Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext)">
            <summary>
            Called before the TokenEndpoint redirects its response to the caller. 
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider">
            <summary>
            Default implementation of IOAuthAuthorizationServerProvider used by Authorization
            Server to communicate with the web application while processing requests. OAuthAuthorizationServerProvider provides some default behavior, 
            may be used as a virtual base class, and offers delegate properties which may be used to 
            handle individual calls without declaring a new class type.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.#ctor">
            <summary>
            Creates new instance of default provider behavior
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnMatchEndpoint">
            <summary>
            Called to determine if an incoming request is treated as an Authorize or Token
            endpoint. If Options.AuthorizeEndpointPath or Options.TokenEndpointPath
            are assigned values, then handling this event is optional and context.IsAuthorizeEndpoint and context.IsTokenEndpoint
            will already be true if the request path matches.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnValidateClientRedirectUri">
            <summary>
            Called to validate that the context.ClientId is a registered "client_id", and that the context.RedirectUri a "redirect_uri" 
            registered for that client. This only occurs when processing the Authorize endpoint. The application MUST implement this
            call, and it MUST validate both of those factors before calling context.Validated. If the context.Validated method is called
            with a given redirectUri parameter, then IsValidated will only become true if the incoming redirect URI matches the given redirect URI. 
            If context.Validated is not called the request will not proceed further. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnValidateClientAuthentication">
            <summary>
            Called to validate that the origin of the request is a registered "client_id", and that the correct credentials for that client are
            present on the request. If the web application accepts Basic authentication credentials, 
            context.TryGetBasicCredentials(out clientId, out clientSecret) may be called to acquire those values if present in the request header. If the web 
            application accepts "client_id" and "client_secret" as form encoded POST parameters, 
            context.TryGetFormCredentials(out clientId, out clientSecret) may be called to acquire those values if present in the request body.
            If context.Validated is not called the request will not proceed further. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnValidateAuthorizeRequest">
            <summary>
            Called for each request to the Authorize endpoint to determine if the request is valid and should continue. 
            The default behavior when using the OAuthAuthorizationServerProvider is to assume well-formed requests, with 
            validated client redirect URI, should continue processing. An application may add any additional constraints.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnValidateTokenRequest">
            <summary>
            Called for each request to the Token endpoint to determine if the request is valid and should continue. 
            The default behavior when using the OAuthAuthorizationServerProvider is to assume well-formed requests, with 
            validated client credentials, should continue processing. An application may add any additional constraints.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnGrantAuthorizationCode">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "authorization_code". This occurs after the Authorize
            endpoint as redirected the user-agent back to the client with a "code" parameter, and the client is exchanging that for an "access_token".
            The claims and properties 
            associated with the authorization code are present in the context.Ticket. The application must call context.Validated to instruct the Authorization
            Server middleware to issue an access token based on those claims and properties. The call to context.Validated may be given a different
            AuthenticationTicket or ClaimsIdentity in order to control which information flows from authorization code to access token.
            The default behavior when using the OAuthAuthorizationServerProvider is to flow information from the authorization code to 
            the access token unmodified.
            See also http://tools.ietf.org/html/rfc6749#section-4.1.3
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnGrantResourceOwnerCredentials">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "password". This occurs when the user has provided name and password
            credentials directly into the client application's user interface, and the client application is using those to acquire an "access_token" and 
            optional "refresh_token". If the web application supports the
            resource owner credentials grant type it must validate the context.Username and context.Password as appropriate. To issue an
            access token the context.Validated must be called with a new ticket containing the claims about the resource owner which should be associated
            with the access token. The application should take appropriate measures to ensure that the endpoint isn’t abused by malicious callers.
            The default behavior is to reject this grant type.
            See also http://tools.ietf.org/html/rfc6749#section-4.3.2
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnGrantClientCredentials">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "client_credentials". This occurs when a registered client
            application wishes to acquire an "access_token" to interact with protected resources on it's own behalf, rather than on behalf of an authenticated user. 
            If the web application supports the client credentials it may assume the context.ClientId has been validated by the ValidateClientAuthentication call.
            To issue an access token the context.Validated must be called with a new ticket containing the claims about the client application which should be associated
            with the access token. The application should take appropriate measures to ensure that the endpoint isn’t abused by malicious callers.
            The default behavior is to reject this grant type.
            See also http://tools.ietf.org/html/rfc6749#section-4.4.2
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnGrantRefreshToken">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "refresh_token". This occurs if your application has issued a "refresh_token" 
            along with the "access_token", and the client is attempting to use the "refresh_token" to acquire a new "access_token", and possibly a new "refresh_token".
            To issue a refresh token the an Options.RefreshTokenProvider must be assigned to create the value which is returned. The claims and properties 
            associated with the refresh token are present in the context.Ticket. The application must call context.Validated to instruct the 
            Authorization Server middleware to issue an access token based on those claims and properties. The call to context.Validated may 
            be given a different AuthenticationTicket or ClaimsIdentity in order to control which information flows from the refresh token to 
            the access token. The default behavior when using the OAuthAuthorizationServerProvider is to flow information from the refresh token to 
            the access token unmodified.
            See also http://tools.ietf.org/html/rfc6749#section-6
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnGrantCustomExtension">
            <summary>
            Called when a request to the Token andpoint arrives with a "grant_type" of any other value. If the application supports custom grant types
            it is entirely responsible for determining if the request should result in an access_token. If context.Validated is called with ticket
            information the response body is produced in the same way as the other standard grant types. If additional response parameters must be
            included they may be added in the final TokenEndpoint call.
            See also http://tools.ietf.org/html/rfc6749#section-4.5
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnAuthorizeEndpoint">
            <summary>
            Called at the final stage of an incoming Authorize endpoint request before the execution continues on to the web application component 
            responsible for producing the html response. Anything present in the OWIN pipeline following the Authorization Server may produce the
            response for the Authorize page. If running on IIS any ASP.NET technology running on the server may produce the response for the 
            Authorize page. If the web application wishes to produce the response directly in the AuthorizeEndpoint call it may write to the 
            context.Response directly and should call context.RequestCompleted to stop other handlers from executing. If the web application wishes
            to grant the authorization directly in the AuthorizeEndpoint call it cay call context.OwinContext.Authentication.SignIn with the
            appropriate ClaimsIdentity and should call context.RequestCompleted to stop other handlers from executing.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnTokenEndpoint">
            <summary>
            Called at the final stage of a successful Token endpoint request. An application may implement this call in order to do any final 
            modification of the claims being used to issue access or refresh tokens. This call may also be used in order to add additional 
            response parameters to the Token endpoint's json response body.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnAuthorizationEndpointResponse">
            <summary>
            Called before the AuthorizationEndpoint redirects its response to the caller. The response could be the
            token, when using implicit flow or the AuthorizationEndpoint when using authorization code flow.  
            An application may implement this call in order to do any final modification of the claims being used 
            to issue access or refresh tokens. This call may also be used in order to add additional 
            response parameters to the authorization endpoint's response.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.OnTokenEndpointResponse">
            <summary>
            Called before the TokenEndpoint redirects its response to the caller. 
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.MatchEndpoint(Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext)">
            <summary>
            Called to determine if an incoming request is treated as an Authorize or Token
            endpoint. If Options.AuthorizeEndpointPath or Options.TokenEndpointPath
            are assigned values, then handling this event is optional and context.IsAuthorizeEndpoint and context.IsTokenEndpoint
            will already be true if the request path matches.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.ValidateClientRedirectUri(Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext)">
            <summary>
            Called to validate that the context.ClientId is a registered "client_id", and that the context.RedirectUri a "redirect_uri" 
            registered for that client. This only occurs when processing the Authorize endpoint. The application MUST implement this
            call, and it MUST validate both of those factors before calling context.Validated. If the context.Validated method is called
            with a given redirectUri parameter, then IsValidated will only become true if the incoming redirect URI matches the given redirect URI. 
            If context.Validated is not called the request will not proceed further. 
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.ValidateClientAuthentication(Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext)">
            <summary>
            Called to validate that the origin of the request is a registered "client_id", and that the correct credentials for that client are
            present on the request. If the web application accepts Basic authentication credentials, 
            context.TryGetBasicCredentials(out clientId, out clientSecret) may be called to acquire those values if present in the request header. If the web 
            application accepts "client_id" and "client_secret" as form encoded POST parameters, 
            context.TryGetFormCredentials(out clientId, out clientSecret) may be called to acquire those values if present in the request body.
            If context.Validated is not called the request will not proceed further. 
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.ValidateAuthorizeRequest(Microsoft.Owin.Security.OAuth.OAuthValidateAuthorizeRequestContext)">
            <summary>
            Called for each request to the Authorize endpoint to determine if the request is valid and should continue. 
            The default behavior when using the OAuthAuthorizationServerProvider is to assume well-formed requests, with 
            validated client redirect URI, should continue processing. An application may add any additional constraints.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.ValidateTokenRequest(Microsoft.Owin.Security.OAuth.OAuthValidateTokenRequestContext)">
            <summary>
            Called for each request to the Token endpoint to determine if the request is valid and should continue. 
            The default behavior when using the OAuthAuthorizationServerProvider is to assume well-formed requests, with 
            validated client credentials, should continue processing. An application may add any additional constraints.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.GrantAuthorizationCode(Microsoft.Owin.Security.OAuth.OAuthGrantAuthorizationCodeContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "authorization_code". This occurs after the Authorize
            endpoint as redirected the user-agent back to the client with a "code" parameter, and the client is exchanging that for an "access_token".
            The claims and properties 
            associated with the authorization code are present in the context.Ticket. The application must call context.Validated to instruct the Authorization
            Server middleware to issue an access token based on those claims and properties. The call to context.Validated may be given a different
            AuthenticationTicket or ClaimsIdentity in order to control which information flows from authorization code to access token.
            The default behavior when using the OAuthAuthorizationServerProvider is to flow information from the authorization code to 
            the access token unmodified.
            See also http://tools.ietf.org/html/rfc6749#section-4.1.3
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.GrantRefreshToken(Microsoft.Owin.Security.OAuth.OAuthGrantRefreshTokenContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "refresh_token". This occurs if your application has issued a "refresh_token" 
            along with the "access_token", and the client is attempting to use the "refresh_token" to acquire a new "access_token", and possibly a new "refresh_token".
            To issue a refresh token the an Options.RefreshTokenProvider must be assigned to create the value which is returned. The claims and properties 
            associated with the refresh token are present in the context.Ticket. The application must call context.Validated to instruct the 
            Authorization Server middleware to issue an access token based on those claims and properties. The call to context.Validated may 
            be given a different AuthenticationTicket or ClaimsIdentity in order to control which information flows from the refresh token to 
            the access token. The default behavior when using the OAuthAuthorizationServerProvider is to flow information from the refresh token to 
            the access token unmodified.
            See also http://tools.ietf.org/html/rfc6749#section-6
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.GrantResourceOwnerCredentials(Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "password". This occurs when the user has provided name and password
            credentials directly into the client application's user interface, and the client application is using those to acquire an "access_token" and 
            optional "refresh_token". If the web application supports the
            resource owner credentials grant type it must validate the context.Username and context.Password as appropriate. To issue an
            access token the context.Validated must be called with a new ticket containing the claims about the resource owner which should be associated
            with the access token. The application should take appropriate measures to ensure that the endpoint isn’t abused by malicious callers.
            The default behavior is to reject this grant type.
            See also http://tools.ietf.org/html/rfc6749#section-4.3.2
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.GrantClientCredentials(Microsoft.Owin.Security.OAuth.OAuthGrantClientCredentialsContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of "client_credentials". This occurs when a registered client
            application wishes to acquire an "access_token" to interact with protected resources on it's own behalf, rather than on behalf of an authenticated user. 
            If the web application supports the client credentials it may assume the context.ClientId has been validated by the ValidateClientAuthentication call.
            To issue an access token the context.Validated must be called with a new ticket containing the claims about the client application which should be associated
            with the access token. The application should take appropriate measures to ensure that the endpoint isn’t abused by malicious callers.
            The default behavior is to reject this grant type.
            See also http://tools.ietf.org/html/rfc6749#section-4.4.2
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.GrantCustomExtension(Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext)">
            <summary>
            Called when a request to the Token endpoint arrives with a "grant_type" of any other value. If the application supports custom grant types
            it is entirely responsible for determining if the request should result in an access_token. If context.Validated is called with ticket
            information the response body is produced in the same way as the other standard grant types. If additional response parameters must be
            included they may be added in the final TokenEndpoint call.
            See also http://tools.ietf.org/html/rfc6749#section-4.5
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.AuthorizeEndpoint(Microsoft.Owin.Security.OAuth.OAuthAuthorizeEndpointContext)">
            <summary>
            Called at the final stage of an incoming Authorize endpoint request before the execution continues on to the web application component 
            responsible for producing the html response. Anything present in the OWIN pipeline following the Authorization Server may produce the
            response for the Authorize page. If running on IIS any ASP.NET technology running on the server may produce the response for the 
            Authorize page. If the web application wishes to produce the response directly in the AuthorizeEndpoint call it may write to the 
            context.Response directly and should call context.RequestCompleted to stop other handlers from executing. If the web application wishes
            to grant the authorization directly in the AuthorizeEndpoint call it cay call context.OwinContext.Authentication.SignIn with the
            appropriate ClaimsIdentity and should call context.RequestCompleted to stop other handlers from executing.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.TokenEndpoint(Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext)">
            <summary>
            Called at the final stage of a successful Token endpoint request. An application may implement this call in order to do any final 
            modification of the claims being used to issue access or refresh tokens. This call may also be used in order to add additional 
            response parameters to the Token endpoint's json response body.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.AuthorizationEndpointResponse(Microsoft.Owin.Security.OAuth.OAuthAuthorizationEndpointResponseContext)">
            <summary>
            Called before the AuthorizationEndpoint redirects its response to the caller. The response could be the
            token, when using implicit flow or the AuthorizationEndpoint when using authorization code flow.  
            An application may implement this call in order to do any final modification of the claims being used 
            to issue access or refresh tokens. This call may also be used in order to add additional 
            response parameters to the authorization endpoint's response.
            </summary>
            <param name="context">The context of the event carries information in and results out.</param>
            <returns>Task to enable asynchronous execution</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerProvider.TokenEndpointResponse(Microsoft.Owin.Security.OAuth.OAuthTokenEndpointResponseContext)">
            <summary>
            Called before the TokenEndpoint redirects its response to the caller. 
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider">
            <summary>
            OAuth bearer token middleware provider
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider"/> class
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider.OnRequestToken">
            <summary>
            Handles processing OAuth bearer token.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider.OnValidateIdentity">
            <summary>
            Handles validating the identity produced from an OAuth bearer token.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider.OnApplyChallenge">
            <summary>
            Handles applying the authentication challenge to the response message.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider.RequestToken(Microsoft.Owin.Security.OAuth.OAuthRequestTokenContext)">
            <summary>
            Handles processing OAuth bearer token.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider.ValidateIdentity(Microsoft.Owin.Security.OAuth.OAuthValidateIdentityContext)">
            <summary>
            Handles validating the identity produced from an OAuth bearer token.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationProvider.ApplyChallenge(Microsoft.Owin.Security.OAuth.OAuthChallengeContext)">
            <summary>
            Handles applying the authentication challenge to the response message.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthChallengeContext">
            <summary>
            Specifies the HTTP response header for the bearer authentication scheme.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthChallengeContext.#ctor(Microsoft.Owin.IOwinContext,System.String)">
            <summary>
            Initializes a new <see cref="T:Microsoft.Owin.Security.OAuth.OAuthRequestTokenContext"/>
            </summary>
            <param name="context">OWIN environment</param>
            <param name="challenge">The www-authenticate header value.</param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthChallengeContext.Challenge">
            <summary>
            The www-authenticate header value.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthGrantAuthorizationCodeContext">
            <summary>
            Provides context information when handling an OAuth authorization code grant.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthGrantAuthorizationCodeContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthGrantAuthorizationCodeContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="ticket"></param>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthGrantRefreshTokenContext">
            <summary>
            Provides context information used when granting an OAuth refresh token.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthGrantRefreshTokenContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.AuthenticationTicket,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthGrantRefreshTokenContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="ticket"></param>
            <param name="clientId"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantRefreshTokenContext.ClientId">
            <summary>
            The OAuth client id.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthRequestTokenContext">
            <summary>
            Specifies the HTTP request header for the bearer authentication scheme.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthRequestTokenContext.#ctor(Microsoft.Owin.IOwinContext,System.String)">
            <summary>
            Initializes a new <see cref="T:Microsoft.Owin.Security.OAuth.OAuthRequestTokenContext"/>
            </summary>
            <param name="context">OWIN environment</param>
            <param name="token">The authorization header value.</param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthRequestTokenContext.Token">
            <summary>
            The authorization header value
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext">
            <summary>
            Contains information about the client credentials.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.IReadableStringCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="parameters"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext.Parameters">
            <summary>
            Gets the set of form parameters from the request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext.Validated(System.String)">
            <summary>
            Sets the client id and marks the context as validated by the application.
            </summary>
            <param name="clientId"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext.TryGetBasicCredentials(System.String@,System.String@)">
            <summary>
            Extracts HTTP basic authentication credentials from the HTTP authenticate header.
            </summary>
            <param name="clientId"></param>
            <param name="clientSecret"></param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateClientAuthenticationContext.TryGetFormCredentials(System.String@,System.String@)">
            <summary>
            Extracts forms authentication credentials from the HTTP request body.
            </summary>
            <param name="clientId"></param>
            <param name="clientSecret"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext">
            <summary>
            Provides context information used when determining the OAuth flow type based on the request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext.IsAuthorizeEndpoint">
            <summary>
            Gets whether or not the endpoint is an OAuth authorize endpoint.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext.IsTokenEndpoint">
            <summary>
            Gets whether or not the endpoint is an OAuth token endpoint.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext.MatchesAuthorizeEndpoint">
            <summary>
            Sets the endpoint type to authorize endpoint.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext.MatchesTokenEndpoint">
            <summary>
            Sets the endpoint type to token endpoint.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthMatchEndpointContext.MatchesNothing">
            <summary>
            Sets the endpoint type to neither authorize nor token.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext">
            <summary>
            Provides context information used when processing an OAuth token request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.AuthenticationTicket,Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="ticket"></param>
            <param name="tokenEndpointRequest"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext.Identity">
            <summary>
            Gets the identity of the resource owner.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext.Properties">
            <summary>
            Dictionary containing the state of the authentication session.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext.TokenEndpointRequest">
            <summary>
            Gets information about the token endpoint request. 
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext.TokenIssued">
            <summary>
            Gets whether or not the token should be issued.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext.AdditionalResponseParameters">
            <summary>
            Enables additional values to be appended to the token response.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthTokenEndpointContext.Issue(System.Security.Claims.ClaimsIdentity,Microsoft.Owin.Security.AuthenticationProperties)">
            <summary>
            Issues the token.
            </summary>
            <param name="identity"></param>
            <param name="properties"></param>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthGrantClientCredentialsContext">
            <summary>
            Provides context information used in handling an OAuth client credentials grant.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthGrantClientCredentialsContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthGrantClientCredentialsContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="clientId"></param>
            <param name="scope"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantClientCredentialsContext.ClientId">
            <summary>
            OAuth client id.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantClientCredentialsContext.Scope">
            <summary>
            List of scopes allowed by the resource owner.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext">
            <summary>
            Provides context information used when handling OAuth extension grant types.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,System.String,System.String,Microsoft.Owin.IReadableStringCollection)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="clientId"></param>
            <param name="grantType"></param>
            <param name="parameters"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext.ClientId">
            <summary>
            Gets the OAuth client id.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext.GrantType">
            <summary>
            Gets the name of the OAuth extension grant type.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantCustomExtensionContext.Parameters">
            <summary>
            Gets a list of additional parameters from the token request.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthValidateAuthorizeRequestContext">
            <summary>
            Provides context information used in validating an OAuth authorization request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateAuthorizeRequestContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.OAuth.Messages.AuthorizeEndpointRequest,Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthValidateAuthorizeRequestContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="authorizeRequest"></param>
            <param name="clientContext"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthValidateAuthorizeRequestContext.AuthorizeRequest">
            <summary>
            Gets OAuth authorization request data.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthValidateAuthorizeRequestContext.ClientContext">
            <summary>
            Gets data about the OAuth client. 
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext">
            <summary>
            Contains data about the OAuth client redirect URI
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="clientId"></param>
            <param name="redirectUri"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext.RedirectUri">
            <summary>
            Gets the client redirect URI
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext.Validated">
            <summary>
            Marks this context as validated by the application. IsValidated becomes true and HasError becomes false as a result of calling.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext.Validated(System.String)">
            <summary>
            Checks the redirect URI to determine whether it equals <see cref="P:Microsoft.Owin.Security.OAuth.OAuthValidateClientRedirectUriContext.RedirectUri"/>.
            </summary>
            <param name="redirectUri"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthValidateIdentityContext">
            <summary>
            Contains the authentication ticket data from an OAuth bearer token.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateIdentityContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions,Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthValidateIdentityContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="ticket"></param>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.IOAuthBearerAuthenticationProvider">
            <summary>
            Specifies callback methods which the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationMiddleware"></see> invokes to enable developer control over the authentication process. />
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthBearerAuthenticationProvider.RequestToken(Microsoft.Owin.Security.OAuth.OAuthRequestTokenContext)">
            <summary>
            Invoked before the <see cref="T:System.Security.Claims.ClaimsIdentity"/> is created. Gives the application an 
            opportunity to find the identity from a different location, adjust, or reject the token.
            </summary>
            <param name="context">Contains the token string.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthBearerAuthenticationProvider.ValidateIdentity(Microsoft.Owin.Security.OAuth.OAuthValidateIdentityContext)">
            <summary>
            Called each time a request identity has been validated by the middleware. By implementing this method the
            application may alter or reject the identity which has arrived with the request.
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.IOAuthBearerAuthenticationProvider.ApplyChallenge(Microsoft.Owin.Security.OAuth.OAuthChallengeContext)">
            <summary>
            Called each time a challenge is being sent to the client. By implementing this method the application
            may modify the challenge as needed.
            </summary>
            <param name="context">Contains the default challenge.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext">
            <summary>
            Provides context information used in handling an OAuth resource owner grant.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,System.String,System.String,System.String,System.Collections.Generic.IList{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="clientId"></param>
            <param name="userName"></param>
            <param name="password"></param>
            <param name="scope"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext.ClientId">
            <summary>
            OAuth client id.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext.UserName">
            <summary>
            Resource owner username.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext.Password">
            <summary>
            Resource owner password.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthGrantResourceOwnerCredentialsContext.Scope">
            <summary>
            List of scopes allowed by the resource owner.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.OAuth.OAuthValidateTokenRequestContext">
            <summary>
            Provides context information used in validating an OAuth token request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.OAuth.OAuthValidateTokenRequestContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions,Microsoft.Owin.Security.OAuth.Messages.TokenEndpointRequest,Microsoft.Owin.Security.OAuth.BaseValidatingClientContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Security.OAuth.OAuthValidateTokenRequestContext"/> class
            </summary>
            <param name="context"></param>
            <param name="options"></param>
            <param name="tokenRequest"></param>
            <param name="clientContext"></param>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthValidateTokenRequestContext.TokenRequest">
            <summary>
            Gets the token request data.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.OAuth.OAuthValidateTokenRequestContext.ClientContext">
            <summary>
            Gets information about the client.
            </summary>
        </member>
        <member name="T:Owin.OAuthAuthorizationServerExtensions">
            <summary>
            Extension methods to add Authorization Server capabilities to an OWIN pipeline
            </summary>
        </member>
        <member name="M:Owin.OAuthAuthorizationServerExtensions.UseOAuthAuthorizationServer(Owin.IAppBuilder,Microsoft.Owin.Security.OAuth.OAuthAuthorizationServerOptions)">
            <summary>
            Adds OAuth2 Authorization Server capabilities to an OWIN web application. This middleware
            performs the request processing for the Authorize and Token endpoints defined by the OAuth2 specification.
            See also http://tools.ietf.org/html/rfc6749
            </summary>
            <param name="app">The web application builder</param>
            <param name="options">Options which control the behavior of the Authorization Server.</param>
            <returns>The application builder</returns>
        </member>
        <member name="T:Owin.OAuthBearerAuthenticationExtensions">
            <summary>
            Extension methods to add OAuth Bearer authentication capabilities to an OWIN application pipeline
            </summary>
        </member>
        <member name="M:Owin.OAuthBearerAuthenticationExtensions.UseOAuthBearerAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.OAuth.OAuthBearerAuthenticationOptions)">
            <summary>
            Adds Bearer token processing to an OWIN application pipeline. This middleware understands appropriately
            formatted and secured tokens which appear in the request header. If the Options.AuthenticationMode is Active, the
            claims within the bearer token are added to the current request's IPrincipal User. If the Options.AuthenticationMode 
            is Passive, then the current request is not modified, but IAuthenticationManager AuthenticateAsync may be used at
            any time to obtain the claims from the request's bearer token.
            See also http://tools.ietf.org/html/rfc6749
            </summary>
            <param name="app">The web application builder</param>
            <param name="options">Options which control the processing of the bearer header.</param>
            <returns>The application builder</returns>
        </member>
    </members>
</doc>
