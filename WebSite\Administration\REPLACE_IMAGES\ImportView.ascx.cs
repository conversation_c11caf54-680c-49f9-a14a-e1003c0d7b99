/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
using System;
using System.IO;
using System.Data;
using System.Data.Common;
using System.Collections;
using System.Collections.Specialized;
using System.Drawing;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Diagnostics;
using System.Xml;
using System.Text;
using SplendidCRM.TGSAP;
using System.Collections.Generic;
//using Microsoft.VisualBasic;

namespace SplendidCRM.Administration.REPLACE_IMAGES
{
	/// <summary>
	///		Summary description for ImportView.
	/// </summary>
	public class ImportView : SplendidControl
	{
        protected RequiredFieldValidator reqFILENAME;
        protected HtmlInputFile fileIMPORT;
        protected XmlDocument xml;
        protected Label successTxt;

        private const string VS_SOURCEDIRECTORY_KEY = "VS_SOURCEDIRECTORY_KEY";

        public string SourceDirectory
        {
            get
            {
                return ViewState[VS_SOURCEDIRECTORY_KEY] == null ? Server.MapPath(this.Request.ApplicationPath) : Server.MapPath((string)ViewState[VS_SOURCEDIRECTORY_KEY]);
            }
            set
            {
                ViewState[VS_SOURCEDIRECTORY_KEY] = value;
            }
        }

		protected void Page_Command(Object sender, CommandEventArgs e)
		{
			try
			{
                if (e.CommandName == "Import.Upload")
                {
                    List<string> pendingImages = new List<string>();
                    pendingImages = GetValidImageNameList(SplendidCRM.TGSAP.APImagePriority.UPDATE_IMAGES + "\\DCN");


                    for (int imageNumber = 0; imageNumber < pendingImages.Count; imageNumber++)
                    {
                        string imageName = pendingImages[imageNumber];
                        byte[] imageCONTENT = File.ReadAllBytes(imageName);

                        try
                        {
                            Guid gID = Guid.Empty;
                            int iFileSize = imageCONTENT.Length;
                            string sFileName = Path.GetFileName(imageName).Replace(Path.GetExtension(imageName), "");

                            ImportImageToDB(ref gID, sFileName, imageCONTENT,iFileSize);

                            File.Delete(imageName);
                        }
                        catch
                        {
                            continue;
                        }
                    }

                    //successTxt.Text = "Upload image success.";
                }
                else if (e.CommandName == "Import.UploadPdf")
                {
                    List<string> pendingImages = new List<string>();
                    pendingImages = GetValidImageNameList(SplendidCRM.TGSAP.APImagePriority.UPDATE_IMAGES + "\\PDF_NAME");


                    for (int imageNumber = 0; imageNumber < pendingImages.Count; imageNumber++)
                    {
                        string imageName = pendingImages[imageNumber];
                        byte[] imageCONTENT = File.ReadAllBytes(imageName);

                        try
                        {
                            Guid gID = Guid.Empty;
                            int iFileSize = imageCONTENT.Length;
                            string sFileName = Path.GetFileName(imageName).Replace(Path.GetExtension(imageName), "");

                            ImportImagePDFToDB(ref gID, sFileName, imageCONTENT, iFileSize);

                            File.Delete(imageName);
                        }
                        catch
                        {
                            continue;
                        }
                    }

                    //successTxt.Text = "Upload image success.";
                }
			}
			catch(Exception ex)
			{
                SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
				return;
			}
		}

        #region Import Image To DataBase
        private void ImportImageToDB(ref Guid gID, string imageName, byte[] imageCONTENT,Int32 iFILE_SIZE)
        {
            DbProviderFactory dbf = DbProviderFactories.GetFactory();
            using (IDbConnection con = dbf.CreateConnection())
            {
                con.Open();
                using (IDbTransaction trn = con.BeginTransaction())
                {
                    try
                    {
                        SplendidCRM.TGSAP.APSqlProcs.spAP_IMAGES_OnlyUpdateImageContent(ref gID, imageName, imageCONTENT,iFILE_SIZE, trn);

                        trn.Commit();
                    }
                    catch (Exception ex)
                    {
                        trn.Rollback();
                        SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
                        throw ex;
                    }
                }
            }
        }


        private void ImportImagePDFToDB(ref Guid gID, string imageName, byte[] imageCONTENT, Int32 iFILE_SIZE)
        {
            DbProviderFactory dbf = DbProviderFactories.GetFactory();
            using (IDbConnection con = dbf.CreateConnection())
            {
                con.Open();
                using (IDbTransaction trn = con.BeginTransaction())
                {
                    try
                    {
                        SplendidCRM.TGSAP.APSqlProcs.spAP_IMAGES_OnlyUpdateImageContent_NEW(ref gID, imageName, imageCONTENT, iFILE_SIZE, trn);

                        trn.Commit();
                    }
                    catch (Exception ex)
                    {
                        trn.Rollback();
                        SplendidError.SystemError(new StackTrace(true).GetFrame(0), ex);
                        throw ex;
                    }
                }
            }
        }
        #endregion

        #region Process images name list
        private List<string> GetValidImageNameList(string subFolder)
        {
            List<string> pendingImages = new List<string>();
            string[] imageNames = Directory.GetFiles(SourceDirectory + "\\PendingProcess\\" + subFolder, "*.*", SearchOption.AllDirectories);

            foreach (string imageName in imageNames)
            {
                if (subFolder.ToUpper().IndexOf("UPDATE_IMAGES")>-1)
                {
                    if (IsValidDATFileName(imageName))
                    {
                        pendingImages.Add(imageName);
                    }
                }
            }

            pendingImages.Sort(new SplendidCRM.TGSAP.StringComparer());

            return pendingImages;
        }

        private bool IsValidDATFileName(string fileName)
        {
            if (Sql.IsEmptyString(fileName))
                return true;

            if (fileName.ToLower().Trim().IndexOf(".pdf") > -1 || fileName.ToUpper().Trim().IndexOf(".PDF") > -1)
            {
                return true;
            }

            return false;
        }

        #endregion

        private void Page_Load(object sender, System.EventArgs e)
		{
			SetPageTitle(L10n.Term("Import.LBL_MODULE_NAME"));
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		///		Required method for Designer support - do not modify
		///		the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.Load += new System.EventHandler(this.Page_Load);
		}
		#endregion
	}
}
