<%@ Control CodeBehind="ListView.ascx.cs" Language="c#" AutoEventWireup="false" Inherits="SplendidCRM.Administration.SystemLog.ListView" %>
<script runat="server">
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
</script>
<script type="text/javascript">
// 05/22/2008 Paul.  We need to redefine the calendar function so that it points two levels up. 
function CalendarPopup(ctlDate, clientX, clientY)
{
	clientX = window.screenLeft + parseInt(clientX);
	clientY = window.screenTop  + parseInt(clientY);
	if ( clientX < 0 )
		clientX = 0;
	if ( clientY < 0 )
		clientY = 0;
	return window.open('../../Calendar/Popup.aspx?Date=' + ctlDate.value,'CalendarPopup','width=193,height=155,resizable=1,scrollbars=0,left=' + clientX + ',top=' + clientY);
}
</script>
<div id="divListView">
	<%@ Register TagPrefix="SplendidCRM" Tagname="ModuleHeader" Src="~/_controls/ModuleHeader.ascx" %>
	<SplendidCRM:ModuleHeader ID="ctlModuleHeader" Module="Users" Title="Administration.LBL_SYSTEM_LOG_TITLE" EnablePrint="true" HelpName="index" EnableHelp="true" Runat="Server" />

	<%@ Register TagPrefix="SplendidCRM" Tagname="SearchView" Src="~/_controls/SearchView.ascx" %>
	<SplendidCRM:SearchView ID="ctlSearchView" Module="SystemLog" ShowSearchTabs="false" ShowSearchViews="false" Visible="<%# !PrintView %>" Runat="Server" />

	<%@ Register TagPrefix="SplendidCRM" Tagname="ExportHeader" Src="~/_controls/ExportHeader.ascx" %>
	<SplendidCRM:ExportHeader ID="ctlExportHeader" Module="Administration" Title="" Runat="Server" />
	
	<asp:Panel CssClass="button-panel" Visible="<%# !PrintView %>" runat="server">
		<asp:Label ID="lblError" CssClass="error" EnableViewState="false" Runat="server" />
	</asp:Panel>
	
	<SplendidCRM:SplendidGrid id="grdMain" SkinID="grdListView" AllowPaging="<%# !PrintView %>" EnableViewState="true" runat="server">
		<Columns>
			<asp:BoundColumn    HeaderText=".LBL_LIST_DATE_ENTERED"              DataField="DATE_ENTERED"     SortExpression="DATE_ENTERED"     ItemStyle-VerticalAlign="Top" ItemStyle-Wrap="false" />
			<asp:BoundColumn    HeaderText="Users.LBL_LIST_USER_ID"              DataField="USER_ID"          SortExpression="USER_ID"          ItemStyle-VerticalAlign="Top" Visible="false" />
			<asp:BoundColumn    HeaderText="Users.LBL_LIST_USER_NAME"            DataField="USER_NAME"        SortExpression="USER_NAME"        ItemStyle-VerticalAlign="Top" />
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_MACHINE"          DataField="MACHINE"          SortExpression="MACHINE"          ItemStyle-VerticalAlign="Top" Visible="false" />
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_ASPNET_SESSIONID" DataField="ASPNET_SESSIONID" SortExpression="ASPNET_SESSIONID" ItemStyle-VerticalAlign="Top" Visible="false" />
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_REMOTE_HOST"      DataField="REMOTE_HOST"      SortExpression="REMOTE_HOST"      ItemStyle-VerticalAlign="Top" Visible="false" />
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_SERVER_HOST"      DataField="SERVER_HOST"      SortExpression="SERVER_HOST"      ItemStyle-VerticalAlign="Top" Visible="false" />
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_TARGET"           DataField="TARGET"           SortExpression="TARGET"           ItemStyle-VerticalAlign="Top" Visible="false" />
			<asp:TemplateColumn HeaderText="SystemLog.LBL_LIST_ERROR_TYPE"                                    SortExpression="ERROR_TYPE"       ItemStyle-VerticalAlign="Top">
				<ItemTemplate>
					<div class="<%# (Sql.ToString(Eval("ERROR_TYPE")) == "Error" ? "error" : String.Empty) %>"><%# Eval("ERROR_TYPE") %></div>
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn HeaderText="SystemLog.LBL_LIST_MESSAGE"                                       SortExpression="MESSAGE"          ItemStyle-VerticalAlign="Top" ItemStyle-Width="20%">
				<ItemTemplate>
					<div class="<%# (Sql.ToString(Eval("ERROR_TYPE")) == "Error" ? "error" : String.Empty) %>"><%# Eval("MESSAGE") %></div>
				</ItemTemplate>
			</asp:TemplateColumn>
			<asp:TemplateColumn HeaderText="SystemLog.LBL_LIST_FILE_NAME"                                     SortExpression="FILE_NAME"        ItemStyle-VerticalAlign="Top">
				<ItemTemplate><%# Sql.ToString(Eval("FILE_NAME")).Replace("/", "/ ") %></ItemTemplate>
			</asp:TemplateColumn>
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_METHOD"           DataField="METHOD"           SortExpression="METHOD"           ItemStyle-VerticalAlign="Top" />
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_LINE_NUMBER"      DataField="LINE_NUMBER"      SortExpression="LINE_NUMBER"      ItemStyle-VerticalAlign="Top" />
			<asp:TemplateColumn HeaderText="SystemLog.LBL_LIST_RELATIVE_PATH"                                 SortExpression="RELATIVE_PATH"    ItemStyle-VerticalAlign="Top">
				<ItemTemplate><%# Sql.ToString(Eval("RELATIVE_PATH")).Replace("/", "/ ") %></ItemTemplate>
			</asp:TemplateColumn>
			<asp:BoundColumn    HeaderText="SystemLog.LBL_LIST_PARAMETERS"       DataField="PARAMETERS"       SortExpression="PARAMETERS"       ItemStyle-VerticalAlign="Top" />
		</Columns>
	</SplendidCRM:SplendidGrid>

	<%@ Register TagPrefix="SplendidCRM" Tagname="DumpSQL" Src="~/_controls/DumpSQL.ascx" %>
	<SplendidCRM:DumpSQL ID="ctlDumpSQL" Visible="<%# !PrintView %>" Runat="Server" />
</div>
