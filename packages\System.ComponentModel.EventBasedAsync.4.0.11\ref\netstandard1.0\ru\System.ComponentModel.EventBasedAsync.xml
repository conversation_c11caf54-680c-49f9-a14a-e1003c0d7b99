﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>Предоставляет данные для события ИмяМетодаCompleted.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />. </summary>
      <param name="error">Любая ошибка, возникающая в ходе асинхронной операции.</param>
      <param name="cancelled">Значение, показывающее, была ли отменена асинхронная операция.</param>
      <param name="userState">Необязательный заданный пользователем объект состояния, переданный методу <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />.</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>Возвращает значение, показывающее, была ли отменена асинхронная операция.</summary>
      <returns>Значение true, если фоновая операция была отменена; в противном случае — значение false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>Возвращает значение, показывающее, какая ошибка произошла в течение асинхронной операции.</summary>
      <returns>Экземпляр исключения <see cref="T:System.Exception" />, если в ходе асинхронной операции произошла ошибка; в противном случае — значение null.</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>Вызывает предоставленное пользователем исключение в случае неудачного выполнения асинхронной операции.</summary>
      <exception cref="T:System.InvalidOperationException">Значение свойства <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> — true. </exception>
      <exception cref="T:System.Reflection.TargetInvocationException">Свойство <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> было установлено асинхронной операцией.Свойство <see cref="P:System.Exception.InnerException" /> содержит ссылку на ошибку <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>Возвращает уникальный идентификатор для асинхронной задачи.</summary>
      <returns>Ссылка на объект, уникальным образом идентифицирующая асинхронную задачу; в противном случае — значение null, если значение не было задано.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>Представляет метод, который будет обрабатывать событие ИмяМетодаCompleted асинхронной операции.</summary>
      <param name="sender">Источник события. </param>
      <param name="e">Объект <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />, содержащий данные события. </param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>Отслеживает время существования асинхронной операции.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>Завершает асинхронную операцию.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>Завершает время существования асинхронной операции.</summary>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> ранее уже вызывался для этой задачи. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Вызывает делегата для потока или контекста, соответствующего модели приложения.</summary>
      <param name="d">Объект <see cref="T:System.Threading.SendOrPostCallback" />, создающий оболочку делегата, который должен вызываться при завершении операции. </param>
      <param name="arg">Аргумент для делегата, содержащегося в параметре <paramref name="d" />. </param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> уже был вызван ранее для этой задачи. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="d" /> имеет значение null. </exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>Завершает время существования асинхронной операции.</summary>
      <param name="d">Объект <see cref="T:System.Threading.SendOrPostCallback" />, создающий оболочку делегата, который должен вызываться при завершении операции. </param>
      <param name="arg">Аргумент для делегата, содержащегося в параметре <paramref name="d" />. </param>
      <exception cref="T:System.InvalidOperationException">Метод <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" /> ранее уже вызывался для этой задачи. </exception>
      <exception cref="T:System.ArgumentNullException">Свойство <paramref name="d" /> имеет значение null. </exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>Возвращает объект <see cref="T:System.Threading.SynchronizationContext" />, переданный в конструктор.</summary>
      <returns>Объект <see cref="T:System.Threading.SynchronizationContext" />, передаваемый в конструктор.</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>Возвращает или задает объект, используемый для уникальной идентификации асинхронной операции.</summary>
      <returns>Объект состояния, переданный в вызов асинхронного метода.</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>Обеспечивает управление параллельным выполнением для классов, поддерживающих асинхронные вызовы метода.Этот класс не наследуется.</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>Возвращает объект <see cref="T:System.ComponentModel.AsyncOperation" /> для контроля длительности конкретной асинхронной операции.</summary>
      <returns>Объект <see cref="T:System.ComponentModel.AsyncOperation" />, который можно использовать для контроля длительности вызова асинхронного метода.</returns>
      <param name="userSuppliedState">Объект, используемый для связи элемента состояния клиента, например идентификатора задачи, с конкретной асинхронной операцией. </param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>Получает или задает контекст синхронизации для асинхронной операции.</summary>
      <returns>Контекст синхронизации для асинхронной операции.</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>Выполняет операцию в отдельном потоке.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.ComponentModel.BackgroundWorker" />.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>Запрашивает отмену отложенной фоновой операции.</summary>
      <exception cref="T:System.InvalidOperationException">Параметр <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> имеет значение false. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>Возвращает значение, показывающее, запросило ли приложение отмену фоновой операции.</summary>
      <returns>Значение true, если приложение запросило отмену фоновой операции; в противном случае — значение false.Значение по умолчанию — false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>Возникает при вызове метода <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" />.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>Возвращает значение, показывающее, выполняет ли объект <see cref="T:System.ComponentModel.BackgroundWorker" /> асинхронную операцию.</summary>
      <returns>Значение true, если объект <see cref="T:System.ComponentModel.BackgroundWorker" /> выполняет асинхронную операцию; в противном случае — значение false.</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>Создает событие <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />. </summary>
      <param name="e">Объект <see cref="T:System.EventArgs" />, содержащий данные события.</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>Создает событие <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="e">Объект <see cref="T:System.EventArgs" />, содержащий данные события. </param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>Создает событие <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" />.</summary>
      <param name="e">Объект <see cref="T:System.EventArgs" />, содержащий данные события. </param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>Возникает при вызове метода <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" />.</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>Создает событие <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="percentProgress">Выполненный процент, от 0 до 100, фоновой операции. </param>
      <exception cref="T:System.InvalidOperationException">Свойство <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> имеет значение false. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>Создает событие <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
      <param name="percentProgress">Выполненный процент, от 0 до 100, фоновой операции.</param>
      <param name="userState">Объект состояния, переданный методу <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" />.</param>
      <exception cref="T:System.InvalidOperationException">Свойство <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> имеет значение false. </exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>Запускает выполнение фоновой операции.</summary>
      <exception cref="T:System.InvalidOperationException">Параметр <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> имеет значение true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>Запускает выполнение фоновой операции.</summary>
      <param name="argument">Параметр, для использования фоновой операцией, выполняемой в обработчике событий <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />. </param>
      <exception cref="T:System.InvalidOperationException">Параметр <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> имеет значение true. </exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>Возникает, когда выполнение фоновой операции завершено, отменено или вызвало исключение.</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>Получает или задает значение, показывающее, может ли объект <see cref="T:System.ComponentModel.BackgroundWorker" /> сообщать о ходе выполнения.</summary>
      <returns>Значение true, если объект <see cref="T:System.ComponentModel.BackgroundWorker" /> поддерживает обновление сведений о ходе выполнения; в противном случае — значение false.Значение по умолчанию — false.</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>Получает или задает значение, показывающее, поддерживает ли объект <see cref="T:System.ComponentModel.BackgroundWorker" /> отмену асинхронной операции.</summary>
      <returns>Значение true, если объект <see cref="T:System.ComponentModel.BackgroundWorker" /> поддерживает отмену; в противном случае — значение false.Значение по умолчанию — false.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>Предоставляет данные для обработчика событий <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />.</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.ComponentModel.DoWorkEventArgs" />.</summary>
      <param name="argument">Определяет аргумент для асинхронной операции.</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>Возвращает значение, представляющее аргумент асинхронной операции. </summary>
      <returns>Объект <see cref="T:System.Object" />, представляющий аргумент асинхронной операции.</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>Получает или задает значение, представляющее результат асинхронной операции. </summary>
      <returns>Объект <see cref="T:System.Object" />, представляющий результат асинхронной операции.</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>Представляет метод обработки события <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" />.Этот класс не наследуется.</summary>
      <param name="sender">Источник события.</param>
      <param name="e">Объект <see cref="T:System.ComponentModel.DoWorkEventArgs" />, содержащий данные, относящиеся к событию.</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>Предоставляет данные для события <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" />.</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />.</summary>
      <param name="progressPercentage">Выполненный процент асинхронной задачи.</param>
      <param name="userState">Уникальное пользовательское состояние.</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>Возвращает процент выполнения асинхронной задачи.</summary>
      <returns>Значение в процентах, показывающее выполнение асинхронной задачи.</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>Возвращает уникальное пользовательское состояние.</summary>
      <returns>Уникальный объект <see cref="T:System.Object" />, показывающий пользовательское состояние.</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>Представляет метод, с помощью которого будет выполняться обработка события <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> класса <see cref="T:System.ComponentModel.BackgroundWorker" />.Этот класс не наследуется.</summary>
      <param name="sender">Источник события. </param>
      <param name="e">Объект <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />, содержащий данные события. </param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>Предоставляет данные для события ИмяМетодаCompleted.</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />.</summary>
      <param name="result">Результат асинхронной операции.</param>
      <param name="error">Любая ошибка, возникающая в ходе асинхронной операции.</param>
      <param name="cancelled">Значение, показывающее, была ли отменена асинхронная операция.</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>Возвращает значение, представляющее результат асинхронной операции.</summary>
      <returns>Объект <see cref="T:System.Object" />, представляющий результат асинхронной операции.</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">Значение <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> не null.Свойство <see cref="P:System.Exception.InnerException" /> содержит ссылку на ошибку <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" />.</exception>
      <exception cref="T:System.InvalidOperationException">Параметр <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> имеет значение true.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>Возвращает значение, представляющее состояние пользователя.</summary>
      <returns>Объект <see cref="T:System.Object" />, представляющий состояние пользователя.</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>Представляет метод, c помощью которого будет выполняться обработка события <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> класса <see cref="T:System.ComponentModel.BackgroundWorker" />.</summary>
      <param name="sender">Источник события. </param>
      <param name="e">Объект <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />, содержащий данные события. </param>
    </member>
  </members>
</doc>