﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="UploadBatchData.ascx.cs" Inherits="SplendidCRM.Administration.Batch.UploadBatchData" %>

<asp:Panel ID="Panel1" runat="server">
<p style="font-size:20px">Upload batch data</p>
    Select file: &nbsp;&nbsp
    <asp:FileUpload ID="fileIMPORT" runat="server" />&nbsp;
    <asp:Button ID="btnUpload" runat="server" Text="Upload"  onclick="btnUpload_Click" /><br />
    <asp:Button ID="Button1" runat="server" Text="Execute"  onclick="Button1_Click" />
    <asp:Button ID="Cancel" runat="server" Text="Cancel" onclick="Cancel_Click"  />
    <asp:Label ID="lblError" runat="server" CssClass="error" EnableViewState="false"></asp:Label>  
    <asp:Panel ID="Panel2" runat="server" ScrollBars="Auto" Height="400px" 
        Width="600px">
        <asp:GridView ID="GridView1" runat="server" />
    </asp:Panel>
</asp:Panel>
