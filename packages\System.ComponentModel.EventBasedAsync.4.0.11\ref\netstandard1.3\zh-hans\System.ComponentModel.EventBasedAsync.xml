﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.EventBasedAsync</name>
  </assembly>
  <members>
    <member name="T:System.ComponentModel.AsyncCompletedEventArgs">
      <summary>为 MethodNameCompleted 事件提供数据。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.#ctor(System.Exception,System.Boolean,System.Object)">
      <summary>初始化 <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" /> 类的新实例。</summary>
      <param name="error">在异步操作期间发生的任何错误。</param>
      <param name="cancelled">一个指示异步操作是否已被取消的值。</param>
      <param name="userState">传递给 <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> 方法的、用户提供的可选状态对象。</param>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled">
      <summary>获取一个值，该值指示异步操作是否已被取消。</summary>
      <returns>如果后台操作已被取消，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.Error">
      <summary>获取一个值，该值指示异步操作期间发生的错误。</summary>
      <returns>如果异步操作期间发生错误，则为 <see cref="T:System.Exception" /> 实例；否则为 null。</returns>
    </member>
    <member name="M:System.ComponentModel.AsyncCompletedEventArgs.RaiseExceptionIfNecessary">
      <summary>如果异步操作失败，则引发用户提供的异常。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> 属性为 true。</exception>
      <exception cref="T:System.Reflection.TargetInvocationException">该 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 属性已由异步操作设置。<see cref="P:System.Exception.InnerException" /> 属性持有对 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 的引用。</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncCompletedEventArgs.UserState">
      <summary>获取异步任务的唯一标识符。</summary>
      <returns>唯一标识异步任务的对象引用；如果未设置任何值，则为 null。</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncCompletedEventHandler">
      <summary>表示将要处理异步操作的 MethodNameCompleted 事件的方法。</summary>
      <param name="sender">事件源。</param>
      <param name="e">一个 <see cref="T:System.ComponentModel.AsyncCompletedEventArgs" />，其中包含事件数据。</param>
    </member>
    <member name="T:System.ComponentModel.AsyncOperation">
      <summary>跟踪异步操作的生存期。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Finalize">
      <summary>完成异步操作。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.OperationCompleted">
      <summary>结束异步操作的生存期。</summary>
      <exception cref="T:System.InvalidOperationException">此前便已为此任务调用了 <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" />。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.Post(System.Threading.SendOrPostCallback,System.Object)">
      <summary>在适合于应用程序模型的线程或上下文中调用委托。</summary>
      <param name="d">一个用于包装操作结束时要调用的委托的 <see cref="T:System.Threading.SendOrPostCallback" /> 对象。</param>
      <param name="arg">
        <paramref name="d" /> 参数中包含的委托的一个参数。</param>
      <exception cref="T:System.InvalidOperationException">此前便已为此任务调用了 <see cref="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)" /> 方法。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 为 null。</exception>
    </member>
    <member name="M:System.ComponentModel.AsyncOperation.PostOperationCompleted(System.Threading.SendOrPostCallback,System.Object)">
      <summary>结束异步操作的生存期。</summary>
      <param name="d">一个用于包装操作结束时要调用的委托的 <see cref="T:System.Threading.SendOrPostCallback" /> 对象。</param>
      <param name="arg">
        <paramref name="d" /> 参数中包含的委托的一个参数。</param>
      <exception cref="T:System.InvalidOperationException">此前便已为此任务调用了 <see cref="M:System.ComponentModel.AsyncOperation.OperationCompleted" />。</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="d" /> 为 null。</exception>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.SynchronizationContext">
      <summary>获取传递给构造函数的 <see cref="T:System.Threading.SynchronizationContext" /> 对象。</summary>
      <returns>传递给构造函数的 <see cref="T:System.Threading.SynchronizationContext" /> 对象。</returns>
    </member>
    <member name="P:System.ComponentModel.AsyncOperation.UserSuppliedState">
      <summary>获取或设置用于唯一标识异步操作的对象。</summary>
      <returns>传递给异步方法调用的状态对象。</returns>
    </member>
    <member name="T:System.ComponentModel.AsyncOperationManager">
      <summary>为支持异步方法调用的类提供并发管理。此类不能被继承。</summary>
    </member>
    <member name="M:System.ComponentModel.AsyncOperationManager.CreateOperation(System.Object)">
      <summary>返回可用于对特定异步操作的持续时间进行跟踪的 <see cref="T:System.ComponentModel.AsyncOperation" />。</summary>
      <returns>可用于对异步方法调用的持续时间进行跟踪的 <see cref="T:System.ComponentModel.AsyncOperation" />。</returns>
      <param name="userSuppliedState">一个对象，用于使一个客户端状态（如任务 ID）与一个特定异步操作相关联。</param>
    </member>
    <member name="P:System.ComponentModel.AsyncOperationManager.SynchronizationContext">
      <summary>获取或设置用于异步操作的同步上下文。</summary>
      <returns>用于异步操作的同步上下文。</returns>
    </member>
    <member name="T:System.ComponentModel.BackgroundWorker">
      <summary>在单独的线程上执行操作。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.#ctor">
      <summary>初始化 <see cref="T:System.ComponentModel.BackgroundWorker" /> 类的新实例。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.CancelAsync">
      <summary>请求取消挂起的后台操作。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation" /> 为 false。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.CancellationPending">
      <summary>获取一个值，指示应用程序是否已请求取消后台操作。</summary>
      <returns>如果应用程序已请求取消后台操作，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose"></member>
    <member name="M:System.ComponentModel.BackgroundWorker.Dispose(System.Boolean)"></member>
    <member name="E:System.ComponentModel.BackgroundWorker.DoWork">
      <summary>调用 <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync" /> 时发生。</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.IsBusy">
      <summary>获取一个值，指示 <see cref="T:System.ComponentModel.BackgroundWorker" /> 是否正在运行异步操作。</summary>
      <returns>如果 <see cref="T:System.ComponentModel.BackgroundWorker" /> 正在运行异步操作，则为 true；否则为 false。</returns>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnDoWork(System.ComponentModel.DoWorkEventArgs)">
      <summary>引发 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件。</summary>
      <param name="e">一个 <see cref="T:System.EventArgs" />，其中包含事件数据。</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnProgressChanged(System.ComponentModel.ProgressChangedEventArgs)">
      <summary>引发 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件。</summary>
      <param name="e">一个 <see cref="T:System.EventArgs" />，其中包含事件数据。</param>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.OnRunWorkerCompleted(System.ComponentModel.RunWorkerCompletedEventArgs)">
      <summary>引发 <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> 事件。</summary>
      <param name="e">一个 <see cref="T:System.EventArgs" />，其中包含事件数据。</param>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.ProgressChanged">
      <summary>调用 <see cref="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)" /> 时发生。</summary>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32)">
      <summary>引发 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件。</summary>
      <param name="percentProgress">已完成的后台操作所占的百分比，范围从 0% 到 100%。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> 属性设置为 false。</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.ReportProgress(System.Int32,System.Object)">
      <summary>引发 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件。</summary>
      <param name="percentProgress">已完成的后台操作所占的百分比，范围从 0% 到 100%。</param>
      <param name="userState">传递到 <see cref="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)" /> 的状态对象。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress" /> 属性设置为 false。</exception>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync">
      <summary>开始执行后台操作。</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> 为 true。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.ComponentModel.BackgroundWorker.RunWorkerAsync(System.Object)">
      <summary>开始执行后台操作。</summary>
      <param name="argument">要在 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件处理程序中执行的后台操作使用的参数。</param>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.BackgroundWorker.IsBusy" /> 为 true。</exception>
    </member>
    <member name="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted">
      <summary>当后台操作已完成、被取消或引发异常时发生。</summary>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerReportsProgress">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.ComponentModel.BackgroundWorker" /> 能否报告进度更新。</summary>
      <returns>如果 <see cref="T:System.ComponentModel.BackgroundWorker" /> 支持进度更新，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="P:System.ComponentModel.BackgroundWorker.WorkerSupportsCancellation">
      <summary>获取或设置一个值，该值指示 <see cref="T:System.ComponentModel.BackgroundWorker" /> 是否支持异步取消。</summary>
      <returns>如果 <see cref="T:System.ComponentModel.BackgroundWorker" /> 支持取消，则为 true；否则为 false。默认值为 false。</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventArgs">
      <summary>为 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件处理程序提供数据。</summary>
    </member>
    <member name="M:System.ComponentModel.DoWorkEventArgs.#ctor(System.Object)">
      <summary>初始化 <see cref="T:System.ComponentModel.DoWorkEventArgs" /> 类的新实例。</summary>
      <param name="argument">指定异步操作的参数。</param>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Argument">
      <summary>获取表示异步操作参数的值。</summary>
      <returns>表示异步操作参数的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Cancel"></member>
    <member name="P:System.ComponentModel.DoWorkEventArgs.Result">
      <summary>获取或设置表示异步操作结果的值。</summary>
      <returns>表示异步操作结果的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.DoWorkEventHandler">
      <summary>表示将处理 <see cref="E:System.ComponentModel.BackgroundWorker.DoWork" /> 事件的方法。此类不能被继承。</summary>
      <param name="sender">事件源。</param>
      <param name="e">包含事件数据的 <see cref="T:System.ComponentModel.DoWorkEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventArgs">
      <summary>为 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件提供数据。</summary>
    </member>
    <member name="M:System.ComponentModel.ProgressChangedEventArgs.#ctor(System.Int32,System.Object)">
      <summary>初始化 <see cref="T:System.ComponentModel.ProgressChangedEventArgs" /> 类的新实例。</summary>
      <param name="progressPercentage">已完成的异步任务的百分比。</param>
      <param name="userState">唯一的用户状态。</param>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.ProgressPercentage">
      <summary>获取异步任务的进度百分比。</summary>
      <returns>指示异步任务进度的百分比值。</returns>
    </member>
    <member name="P:System.ComponentModel.ProgressChangedEventArgs.UserState">
      <summary>获取唯一的用户状态。</summary>
      <returns>指示用户状态的唯一 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.ProgressChangedEventHandler">
      <summary>表示将处理 <see cref="T:System.ComponentModel.BackgroundWorker" /> 类的 <see cref="E:System.ComponentModel.BackgroundWorker.ProgressChanged" /> 事件的方法。此类不能被继承。</summary>
      <param name="sender">事件源。</param>
      <param name="e">包含事件数据的 <see cref="T:System.ComponentModel.ProgressChangedEventArgs" />。</param>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventArgs">
      <summary>为 MethodNameCompleted 事件提供数据。</summary>
    </member>
    <member name="M:System.ComponentModel.RunWorkerCompletedEventArgs.#ctor(System.Object,System.Exception,System.Boolean)">
      <summary>初始化 <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" /> 类的新实例。</summary>
      <param name="result">异步操作的结果。</param>
      <param name="error">在异步操作期间发生的任何错误。</param>
      <param name="cancelled">一个指示异步操作是否已被取消的值。</param>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.Result">
      <summary>获取表示异步操作结果的值。</summary>
      <returns>表示异步操作结果的 <see cref="T:System.Object" />。</returns>
      <exception cref="T:System.Reflection.TargetInvocationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 不是 null。<see cref="P:System.Exception.InnerException" /> 属性持有对 <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Error" /> 的引用。</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.ComponentModel.AsyncCompletedEventArgs.Cancelled" /> 为 true。</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.ComponentModel.RunWorkerCompletedEventArgs.UserState">
      <summary>获取表示用户状态的值。</summary>
      <returns>表示用户状态的 <see cref="T:System.Object" />。</returns>
    </member>
    <member name="T:System.ComponentModel.RunWorkerCompletedEventHandler">
      <summary>表示将处理 <see cref="T:System.ComponentModel.BackgroundWorker" /> 类的 <see cref="E:System.ComponentModel.BackgroundWorker.RunWorkerCompleted" /> 事件的方法。</summary>
      <param name="sender">事件源。</param>
      <param name="e">包含事件数据的 <see cref="T:System.ComponentModel.RunWorkerCompletedEventArgs" />。</param>
    </member>
  </members>
</doc>