<%@ Control Language="c#" AutoEventWireup="false" CodeBehind="EditView.ascx.cs" Inherits="SplendidCRM.Administration.ACLRoles.EditView"
    TargetSchema="http://schemas.microsoft.com/intellisense/ie5" %>
<%
/**********************************************************************************************************************
 * The contents of this file are subject to the SugarCRM Public License Version 1.1.3 ("License"); You may not use this
 * file except in compliance with the License. You may obtain a copy of the License at http://www.sugarcrm.com/SPL
 * Software distributed under the License is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, either
 * express or implied.  See the License for the specific language governing rights and limitations under the License.
 *
 * All copies of the Covered Code must include on each user interface screen:
 *    (i) the "Powered by SugarCRM" logo and
 *    (ii) the SugarCRM copyright notice
 *    (iii) the SplendidCRM copyright notice
 * in the same form as they appear in the distribution.  See full license for requirements.
 *
 * The Original Code is: SplendidCRM Open Source
 * The Initial Developer of the Original Code is SplendidCRM Software, Inc.
 * Portions created by SplendidCRM Software are Copyright (C) 2005-2008 SplendidCRM Software, Inc. All Rights Reserved.
 * Contributor(s): ______________________________________.
 *********************************************************************************************************************/
%>
<div id="divEditView">
    <%@ register tagprefix="SplendidCRM" tagname="ModuleHeader" src="~/_controls/ModuleHeader.ascx" %>
    <SplendidCRM:ModuleHeader ID="ctlModuleHeader" Module="Roles" EnablePrint="false"
        HelpName="EditView" EnableHelp="true" runat="Server" />
    <%@ register tagprefix="SplendidCRM" tagname="DynamicButtons" src="~/_controls/DynamicButtons.ascx" %>
    <SplendidCRM:DynamicButtons ID="ctlDynamicButtons" Visible="<%# !PrintView %>" ShowRequired="true"
        runat="Server" />
    <asp:Table SkinID="tabForm" runat="server">
        <asp:TableRow>
            <asp:TableCell>
                <div class="bg5">
                    <table id="tblMain" class="tabEditView" runat="server">
                    </table>
                </div>
            </asp:TableCell>
        </asp:TableRow>
    </asp:Table>
    <div class="m_t1">
        <b>
            <%= L10n.Term("ACLRoles.LBL_EDIT_VIEW_DIRECTIONS") %></b>
        <%@ register tagprefix="SplendidCRM" tagname="AccessView" src="AccessView.ascx" %>
        <SplendidCRM:AccessView ID="ctlAccessView" EnableACLEditing="true" runat="Server" />
    </div>
</div>
