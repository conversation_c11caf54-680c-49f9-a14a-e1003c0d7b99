﻿
using System;

namespace SplendidCRM.UtilityBillPay.Config
{
    public class Edit : SplendidPage
    {
        private void Page_Load(object sender, System.EventArgs e)
        {
            if (!IsPostBack)
            {

            }
        }

        #region Web Form Designer generated code
        override protected void OnInit(EventArgs e)
        {
            InitializeComponent();
            base.OnInit(e);
        }

        private void InitializeComponent()
        {
            this.Load += new System.EventHandler(this.Page_Load);
        }
        #endregion
    }
}







                        
        
        