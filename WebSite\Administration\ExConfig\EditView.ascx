﻿<%@ Control Language="c#" AutoEventWireup="false" CodeBehind="EditView.ascx.cs" Inherits="SplendidCRM.UtilityBillPay.Config.EditView" TargetSchema="http://schemas.microsoft.com/intellisense/ie5" %>

<div id="divEditView" class="Requisition">

    <div class="Title"><span>Config Create/Edit</span></div>

    <div class="PageSubmitButton">
        <input type="button" class="button" value="Save" onclick="saveForm()" />&nbsp;&nbsp;<input type="button" class="button" value="Cancel" onclick="cancel()" />
    </div>

    <div class="bg1">
        <table class="Table1" id="createDiv">
            <tr>
                <td width="15%" class="left">
                    <label class="control-label">Category :<font class="red"> *</font></label>
                </td>
                <td width="50%">
                    <input type="text" id="CATEGORY" name="CATEGORY" class="CommonWidth form-control dlg-input" col="CATEGORY">
                </td>
           
                <td width="15%" class="left">
                    <label class="control-label">Name :<font class="red"> *</font></label>
                </td>
                <td width="50%">
                    <input type="text" id="NAME" name="NAME" class="CommonWidth form-control dlg-input" col="NAME">
                </td>
           
                <td width="15%" class="left">
                    <label class="control-label">Value :<font class="red"> *</font></label>
                </td>
                <td width="50%">
                    <input type="text" id="VALUE" name="VALUE" class="CommonWidth form-control dlg-input" col="VALUE">
                </td>
            </tr>
        </table>
    </div>

</div>

<script type="text/javascript">
    document.write("<script src='<%= Application["rootURL"]%>UtilityBillPay/js/ConfigEdit.js?" + Math.random() + "'><\/script>");
</script>












