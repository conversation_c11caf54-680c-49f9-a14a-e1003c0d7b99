﻿<%@ Page Language="c#" MasterPageFile="~/DefaultView.Master" CodeBehind="DictType.aspx.cs" AutoEventWireup="false" Inherits="SplendidCRM.UtilityBillPay.ExConfig.DictType" %>

<asp:Content ID="cntSidebar" ContentPlaceHolderID="cntSidebar" runat="server">
    <%@ Register TagPrefix="SplendidCRM" TagName="Shortcuts" Src="~/_controls/Shortcuts.ascx" %>
    <SplendidCRM:Shortcuts ID="ctlShortcuts" SubMenu="Users" runat="Server" />
</asp:Content>

<asp:Content ID="cntBody" ContentPlaceHolderID="cntBody" runat="server">
    <script type="text/javascript">
        function PasswordPopup() {
            location.href = "../Password.aspx";
        }
    </script>
    <%@ Register TagPrefix="SplendidCRM" TagName="ListView" Src="ListView.ascx" %>
    <SplendidCRM:ListView ID="ctlListView" Visible="true" runat="Server" />
    <asp:Label ID="lblAccessError" ForeColor="Red" EnableViewState="false" Text='<%# L10n.Term("ACL.LBL_NO_ACCESS") %>' Visible="<%# !ctlListView.Visible %>" runat="server" />
</asp:Content>








                        
        
        